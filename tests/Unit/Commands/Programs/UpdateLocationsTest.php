<?php

namespace Tests\Unit\Commands\Programs;

use App\Console\Commands\Programs\UpdateLocations;
use App\Models\Audio\Podcast;
use App\Models\Audio\Thematic;
use App\Models\Map\Place;
use App\Models\Radio\Program;
use App\Models\Teams\Team;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class UpdateLocationsTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_update_locations_in_podcasts(): void
    {
        Thematic::factory()->create();
        Team::factory()->editor()->create();
        /** @var \App\Models\Map\Place $place1 */
        $place1 = Place::factory()->withPoint()->create();
        /** @var \App\Models\Map\Place $place2 */
        $place2 = Place::factory()->withPoint()->create();
        /** @var \App\Models\Radio\Program $program1 */
        $program1 = Program::factory()->withPlace($place1)->create();
        /** @var \App\Models\Radio\Program $program2 */
        $program2 = Program::factory()->withPlace($place2)->create();
        /** @var \App\Models\Audio\Podcast $podcast1 */
        $podcast1 = Podcast::factory()->withMedia()->withProgram($program1)->create();
        /** @var \App\Models\Audio\Podcast $podcast2 */
        $podcast2 = Podcast::factory()->withMedia()->withProgram($program1)->create();
        /** @var \App\Models\Audio\Podcast $podcast3 */
        $podcast3 = Podcast::factory()->withMedia()->withProgram($program2)->create();

        $this->assertDatabaseMissing('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast1->id,
        ]);
        $this->assertDatabaseMissing('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast2->id,
        ]);
        $this->assertDatabaseMissing('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast3->id,
        ]);

        $mock = $this->mock(ElasticsearchService::class);
        $mock->shouldReceive('findByField')->times(3);
        $mock->shouldReceive('indexDocument')->times(3);

        $this->artisan(UpdateLocations::class)->assertSuccessful();

        $this->assertDatabaseHas('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast1->id,
            'location_type' => Place::class,
            'location_id' => $place1->id,
        ]);
        $this->assertDatabaseHas('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast2->id,
            'location_type' => Place::class,
            'location_id' => $place1->id,
        ]);
        $this->assertDatabaseHas('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast3->id,
            'location_type' => Place::class,
            'location_id' => $place2->id,
        ]);
    }

    /** @test */
    public function it_cant_update_locations_in_podcasts_already_have_location(): void
    {
        Thematic::factory()->create();
        Team::factory()->editor()->create();
        /** @var \App\Models\Map\Place $place1 */
        $place1 = Place::factory()->withPoint()->create();
        /** @var \App\Models\Map\Place $place2 */
        $place2 = Place::factory()->withPoint()->create();
        /** @var \App\Models\Radio\Program $program */
        $program = Program::factory()->withPlace($place1)->create();
        /** @var \App\Models\Audio\Podcast $podcast1 */
        $podcast1 = Podcast::factory()->withMedia()->withProgram($program)->create();
        /** @var \App\Models\Audio\Podcast $podcast2 */
        $podcast2 = Podcast::factory()->withMedia()->withProgram($program)->withPlace($place2)->create();

        $this->assertDatabaseMissing('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast1->id,
        ]);
        $this->assertDatabaseHas('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast2->id,
            'location_type' => Place::class,
            'location_id' => $place2->id,
        ]);

        $mock = $this->mock(ElasticsearchService::class);
        $mock->shouldReceive('findByField')->once();
        $mock->shouldReceive('indexDocument')->once();

        $this->artisan(UpdateLocations::class)->assertSuccessful();

        $this->assertDatabaseHas('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast1->id,
            'location_type' => Place::class,
            'location_id' => $place1->id,
        ]);
        $this->assertDatabaseHas('content_location', [
            'content_type' => Podcast::class,
            'content_id' => $podcast2->id,
            'location_type' => Place::class,
            'location_id' => $place2->id,
        ]);
    }
}

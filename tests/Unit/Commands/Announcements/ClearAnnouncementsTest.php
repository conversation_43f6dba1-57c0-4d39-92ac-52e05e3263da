<?php

namespace Tests\Unit\Commands\Announcements;

use App\Console\Commands\Announcements\ClearAnnouncements;
use App\Http\Middleware\ShareJavascriptToView;
use App\Models\Announcements\Announcement;
use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Audio\Thematic;
use App\Models\Events\Event;
use App\Models\Map\Point;
use App\Models\News\NewsArticle;
use App\Models\Radio\Program;
use App\Models\Settings\Settings;
use App\Models\Teams\Team;
use Illuminate\Auth\Middleware\RequirePassword;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Date;
use Tests\TestCase;

class ClearAnnouncementsTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMix();
        $this->withoutMiddleware([RequirePassword::class, ShareJavascriptToView::class]);

        Settings::factory()->withMedia()->create();
        Team::factory()->admin()->create();
        Thematic::factory()->create();
        Program::factory()->withMedia()->create();
        Podcast::factory()->withMedia()->create();
        Event::factory()->withMedia()->create();
        NewsArticle::factory()->withMedia()->create();
        Playlist::factory()->withMedia()->create();
        Point::factory()->create();
    }

    /** @test */
    public function it_can_deleted_passed_announcements(): void
    {
        $now = Date::now();
        Date::setTestNow($now);
        /** @var \App\Models\Announcements\Announcement $announcement1 */
        $announcement1 = Announcement::factory([
            'published_at' => $now->addDays(-2),
            'unpublished_at' => $now->addDays(1),
        ])->create();
        /** @var \App\Models\Announcements\Announcement $announcement2 */
        $announcement2 = Announcement::factory([
            'published_at' => $now->addDays(-2),
            'unpublished_at' => $now->addDays(-1),
        ])->create();
        /** @var \App\Models\Announcements\Announcement $announcement3 */
        $announcement3 = Announcement::factory([
            'published_at' => $now->addDays(-2),
            'unpublished_at' => null,
            'active' => false,
        ])->create();
        /** @var \App\Models\Announcements\Announcement $announcement4 */
        $announcement4 = Announcement::factory([
            'published_at' => $now->addDays(1),
            'unpublished_at' => null,
            'active' => false,
        ])->create();
        $this->artisan(ClearAnnouncements::class)->assertSuccessful();
        $this->assertDatabaseHas(app(Announcement::class)->getTable(), [
            'id' => $announcement1->id,
            'deleted_at' => null,
        ]);
        $this->assertDatabaseHas(app(Announcement::class)->getTable(), [
            'id' => $announcement2->id,
            'deleted_at' => $now,
        ]);
        $this->assertDatabaseHas(app(Announcement::class)->getTable(), [
            'id' => $announcement3->id,
            'deleted_at' => null,
        ]);
        $this->assertDatabaseHas(app(Announcement::class)->getTable(), [
            'id' => $announcement4->id,
            'deleted_at' => null,
        ]);
    }
}

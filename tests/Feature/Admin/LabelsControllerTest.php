<?php

namespace Tests\Feature\Admin;

use App\Http\Livewire\Tables\LabelsTable;
use App\Http\Middleware\ShareJavascriptToView;
use App\Models\Audio\Song;
use App\Models\Audio\Thematic;
use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Performers\Album;
use App\Models\Performers\Label;
use App\Models\Performers\Performer;
use App\Models\Settings\Settings;
use App\Models\Teams\Team;
use App\Models\Users\User;
use App\Services\Elasticsearch\SongsIndexService;
use Illuminate\Auth\Middleware\RequirePassword;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Livewire\Livewire;
use Mockery;
use Tests\TestCase;

class LabelsControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->withoutMix();
        $this->withoutMiddleware([RequirePassword::class, ShareJavascriptToView::class]);
    }

    /** @test */
    public function it_can_display_label_list(): void
    {
        Settings::factory()->withMedia()->create();
        Team::factory()->admin()->create();
        /** @var \App\Models\Users\User $authUser */
        $authUser = User::factory()->admin()->withMedia()->create();
        /** @var \App\Models\Performers\Label $label1 */
        $label1 = Label::factory()->create();
        /** @var \App\Models\Performers\Performer $performer1 */
        $performer1 = Performer::factory()->withMedia()->create();
        /** @var \App\Models\Performers\Album $album1 */
        $album1 = Album::factory()->withMedia()->withPerformer($performer1)->withLabel($label1)->create();
        Song::factory()->withMedia()->withPerformer($performer1)->withAlbum($album1)->withLabel($label1)->count(2)->create();
        /** @var \App\Models\Performers\Label $label2 */
        $label2 = Label::factory()->withPlace()->create();
        /** @var \App\Models\Performers\Performer $performer2 */
        $performer2 = Performer::factory()->withMedia()->create();
        /** @var \App\Models\Performers\Album $album2 */
        $album2 = Album::factory()->withMedia()->withPerformer($performer2)->withLabel($label2)->create();
        Song::factory()->withMedia()->withPerformer($performer2)->withAlbum($album2)->withLabel($label2)->count(1)->create();
        $this->actingAs($authUser)
            ->get(route('labels.index'))
            ->assertOk();
        Livewire::test(LabelsTable::class)
            ->assertSeeInOrder([
                $label2->id,
                e(Str::limit($label2->name, 25)),
                'text-success', // test BooleanColumn Location
                '1 titre',
                '1 album',
                '1 artiste',
                $label2->created_at->timezone('Europe/Paris')->format('d/m/Y H:i'),
                $label2->updated_at->timezone('Europe/Paris')->format('d/m/Y H:i'),
                $label1->id,
                e(Str::limit($label1->name, 25)),
                'text-danger', // test BooleanColumn Location
                '2 titre',
                '1 album',
                '1 artiste',
                $label1->created_at->timezone('Europe/Paris')->format('d/m/Y H:i'),
                $label1->updated_at->timezone('Europe/Paris')->format('d/m/Y H:i'),
            ]);
    }

    /** @test */
    public function it_can_display_label_edit_page(): void
    {
        Settings::factory()->withMedia()->create();
        Team::factory()->admin()->create();
        /** @var \App\Models\Users\User $authUser */
        $authUser = User::factory()->admin()->create();
        Thematic::factory()->create();
        /** @var \App\Models\Performers\Label $label */
        $label = Label::factory()->create();
        $this->actingAs($authUser)->get(route('label.edit', $label))
            ->assertOk()
            ->assertSeeInOrder([
                // Heading
                e(__('breadcrumbs.orphan.edit', [
                    'entity' => 'Label',
                    'detail' => $label->name,
                ])),
                // Form and actions
                'method="POST"',
                'action="' . route('label.update', $label) . '"',
                'novalidate>',
                csrf_field(),
                method_field('PUT'),
                'href="' . route('labels.index'),
                'Retour',
                'Enregistrer',
                'name="name"',
                'name="place[place_id]"',
            ], false);
    }

    /** @test */
    public function it_can_update_label(): void
    {
        Settings::factory()->withMedia()->create();
        Team::factory()->admin()->create();
        /** @var \App\Models\Users\User $authUser */
        $authUser = User::factory()->admin()->create();
        /** @var \App\Models\Performers\Label $label */
        $label = Label::factory()->create();
        $this->actingAs($authUser)
            ->from(route('label.edit', $label))
            ->put(route('label.update', $label), [
                'name' => $label->name,
                'place' => ['place_id' => ''],
            ])
            ->assertSessionHasNoErrors()
            ->assertSessionHas('toast_success', __('crud.orphan.updated', [
                'entity' => 'Label',
                'name' => $label->name,
            ]))
            ->assertRedirect(route('label.edit', $label));
        // Label still in database
        $this->assertDatabaseHas(app(Label::class)->getTable(), [
            'id' => $label->id,
            'name' => $label->name,
        ]);
    }

    /** @test */
    public function it_can_update_label_name_and_song_publisher(): void
    {
        Settings::factory()->withMedia()->create();
        Team::factory()->admin()->create();
        /** @var \App\Models\Users\User $authUser */
        $authUser = User::factory()->admin()->create();
        /** @var \App\Models\Performers\Label $label */
        $label = Label::factory()->create();
        /** @var \App\Models\Audio\Song $song */
        $song = Song::factory()->withMedia()->withLabel($label)->create();
        $this->mock(SongsIndexService::class)
            ->shouldReceive('updateOrCreate')
            ->once()
            ->with(Mockery::type(Song::class));
        $this->actingAs($authUser)
            ->from(route('label.edit', $label))
            ->put(route('label.update', $label), [
                'name' => 'new name',
                'place' => ['place_id' => ''],
            ])
            ->assertSessionHasNoErrors()
            ->assertSessionHas('toast_success', __('crud.orphan.updated', [
                'entity' => 'Label',
                'name' => 'new name',
            ]))
            ->assertRedirect(route('label.edit', $label));
        // Label still in database
        $this->assertDatabaseHas(app(Label::class)->getTable(), [
            'id' => $label->id,
            'name' => 'new name',
        ]);
        // Song publisher name updated
        $this->assertDatabaseHas(app(Song::class)->getTable(), [
            'id' => $song->id,
            'publisher' => 'new name',
        ]);
    }

    /** @test */
    public function it_can_update_label_with_place(): void
    {
        Settings::factory()->withMedia()->create();
        Team::factory()->admin()->create();
        /** @var \App\Models\Users\User $authUser */
        $authUser = User::factory()->admin()->create();
        /** @var \App\Models\Performers\Label $label */
        $label = Label::factory()->create();
        /** @var Place $place */
        $place = Place::factory()->create();
        $this->actingAs($authUser)
            ->from(route('label.edit', $label))
            ->put(route('label.update', $label), [
                'name' => $label->name,
                'place' => ['place_id' => $place->id],
            ])
            ->assertSessionHasNoErrors()
            ->assertSessionHas('toast_success', __('crud.orphan.updated', [
                'entity' => 'Label',
                'name' => $label->name,
            ]))
            ->assertRedirect(route('label.edit', $label));
        // Label still in database
        $this->assertDatabaseHas(app(Label::class)->getTable(), [
            'id' => $label->id,
            'name' => $label->name,
        ]);
        // New association between label and place.
        $this->assertDatabaseHas(ContentLocation::class, [
            'content_type' => Label::class,
            'content_id' => $label->id,
            'location_type' => Place::class,
            'location_id' => $place->id,
        ]);
    }

    /** @test */
    public function it_cant_create_and_delete_labels(): void
    {
        $this->assertFalse(Route::has('label.create'));
        $this->assertFalse(Route::has('label.store'));
        $this->assertFalse(Route::has('label.destroy'));
    }

    /** @test */
    public function it_can_apply_access_restrictions_to_labels_for_employees(): void
    {
        Settings::factory()->withMedia()->create();
        Team::factory()->employee()->create();
        /** @var \App\Models\Users\User $authUser */
        $authUser = User::factory()->employee()->withMedia()->create();
        /** @var \App\Models\Performers\Label $label */
        $label = Label::factory()->create();
        $this->actingAs($authUser)->get(route('dashboard.index'))->assertSeeInOrder([
            'href="' . route('labels.index') . '"',
            'title="Labels"',
            'Labels',
        ], false);
        $this->actingAs($authUser)->get(route('labels.index'))->assertOk();
        $this->actingAs($authUser)->get(route('label.edit', $label))->assertOk();
        $this->actingAs($authUser)->put(route('label.update', $label))->assertRedirect();
    }

    /** @test */
    public function it_can_apply_access_restrictions_to_labels_for_editors(): void
    {
        Settings::factory()->withMedia()->create();
        Team::factory()->editor()->create();
        /** @var \App\Models\Users\User $authUser */
        $authUser = User::factory()->editor()->withMedia()->create();
        /** @var \App\Models\Performers\Label $label */
        $label = Label::factory()->create();
        $this->actingAs($authUser)->get(route('dashboard.index'))->assertDontSee([
            'href="' . route('labels.index') . '"',
            'title="Labels"',
            'Labels',
        ], false);
        $this->actingAs($authUser)->get(route('labels.index'))->assertForbidden();
        $this->actingAs($authUser)->get(route('label.edit', $label))->assertForbidden();
        $this->actingAs($authUser)->put(route('label.update', $label))->assertForbidden();
    }

    /** @test */
    public function it_can_apply_access_restrictions_to_labels_for_volunteers(): void
    {
        Settings::factory()->withMedia()->create();
        Team::factory()->volunteer()->create();
        /** @var \App\Models\Users\User $authUser */
        $authUser = User::factory()->volunteer()->withMedia()->create();
        /** @var \App\Models\Performers\Label $label */
        $label = Label::factory()->create();
        $this->actingAs($authUser)->get(route('dashboard.index'))->assertDontSee([
            'href="' . route('labels.index') . '"',
            'title="Labels"',
            'Labels',
        ], false);
        $this->actingAs($authUser)->get(route('labels.index'))->assertForbidden();
        $this->actingAs($authUser)->get(route('label.edit', $label))->assertForbidden();
        $this->actingAs($authUser)->put(route('label.update', $label))->assertForbidden();
    }

    /** @test */
    public function it_can_apply_access_restrictions_to_labels_for_listeners(): void
    {
        Settings::factory()->withMedia()->create();
        Team::factory()->listener()->create();
        /** @var \App\Models\Users\User $authUser */
        $authUser = User::factory()->listener()->withMedia()->create();
        /** @var \App\Models\Performers\Label $label */
        $label = Label::factory()->create();
        $this->actingAs($authUser)->get(route('dashboard.index'))->assertDontSee([
            'href="' . route('labels.index') . '"',
            'title="Labels"',
            'Labels',
        ], false);
        $this->actingAs($authUser)->get(route('labels.index'))->assertForbidden();
        $this->actingAs($authUser)->get(route('label.edit', $label))->assertForbidden();
        $this->actingAs($authUser)->put(route('label.update', $label))->assertForbidden();
    }
}

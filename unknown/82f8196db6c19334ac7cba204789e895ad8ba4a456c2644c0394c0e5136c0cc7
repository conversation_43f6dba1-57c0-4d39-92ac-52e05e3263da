<?php

namespace App\Http\Requests\Contact;

use App\Models\Logs\LogContactFormMessage;
use App\Rules\PhoneInternational;
use App\Rules\ValidFilePondUpload;
use App\Rules\ValidMultipleFilePondUpload;
use Illuminate\Foundation\Http\FormRequest;

class ContactPageSendMessageRequest extends FormRequest
{
    public function rules(int $currentStep, string $desiredDestination, string $desiredTeam): array
    {
        $rulesStep1 = [
            'desired_destination' => ['required', 'string'],
            'last_name' => ['required', 'string', 'max:255'],
            'first_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email:rfc,dns,spoof'],
            'phone_number' => ['required_if:desired_destination,evenement', 'string', 'max:255', new PhoneInternational()],
            'message' => [
                'required_if:desired_destination,equipe',
                'required_if:desired_destination,reagir',
                'required_if:desired_destination,probleme',
                'string',
                'max:65535',
            ],
            'problem_type' => ['required_if:desired_destination,probleme', 'string', 'max:255'],
            'problem_device' => ['nullable', 'string', 'max:255'],
            'problem_device_name' => ['nullable', 'string', 'max:255'],
            'problem_allow_user_agent' => ['required', 'boolean'],
            'problem_localisation' => ['nullable', 'string', 'max:255'],
            'event_structure_name' => ['nullable', 'string', 'max:255'],
            'event_already_solicited' => ['required_if:desired_destination,evenement', 'string', 'max:255'],
            'track_person_type' => ['required_if:desired_destination,titre', 'string', 'max:255'],
            'track_structure_name' => ['nullable', 'string', 'max:255'],
            'attachments_general' => [
                'present',
                'array',
                new ValidMultipleFilepondUpload(
                    app(LogContactFormMessage::class)->getMediaCollection('attachments_general')->acceptsMimeTypes,
                    1024 * 1024 * 10
                ),
            ],
            'attachments_audio' => [
                'required_if:desired_destination,titre',
                'array',
                new ValidMultipleFilepondUpload(
                    app(LogContactFormMessage::class)->getMediaCollection('attachments_audio')->acceptsMimeTypes,
                    1024 * 1024 * 100
                ),
            ],
            'attachments_image' => [
                'present',
                'array',
                new ValidMultipleFilepondUpload(
                    app(LogContactFormMessage::class)->getMediaCollection('attachments_image')->acceptsMimeTypes,
                    1024 * 1024 * 10
                ),
            ],
            'attachments_general_end' => [
                'present',
                'array',
                new ValidMultipleFilepondUpload(
                    app(LogContactFormMessage::class)->getMediaCollection('attachments_general')->acceptsMimeTypes,
                    1024 * 1024 * 10
                ),
            ],
            'filepond_metadata' => ['present', 'array'],
            'audio_metadata' => ['present', 'array'],
            'desired_team' => ['required_if:desired_destination,equipe', 'string'],
        ];

        $addtionnalRules = [];
        if ($desiredDestination === 'equipe' && $desiredTeam === 'redaction') {
            $addtionnalRules['redaction_departement'] = [
                'required',
                'string',
            ];
        }
        if ($desiredDestination === 'equipe' && $desiredTeam === 'programmation') {
            $addtionnalRules['programmation_live_session'] = [
                'required',
                'string',
            ];
        }
        if ($desiredDestination === 'titre' || ($desiredDestination === 'equipe' && $desiredTeam === 'programmation')) {
            $addtionnalRules['desired_station'] = [
                'required',
                'string',
            ];
        }

        $rulesStep2 = [
            'event_stations' => ['nullable', 'string', 'max:255'],
            'event_date' => ['required_if:desired_destination,evenement', 'string', 'max:255'],
            'event_time_start' => ['nullable', 'string', 'max:255'],
            'event_time_end' => ['nullable', 'string', 'max:255'],
            'event_thematic' => ['required_if:desired_destination,evenement', 'string', 'max:255'],
            'event_type' => ['required_if:desired_destination,evenement', 'string', 'max:255'],
            'event_name' => ['required_if:desired_destination,evenement', 'string', 'max:255'],
            'event_description' => ['required_if:desired_destination,evenement', 'string'],
            'event_free' => ['required_if:desired_destination,evenement', 'string', 'max:255'],
            'event_address' => ['required_if:desired_destination,evenement', 'string'],
            'event_giveaway_tickets' => ['required_if:desired_destination,evenement', 'int', 'min:0', 'max:10'],
            'event_link' => ['nullable', 'string', 'url'],
            'event_buy_link' => ['nullable', 'string', 'url'],
            'event_audience_age' => ['required_if:desired_destination,evenement', 'string', 'max:255'],
            'audio_metadata.*.performer' => ['required', 'string', 'max:255'],
            'audio_metadata.*.title' => ['required', 'string', 'max:255'],
            'audio_metadata.*.album' => ['required', 'string', 'max:255'],
            'audio_metadata.*.genre' => ['required', 'string', 'max:255'],
            'audio_metadata.*.release_date' => ['required', 'string', 'max:255'],
            'audio_metadata.*.embargo' => ['nullable', 'string', 'max:255'],
            'audio_metadata.*.localisation_region' => ['required', 'string', 'max:255'],
            'audio_metadata.*.localisation_town' => ['nullable', 'string', 'max:255'],
            'audio_metadata.*.buy_link' => ['nullable', 'string', 'url'],
            'audio_metadata.*.programmation_live_session' => ['nullable', 'string', 'max:255'],
            'attachments_audio_cover.*' => [
                'nullable',
                'image',
                new ValidFilePondUpload(
                    app(LogContactFormMessage::class)->getMediaCollection('attachments_audio_cover')->acceptsMimeTypes,
                    1024 * 1024 * 40
                ),
            ],
            'attachments_audio_performer_picture.*' => [
                'nullable',
                'image',
                new ValidFilePondUpload(
                    app(LogContactFormMessage::class)->getMediaCollection('attachments_audio_performer_picture')->acceptsMimeTypes,
                    1024 * 1024 * 40
                ),
            ],
        ];

        if ($currentStep === 1) {
            return array_merge($rulesStep1, $addtionnalRules);
        } elseif ($currentStep === 2) {
            return array_merge($rulesStep1, $addtionnalRules, $rulesStep2);
        }

        return [];
    }

    public function messages()
    {
        return [
            'desired_station' => 'La sélection d\'une station est obligatoire.',
            'desired_team.required_if' => 'Le champ :attribute est obligatoire.',
            'attachments_audio.required_if' => 'Le champ :attribute est obligatoire.',
            'phone_number.required_if' => 'Le champ :attribute est obligatoire.',
            'message.required_if' => 'Le champ :attribute est obligatoire.',
            'event_already_solicited.required_if' => 'Le champ :attribute est obligatoire.',
            'event_date.required_if' => 'Le champ :attribute est obligatoire.',
            'event_thematic.required_if' => 'Le champ :attribute est obligatoire.',
            'event_type.required_if' => 'Le champ :attribute est obligatoire.',
            'event_name.required_if' => 'Le champ :attribute est obligatoire.',
            'event_description.required_if' => 'Le champ :attribute est obligatoire.',
            'event_free.required_if' => 'Le champ :attribute est obligatoire.',
            'event_address.required_if' => 'Le champ :attribute est obligatoire.',
            'event_audience_age.required_if' => 'Le champ :attribute est obligatoire.',
            'track_person_type.required_if' => 'Le champ :attribute est obligatoire.',
            'audio_metadata.*.performer' => 'Champ Artiste requis.',
            'audio_metadata.*.title' => 'Champ Titre requis.',
            'audio_metadata.*.album' => 'Champ Album requis.',
            'audio_metadata.*.genre' => 'Champ Genre requis.',
            'audio_metadata.*.release_date' => 'Champ Date de sortie requis.',
            'audio_metadata.*.localisation_region' => 'Champ Région du groupe requis.',
            'audio_metadata.*.buy_link' => 'Le champ doit être une URL valide.',
        ];
    }
}

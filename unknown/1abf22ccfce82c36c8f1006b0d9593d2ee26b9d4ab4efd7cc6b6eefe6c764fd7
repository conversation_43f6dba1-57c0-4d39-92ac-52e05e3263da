<?php

namespace App\Http\Livewire;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\Map\Place;
use App\Models\News\NewsArticle;
use App\Models\Performers\Album;
use App\Models\Performers\Performer;
use App\Models\Radio\Program;
use App\Services\Router\RouterService;
use App\Services\Users\UserJourneysService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use Livewire\Component;

class Router extends Component
{
    public const ROUTES = [
        [
            'key' => 'browse',
            'name' => 'app.browse.index',
            'url' => '/mysun/parcourir',
            'view' => 'templates.front.app.browse',
            'css' => '/css/templates/front/app/browse.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'map',
            'name' => 'app.map.index',
            'url' => '/mysun/se-balader',
            'view' => 'templates.front.app.map',
            'css' => '/css/templates/front/app/map.css',
            'desktopProfileNav' => false,
            'requiresAuth' => false,
        ],
        [
            'key' => 'dedicace',
            'name' => 'app.dedicaces.index',
            'url' => '/mysun/dedicaces',
            'view' => 'templates.front.app.dedicaces',
            'css' => '/css/templates/front/app/dedicaces.css',
            'requiresAuth' => true,
        ],
        [
            'key' => 'programs',
            'name' => 'app.programs.index',
            'url' => '/mysun/emissions',
            'view' => 'templates.front.app.programs',
            'css' => '/css/templates/front/app/programs.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'program_details',
            'name' => 'app.program.show',
            'url' => '/mysun/emission/{program}',
            'bindings' => ['program' => Program::class],
            'view' => 'templates.front.app.program-details',
            'css' => '/css/templates/front/app/program-details.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'events',
            'name' => 'app.events.index',
            'url' => '/mysun/agenda',
            'view' => 'templates.front.app.events',
            'css' => '/css/templates/front/app/events.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'event_details',
            'name' => 'app.event.show',
            'url' => '/mysun/evenement/{event}',
            'bindings' => ['event' => Event::class],
            'view' => 'templates.front.app.event-details',
            'css' => '/css/templates/front/app/event-details.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'search_results',
            'name' => 'app.search.results.index',
            'url' => '/mysun/rechercher',
            'view' => 'templates.front.app.search-results',
            'css' => '/css/templates/front/app/search-results.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'search_song_results',
            'name' => 'app.search.songs.results.show',
            'url' => '/mysun/rechercher/titres',
            'view' => 'templates.front.app.search-song-results',
            'css' => '/css/templates/front/app/search-song-results.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'search_podcast_results',
            'name' => 'app.search.podcast.results.show',
            'url' => '/mysun/rechercher/podcasts',
            'view' => 'templates.front.app.search-podcast-results',
            'css' => '/css/templates/front/app/search-podcast-results.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'search_news_results',
            'name' => 'app.search.news.results.show',
            'url' => '/mysun/rechercher/actualites',
            'view' => 'templates.front.app.search-news-results',
            'css' => '/css/templates/front/app/news.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'search_event_results',
            'name' => 'app.search.events.results.show',
            'url' => '/mysun/rechercher/evenements',
            'view' => 'templates.front.app.search-event-results',
            'css' => '/css/templates/front/app/search-event-results.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'search_playlist_results',
            'name' => 'app.search.playlist.results.show',
            'url' => '/mysun/rechercher/playlists',
            'view' => 'templates.front.app.search-playlist-results',
            'css' => '/css/templates/front/app/search-playlist-results.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'search_performer_results',
            'name' => 'app.search.performer.results.show',
            'url' => '/mysun/rechercher/artistes',
            'view' => 'templates.front.app.search-performer-results',
            'css' => '/css/templates/front/app/search-performer-results.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'search_album_results',
            'name' => 'app.search.album.results.show',
            'url' => '/mysun/rechercher/albums',
            'view' => 'templates.front.app.search-album-results',
            'css' => '/css/templates/front/app/search-album-results.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'podcasts',
            'name' => 'app.podcasts.index',
            'url' => '/mysun/podcasts',
            'view' => 'templates.front.app.podcasts',
            'css' => '/css/templates/front/app/podcasts.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'podcast_details',
            'name' => 'app.podcast.show',
            'url' => '/mysun/podcast/{podcast}',
            'bindings' => ['podcast' => Podcast::class],
            'view' => 'templates.front.app.podcast-details',
            'css' => '/css/templates/front/app/podcast-details.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'favorite_songs',
            'name' => 'app.songs.favorites',
            'url' => '/mysun/titres-aimes',
            'view' => 'templates.front.app.playlist-details',
            'css' => '/css/templates/front/app/playlist-details.css',
            'requiresAuth' => true,
        ],
        [
            'key' => 'playlists',
            'name' => 'app.playlists.index',
            'url' => '/mysun/playlists',
            'view' => 'templates.front.app.playlists',
            'css' => '/css/templates/front/app/playlists.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'playlist_details',
            'name' => 'app.playlist.show',
            'url' => '/mysun/playlist/{playlist}',
            'bindings' => ['playlist' => Playlist::class],
            'view' => 'templates.front.app.playlist-details',
            'css' => '/css/templates/front/app/playlist-details.css',
            'requiresAuth' => false,
            'authorize' => 'authorizePlaylistDetail',
        ],
        [
            'key' => 'news',
            'name' => 'app.news.index',
            'url' => '/mysun/actualites',
            'view' => 'templates.front.app.news',
            'css' => '/css/templates/front/app/news.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'news_details',
            'name' => 'app.news.show',
            'url' => '/mysun/actualite/{article}',
            'bindings' => ['article' => NewsArticle::class],
            'view' => 'templates.front.app.news-details',
            'css' => '/css/templates/front/app/news-details.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'songs',
            'name' => 'app.songs.index',
            'url' => '/mysun/songs',
            'view' => 'templates.front.app.songs',
            'css' => '/css/templates/front/app/songs.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'performer_details',
            'name' => 'app.performer.show',
            'url' => '/mysun/artiste/{performer}',
            'bindings' => ['performer' => Performer::class],
            'view' => 'templates.front.app.performer-details',
            'css' => '/css/templates/front/app/performer-details.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'album_details',
            'name' => 'app.album.show',
            'url' => '/mysun/album/{album}',
            'bindings' => ['album' => Album::class],
            'view' => 'templates.front.app.album-details',
            'css' => '/css/templates/front/app/album-details.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'place_details',
            'name' => 'app.place.show',
            'url' => '/mysun/lieu/{place}',
            'bindings' => ['place' => Place::class],
            'view' => 'templates.front.app.place-details',
            'css' => '/css/templates/front/app/place-details.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'previous_broadcast_songs',
            'name' => 'app.broadcast.songs.previous',
            'url' => '/mysun/diffusion/morceaux/precedents',
            'view' => 'templates.front.app.previous-broadcast-songs',
            'css' => '/css/templates/front/app/previous-broadcast-songs.css',
            'requiresAuth' => false,
        ],
        [
            'key' => 'profile_information',
            'name' => 'app.profile.information.show',
            'url' => '/mysun/profil/informations',
            'view' => 'templates.front.app.profile-information',
            'css' => '/css/templates/front/app/profile-information.css',
            'desktopProfileNav' => false,
            'requiresAuth' => true,
        ],
        [
            'key' => 'profile_params',
            'name' => 'app.profile.params.show',
            'url' => '/mysun/profil/parametres',
            'view' => 'templates.front.app.profile-params',
            'desktopProfileNav' => false,
            'requiresAuth' => true,
        ],
    ];

    public string $initialUrl;

    protected $listeners = [
        'nav:to' => 'navTo',
        'nav:back' => 'navBack',
        'nav:browser:route:updated' => 'navFromBrowserUrl',
    ];

    public function mount(): void
    {
        $this->initialUrl = urldecode(Request::getRequestUri());
    }

    public function navBack(): void
    {
        $this->emit('nav:browser:previous:request');
    }

    public function init(): void
    {
        $initialRoute = app(RouterService::class)->getInitialRoute($this->initialUrl);
        $routeKey = $this->passesAuthCheck($initialRoute)
            ? $initialRoute['key']
            : 'browse';
        $urlParams = app(RouterService::class)->getParamsFromUrl($this->initialUrl);
        $routeParams = array_merge(
            $urlParams ? ['url_params' => $urlParams] : [],
            $initialRoute['params'] ?? []
        );
        $loginModalDestination = null;
        if (! $this->passesAuthCheck($initialRoute) && $initialRoute['key'] === 'dedicace') {
            $loginModalDestination = 'dedicace';
        }
        $this->emitSelf('nav:to', $routeKey, $routeParams, false, $loginModalDestination);
    }

    protected function passesAuthCheck(array $route): bool
    {
        if ($route['requiresAuth']) {
            return Auth::check();
        }

        return true;
    }

    public function navTo(string $routeKey, array $params = [], bool $skipPushHistory = false, ?string $loginModalDestination = null): void
    {
        // Utile pour rediriger vers la carte depuis une URL (ex annonce de type map)
        if ($routeKey === 'map' && isset($params['url'])) {
            $this->emit('nav:browser:history:push', $params['url']);
            $this->navFromBrowserUrl($params['url'], $skipPushHistory);

            return;
        }

        $route = $this->getRoute($routeKey);
        if (! $this->passesAuthCheck($route)) {
            $route = $this->getRoute('browse');
        }
        if (! $skipPushHistory) {
            $this->pushUrlToBrowserHistory($route, $params);
        }
        $params = $this->mergeUrlParamsToParams($params);
        if (! $this->passesAuthorization($route, $params)) {
            $route = $this->getRoute('browse');
        }
        $this->emitTo('app', 'template:load', $route, $params);
        app(UserJourneysService::class)->setCurrentRouteKey($route['key']);
        $this->emit('nav:route:loaded', $route['key'], $params);
        if ($loginModalDestination) {
            $this->emit('modal:show', 'login', ['destinationRouteKey' => $loginModalDestination]);
        }
    }

    protected function getRoute(string $routeKey): array
    {
        $route = collect(self::ROUTES)->where('key', $routeKey)->firstOrFail();

        return $route + [
            'desktopProfileNav' => true,
            'requiresAuth' => false,
        ];
    }

    protected function pushUrlToBrowserHistory(array $route, array $params): void
    {
        $urlParams = http_build_query($params['url_params'] ?? []);
        $url = $this->getUrlFromRoute($route, $params) . ($urlParams ? '?' . $urlParams : '');
        $this->emit('nav:browser:history:push', $url);
    }

    protected function getUrlFromRoute(array $route, array $params)
    {
        if (! array_key_exists('bindings', $params)) {
            return $route['url'];
        }
        $bindings = array_map(static fn (array $bindingParams) => $bindingParams['id'], $params['bindings']);

        return app(RouterService::class)->getUrlWithBindingsSubstituted($route['url'], $bindings);
    }

    protected function mergeUrlParamsToParams(array $params): array
    {
        $urlParams = $params['url_params'] ?? [];
        unset($params['url_params']);

        return array_merge($params, $urlParams);
    }

    protected function passesAuthorization(array $route, array $routeParams): bool
    {
        if ($route['authorize'] ?? null) {
            return $this->{$route['authorize']}($routeParams);
        }

        return true;
    }

    public function navFromBrowserUrl(?string $browserUrl = null, bool $skipPushHistory = false): void
    {
        if (! $browserUrl) {
            $this->emitSelf('nav:to', 'browse', [], $skipPushHistory);

            return;
        }
        $urlParams = app(RouterService::class)->getParamsFromUrl($browserUrl);
        $url = app(RouterService::class)->cleanUrlFromParams($browserUrl);
        if ($url === '/mysun') {
            $this->navBack();

            return;
        }
        $route = app(RouterService::class)->getRouteFromUrl($url);
        if ($route['key'] === app(UserJourneysService::class)->getCurrentRouteKey()) {
            $this->navBack();

            return;
        }
        $routeParams = array_merge(
            $urlParams ? ['url_params' => $urlParams] : [],
            $route['params'] ?? []
        );
        $this->emitSelf('nav:to', $route['key'], $routeParams, $skipPushHistory);
    }

    public function render(): string
    {
        return '<div wire:init="init" class="d-none"></div>';
    }

    protected function getUrlParams(array $params): array
    {
        $urlParamsString = http_build_query($params['url_params'] ?? []);
        $urlParamsArray = $params['url_params'] ?? [];
        unset($params['url_params']);

        return compact('urlParamsString', 'urlParamsArray');
    }

    protected function authorizePlaylistDetail(array $routeParams): bool
    {
        if (! $routeParams) {
            return false;
        }
        $playlist = Playlist::find($routeParams['bindings']['playlist']['id']);
        if (! $playlist) {
            return false;
        }

        return (Auth::check() && $playlist->user_id === Auth::id()) || $playlist->user_id === null;
    }
}

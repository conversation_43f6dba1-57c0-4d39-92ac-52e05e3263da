<?php

return [
    'name' => 'LaravelPWA',
    'manifest' => [
        'name' => 'mySUN',
        'short_name' => 'mySUN',
        'start_url' => '/',
        'background_color' => '#ffcd00',
        'theme_color' => '#ffcd00',
        'display' => 'standalone',
        'orientation' => 'any',
        'status_bar' => 'black',
        'icons' => [],
        'splash' => [],
        'shortcuts' => [
            [
                'name' => 'Parcourir',
                'description' => 'Parcourir mySUN',
                'url' => '/mysun/parcourir',
                'icons' => [
                    'src' => '/images/pwa/icon-192x192.png',
                    'type' => 'image/png',
                    'purpose' => 'any',
                ],
            ],
            [
                'name' => 'Podcasts',
                'description' => 'Tous les podcasts',
                'url' => '/mysun/podcasts',
                'icons' => [
                    'src' => '/images/pwa/icon-192x192.png',
                    'type' => 'image/png',
                    'purpose' => 'any',
                ],
            ],
            [
                'name' => 'Agenda',
                'description' => 'Agenda des évènements',
                'url' => '/mysun/agenda',
                'icons' => [
                    'src' => '/images/pwa/icon-192x192.png',
                    'type' => 'image/png',
                    'purpose' => 'any',
                ],
            ],
        ],
        'custom' => [
            'icons' => [
                [
                    'src' => '/images/pwa/icon-72x72.png',
                    'type' => 'image/png',
                    'sizes' => '72x72',
                    'purpose' => 'any',
                ],
                [
                    'src' => '/images/pwa/icon-96x96.png',
                    'type' => 'image/png',
                    'sizes' => '96x96',
                    'purpose' => 'any',
                ],
                [
                    'src' => '/images/pwa/icon-128x128.png',
                    'type' => 'image/png',
                    'sizes' => '128x128',
                    'purpose' => 'any',
                ],
                [
                    'src' => '/images/pwa/icon-144x144.png',
                    'type' => 'image/png',
                    'sizes' => '144x144',
                    'purpose' => 'any',
                ],
                [
                    'src' => '/images/pwa/icon-152x152.png',
                    'type' => 'image/png',
                    'sizes' => '152x152',
                    'purpose' => 'any',
                ],
                [
                    'src' => '/images/pwa/icon-192x192.png',
                    'type' => 'image/png',
                    'sizes' => '192x192',
                    'purpose' => 'any',
                ],
                [
                    'src' => '/images/pwa/icon-384x384.png',
                    'type' => 'image/png',
                    'sizes' => '384x384',
                    'purpose' => 'any',
                ],
                [
                    'src' => '/images/pwa/icon-512x512.png',
                    'type' => 'image/png',
                    'sizes' => '512x512',
                    'purpose' => 'any',
                ],
                [
                    'src' => '/images/pwa/icon-72x72.png',
                    'type' => 'image/png',
                    'sizes' => '72x72',
                    'purpose' => 'maskable',
                ],
                [
                    'src' => '/images/pwa/icon-96x96.png',
                    'type' => 'image/png',
                    'sizes' => '96x96',
                    'purpose' => 'maskable',
                ],
                [
                    'src' => '/images/pwa/icon-128x128.png',
                    'type' => 'image/png',
                    'sizes' => '128x128',
                    'purpose' => 'maskable',
                ],
                [
                    'src' => '/images/pwa/icon-144x144.png',
                    'type' => 'image/png',
                    'sizes' => '144x144',
                    'purpose' => 'maskable',
                ],
                [
                    'src' => '/images/pwa/icon-152x152.png',
                    'type' => 'image/png',
                    'sizes' => '152x152',
                    'purpose' => 'maskable',
                ],
                [
                    'src' => '/images/pwa/icon-192x192.png',
                    'type' => 'image/png',
                    'sizes' => '192x192',
                    'purpose' => 'maskable',
                ],
                [
                    'src' => '/images/pwa/icon-384x384.png',
                    'type' => 'image/png',
                    'sizes' => '384x384',
                    'purpose' => 'maskable',
                ],
                [
                    'src' => '/images/pwa/icon-512x512.png',
                    'type' => 'image/png',
                    'sizes' => '512x512',
                    'purpose' => 'maskable',
                ],
            ],
        ],
    ],
];

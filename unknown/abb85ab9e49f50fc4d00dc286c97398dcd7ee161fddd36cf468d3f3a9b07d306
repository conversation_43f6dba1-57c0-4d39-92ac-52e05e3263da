<?php

namespace App\Http\Livewire\Programs;

use App\Models\Audio\Podcast;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Livewire\Component;

class Index extends Component
{
    public bool $initialized = false;

    public ?int $selectedRadioStationId = null;

    public ?string $selectedDay = null;

    public Collection $podcasts;

    public array $daysOfWeek = [];

    public array $selectedPodcastIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllPodcasts = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'radio:station:universe:updated' => 'setSelectedRadioStationId',
    ];

    public function mount(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'programs', livewireComponent: $this);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStationId($selectedRadioStationId);
        $this->setDaysOfCurrentWeek();
        $this->selectedDay = Date::today()->toDateString();
        $this->initialized = true;
    }

    public function setSelectedRadioStationId(int $selectedRadioStationId): void
    {
        $this->selectedRadioStationId = $selectedRadioStationId;
    }

    protected function setDaysOfCurrentWeek(): void
    {
        $startOfWeek = Date::now()->sub('days', 3);
        $daysOfWeek = [];
        for ($day = 0; $day < 7; $day++) {
            $daysOfWeek[] = $startOfWeek->add('days', $day)->toDateString();
        }
        $this->daysOfWeek = $daysOfWeek;
    }

    public function updateSelectedDay(string $selectedDay): void
    {
        $this->selectedDay = $selectedDay;
        $this->setPodcasts();
    }

    public function setPodcasts(): void
    {
        $this->podcasts = Podcast::with(['media', 'program.subPrograms'])
            ->where('type', Podcast::TYPE_REPLAY)
            ->whereDate('published_at', Date::parse($this->selectedDay))
            ->where('active', true)
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereRelation(
                    'radioStations',
                    fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStationId)
                )->orWhereDoesntHave('radioStations');
            })
            ->orderBy('published_at')
            ->get();
        $this->updatePodcastStatuses();
    }

    public function updatePodcastStatuses(): void
    {
        if ($this->pauseAllPodcasts) {
            $this->podcasts->map(function (Podcast $podcast) {
                $podcast->programSelected = in_array($podcast->id, $this->selectedPodcastIds, true);
                $podcast->playing = false;
                $podcast->subPodcasts = $this->podcasts->whereIn(
                    'program_id',
                    $podcast->program->subPrograms->pluck('id')
                );

                return $podcast;
            });

            return;
        }
        $this->selectedPodcastIds = [];
        $this->podcasts = $this->podcasts->map(function (Podcast $podcast) {
            $isPlaying = $podcast::class === $this->playedAudioSourceClass
                && $podcast->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedPodcastIds[] = $podcast->id;
            }
            $podcast->programSelected = $isPlaying;
            $podcast->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
            $podcast->subPodcasts = $this->podcasts->whereIn('program_id', $podcast->program->subPrograms->pluck('id'));

            return $podcast;
        });
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllPodcasts = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllPodcasts = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllPodcasts = false;
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->setPodcasts();
        }

        return view('livewire.programs.index');
    }
}

<?php

namespace App\Http\Livewire\Playlists;

use App\Models\Audio\Playlist;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Rail extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = true;

    public ?string $title = null;

    public string $railName;

    public Collection $playlists;

    public ?string $showAllRoute = null;

    public array $showAllParams = [];

    public array $selectedPlaylistIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllPlaylists = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    public function mount(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function init(): void
    {
        $this->initialized = true;
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->updateAudioPlaylistsStatuses();
        }

        return view('livewire.playlists.rail');
    }

    protected function updateAudioPlaylistsStatuses(): void
    {
        if ($this->pauseAllPlaylists) {
            $this->playlists = $this->playlists->map(function (Playlist $playlist) {
                $playlist->selected = in_array($playlist->id, $this->selectedPlaylistIds, true);
                $playlist->playing = false;

                return $playlist;
            });

            return;
        }
        $this->selectedPlaylistIds = [];
        $this->playlists = $this->playlists->map(function (Playlist $playlist) {
            $isPlaying = $playlist::class === $this->playedAudioSourceClass
                && $playlist->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedPlaylistIds[] = $playlist->id;
            }
            $playlist->selected = $isPlaying;
            $playlist->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $playlist;
        });
        $this->emit('rail:load', ['railName' => $this->railName]);
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllPlaylists = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllPlaylists = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllPlaylists = false;
    }
}

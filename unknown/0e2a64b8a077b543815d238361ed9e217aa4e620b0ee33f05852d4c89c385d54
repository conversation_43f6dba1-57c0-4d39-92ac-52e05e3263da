<?php

use App\Http\Controllers\Api\AgendaController;
use Illuminate\Support\Facades\Route;

Route::get('/agenda/thematics', [AgendaController::class, 'thematics'])
    ->name('api.agenda.thematics');

Route::get('/agenda/stations', [AgendaController::class, 'stations'])
    ->name('api.agenda.stations');

Route::get('/agenda/users', [AgendaController::class, 'users'])
    ->name('api.agenda.users');

Route::get('/agenda/places', [AgendaController::class, 'places'])
    ->name('api.agenda.places');

Route::get('/agenda/placetypes', [AgendaController::class, 'placeTypes'])
    ->name('api.agenda.placetypes');

Route::get('/agenda/events', [AgendaController::class, 'events'])
    ->name('api.agenda.events');

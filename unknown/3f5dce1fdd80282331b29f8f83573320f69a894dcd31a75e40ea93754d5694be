<?php

namespace App\Http\Livewire\Events;

use App\Models\Events\Event;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Rail extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = true;

    public ?string $title = null;

    public string $railName;

    public Collection $events;

    public ?string $showAllRoute = null;

    public array $showAllParams = [];

    public array $selectedEventIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllEvents = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function init(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->updateAudioEventsStatuses();
        $this->initialized = true;
    }

    public function render(): View
    {
        return view('livewire.events.rail');
    }

    protected function updateAudioEventsStatuses(): void
    {
        if ($this->pauseAllEvents) {
            $this->events = $this->events->map(function (Event $event) {
                $event->selected = in_array($event->id, $this->selectedEventIds, true);
                $event->playing = false;

                return $event;
            });

            return;
        }
        $this->selectedEventIds = [];
        $this->events = $this->events->map(function (Event $event) {
            $isPlaying = $event::class === $this->playedAudioSourceClass
                && $event->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedEventIds[] = $event->id;
            }
            $event->selected = $isPlaying;
            $event->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $event;
        });
        $this->emit('rail:load', ['railName' => $this->railName]);
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllEvents = false;
        $this->updateAudioEventsStatuses();
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllEvents = true;
        $this->updateAudioEventsStatuses();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllEvents = false;
        $this->updateAudioEventsStatuses();
    }
}

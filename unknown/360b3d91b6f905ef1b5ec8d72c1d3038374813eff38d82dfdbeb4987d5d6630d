<?php

namespace Database\Factories\Announcements;

use App\Models\Announcements\Announcement;
use App\Models\Radio\RadioStation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

class AnnouncementFactory extends Factory
{
    /** @var string */
    protected $model = Announcement::class;

    /** @throws \Exception */
    public function definition(): array
    {
        $announcementClass = Arr::random(array_keys(Announcement::ANNOUNCEABLES));

        return [
            'announceable_type' => $announcementClass,
            'announceable_id' => app($announcementClass)->inRandomOrder()->first()->id,
            'title' => $this->faker->boolean ? $this->faker->catchPhrase : null,
            'subtitle' => $this->faker->boolean ? $this->faker->sentence : null,
            'published_at' => $this->faker->dateTimeBetween(),
            'unpublished_at' => $this->faker->boolean ? $this->faker->dateTimeBetween('now', '+2 months') : null,
            'active' => $this->faker->boolean,
            'url' => $this->faker->boolean ? $this->faker->url : null,
        ];
    }

    public function withRadioStations(array $radioStationIds = []): self
    {
        return $this->afterCreating(function (Announcement $announcement) use ($radioStationIds) {
            if (! $radioStationIds) {
                $radioStationIds = RadioStation::inRandomOrder()
                    ->limit($this->faker->numberBetween(0, 5))
                    ->pluck('id')
                    ->toArray();
            }
            $announcement->radioStations()->sync($radioStationIds);
        });
    }

    public function active(): self
    {
        return $this->state(fn () => ['active' => true]);
    }

    public function inactive(): self
    {
        return $this->state(fn () => ['active' => false]);
    }
}

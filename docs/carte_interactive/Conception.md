# Notions de conception de la carte interactive

## Introduction

La carte interactive s'appuie à la fois sur le framework JavaScript React,
accompagné de la bibliothèque [`react-map-gl`][1], et le framework Livewire.

Le choix de cette architecture hybride a été motivé par l'idée de permettre
au(x) mainteneur(x) de l'application, basée sur Laravel/Livewire, d'être le
plus indépendant possible dans la maintenance de la carte interactive. C'est
pourquoi un maximum d'éléments ont été développés en Livewire.

## Les composants Livewire

Une majorité des éléments visibles sur la carte sont des composants Livewire.

La carte en elle-même est propulsée par un composant développé en React et
utilisant la bibliothèque `react-map-gl`. Néanmoins, l'élément DOM dans lequel
elle s'instancie est également lié à un composant Livewire qui la représente
côté back-end.

L'illustration ci-dessous présente les principaux composants constituant la
carte interactive. Y sont également mentionnés une petite partie des évènements
qu'ils déclenchent ou écoutent pour donner naissance aux interactivités.
\([Accéder à la documentation complète des évènements.](Evenements.md)\)

![Aperçu de la carte interactive](images/overview.png)

L'ensemble des composants Livewire spécifiques à la carte sont regroupés dans
le namespace `App\Http\Livewire\Map`. Le composant `Index` en est le composant
racine, celui qui invoque tous les autres. Le composant `Map` est quant à lui
le représentant de la carte : il est l'hôte et l'initiateur du composant React
qui donne vie à la carte.

Le composant React et les ressources qu'il utilise se situent dans le dossier
`resources/js/map`.

## Le modèle des données

Trois nouvelles tables ont été introduites dans la base de données pour les
besoins de la carte interactive.

![Schéma de données](images/schema.png)

La table `content_location` sert à relier des « contenus » avec des
« localisations ».

Sont appelés « contenus » les divers types d'information remontés sur
l'application MySUN : les évènements, les actualités, les playlists, etc.

Quant aux localisations, il peut s'agir d'un lieu (table `place`) ou d'une
position géographique (table `point`).

Dans la table `content_location` :

- `content_type` et `content_id` servent à référencer les contenus,
- `location_type` et `location_id` servent à référencer les localisations.

Du côté du modèle objet (_Eloquent_), l'entité `ContentLocation` est donc dotée
de deux relations polymorphes _many to one_, l'une pour les contenus, l'autre
pour les localisations.

Pour les entités du modèle nécessitant d'être liées à une localisation, le
trait `App\Models\Traits\HasLocation` est destiné à faciliter leur rattachement
à l'entité `ContentLocation`. Il déclare la relation _one to many_ vers les
`ContentLocation` et fournit quelques méthodes pratiques pour accéder aisément
à la localisation.

Bien qu'inexploitée pour le moment, le modèle est conçu pour offrir la
possibilité de relier un contenu à plusieurs localisations.

## Les services

L'implémentation de la carte sous la forme d'un ensemble de composants Livewire
œuvrant de concert nécessitait qu'ils aient tous accès à un certain nombre de
données. Cette contrainte a orienté leurs implémentations autour des deux
services suivants :

- `App\Services\Map\MapService` : service centralisant la logique de sélection
  et de recherche des lieux à afficher sur la carte.
- `App\Services\Map\MapStateService` : service centralisant l'ensemble des
  données relatives à la carte permettant ainsi d'en connaître l'état à tout
  instant.

Le premier service, `MapService`, utilise le second pour obtenir les données
nécessaires à la construction des requêtes qu'il adresse à la base de données
ou au moteur de recherche.

Le second service, `MapStateService`, représentant l'état de la carte à chaque
instant, est maintenu par les composants agissant eux-mêmes sur la carte.

Il est possible d'obtenir le `MapStateService` à partir du `MapService` via sa
méthode `state()`.

[1]: https://visgl.github.io/react-map-gl/

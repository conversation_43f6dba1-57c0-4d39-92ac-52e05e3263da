# Les évènements en jeu sur la carte interactive

La carte interactive est constituée de composants à la fois front-end (React)
et back-end (Laravel/Livewire). Ces composants, issus d'univers différents,
doivent être en mesure de communiquer entre eux pour les besoins interactifs
de la carte. Techniquement les communications sont basées sur des évènements.

Ces évènements peuvent être de natures différentes :

- des évènements DOM de type [`CustomEvent`][1],
- des [évènements Livewire][2].

Dans le cas des évènements DOM `CustomEvent`, l'option `detail` offerte par le
constructeur est utilisée pour peupler l'évènement des données nécessaires à
son utilisation.

Ci-après, le terme « carte » désigne le composant React qui la représente
côté navigateur.

## Les évènements DOM

Ces évènements sont principalement lancés depuis le back-end à l'attention
de la carte.

### `map:init`

Déclencher l'initialisation de la carte.

À noter : un évènement Livewire porte également le même nom.

#### Données

- `settings` : Objet contenant le paramétrage de la carte.
- `content` : Objet porteur des « contenus » (géométries) à dessiner sur la
  carte, conforme à la structure GeoJSON de type `FeatureCollection`.

##### Les paramètres (`settings`)

- `mapContainerId` (_string_) : Identifiant HTML de l'élément hôte de la carte.
- `fullscreenContainerId` (_string_) : Identifiant HTML de l'élément contenant
  la zone de la page à considérer pour le passage en mode plein écran.
- `position` (_float\[2\]_) : Coordonnées de la position centrale initiale de
  la carte (longitude en première valeur, latitude en seconde).
- `zoom` (_float_) : Niveau de zoom initial de la carte.
- `mapStyle` : Objet déterminant le fond de carte à utiliser. Deux
  sous-propriétés sont attendues :
  - `name` (_string_) : Identifiant du fond de carte à utiliser.
  - `theme` (_string_) : Jeu(x) de couleurs disponible(s) pour le fond de
    carte : `dark`, `light`, ou `both` si les deux versions existent.
  Ces sous-propriétés permettent de déterminer le nom du fichier de définition
  des styles cartographiques à utiliser. Voir le dossier `resources/js/map/styles`.
  Si le paramètre `mapStyle` est omis, c'est le fond de carte par défaut, nommé 
  `default`, qui sera utilisé.
- `colorScheme` (_string_) : Jeu de couleurs (thème) défini au sein du profil de 
  l'utilisateur connecté (`auto`, `dark` ou `light`).
- `searchMode` (_string_) : Mode de « recherche » de la carte. Les valeurs
  possibles sont `all` pour considérer l'ensemble des types de contenu, ou
  l'identifiant particulier de l'un d'eux, à savoir : `event`, `news`,
  `performer`, `playlist` ou `podcast` de façon à n'en considérer qu'un seul.
- `activePlaceId` (_integer_) : Identifiant du lieu à rendre « actif » par défaut.

##### Le contenu (`content`)

La propriété `content` est destinée à contenir les informations des géométries
(_feature_) à dessiner sur la carte.

En complément des données géométriques, les lieux, représentés par des
géométries de type `Point`, nécessitent les propriétés suivantes :

- `type` (_string_) : Type du lieu.
- `id` (_integer_) : Identifiant du lieu.
- `label` (_string_) : Dénomination du lieu.
- `contentCounts` (_\[string => integer\]_) : Tableau de dénombrement des
  contenus associés au lieu pour chaque type de contenu. Les clés identifiant
  les types de contenu visibles sur la carte sont : `event`, `news`,
  `performer`, `playlist` et `podcast`.
- `todaysContentCount` (_integer_) : Nombre de contenus associés au lieu ayant
  trait à la date du jour, tous types confondus.

Ces propriétés sont à déclarer au sein de l'objet `properties` de la structure
GeoJSON représentant chaque lieu. Voir la méthode `toGeoJson()` de la classe
`App\Models\Map\PlaceCollection`.

#### Exemple

```php
$this->dispatchBrowserEvent('map:init', [
    'settings' => [
        'mapContainerId' => 'map-host-element',
        'position' => [-1.561013, 47.219074],
        'zoom' => 12,
        'searchMode' => 'event',
    ],
    'content' => [
        'type' => 'FeatureCollection',
        'features' => [
            [
                'type' => 'Feature',
                'properties' => [
                    'type' => 'bar',
                    'id' => 33,
                    'label' => "Jazz chez les ducs",
                    'contentCounts' => [
                        'event' => 2,
                        'news' => 0,
                        'performer' => 0, 
                        'playlist' => 3,
                        'podcast' => 1,
                    ],
                    'todaysContentCount' => 0,
                ],
                'geometry' => [
                    'type' => 'Point',
                    'coordinates' => [-1.549386, 47.216201],
                ],
            ], [
                'type' => 'Feature',
                'properties' => [
                    'type' => 'studio_radio',
                    'id' => 49,
                    'label' => "Studio du Capitole",
                    'contentCounts' => [
                        'event' => 4,
                        'news' => 2,
                        'performer' => 5,
                        'playlist' => 3,
                        'podcast' => 0,
                    ],
                    'todaysContentCount' => 3,
                ],
                'geometry' => [
                    'type' => 'Point',
                    'coordinates' => [1.444265, 43.604423],
                ],
            ],
        ],
    ],
]);
```

Ce qui sera transformé en JSON et aboutira au résultat suivant :

```json
{
    "settings": {
        "mapContainerId": "map-host-element",
        "position": [-1.561013, 47.219074],
        "zoom": 12,
        "searchMode": "event"
    },
    "content": {
        "type": "FeatureCollection",
        "features": [
            {
                "type": "Feature",
                "properties": {
                    "type": "bar",
                    "id": 33,
                    "label": "Jazz chez les ducs",
                    "contentCounts": {
                        "event": 2,
                        "news": 0,
                        "performer": 0, 
                        "playlist": 3,
                        "podcast": 1
                    },
                    "todaysContentCount": 0
                },
                "geometry": {
                    "type": "Point",
                    "coordinates": [-1.549386, 47.216201]
                }
            },
            {
                "type": "Feature",
                "properties": {
                    "type": "studio_radio",
                    "id": 49,
                    "label": "Studio du Capitole",
                    "contentCounts": {
                        "event": 4,
                        "news": 2,
                        "performer": 5,
                        "playlist": 3,
                        "podcast": 0
                    },
                    "todaysContentCount": 3
                },
                "geometry": {
                    "type": "Point",
                    "coordinates": [1.444265, 43.604423]
                }
            }
        ]
    }
}
```

### `map:draw`

Dessiner de nouvelles géométries sur la carte. Les géométries pré-existantes
sont effacées.

#### Données

Les données attendues pour l'évènement `map:draw` sont similaires à celles de
l'évènement `map:init`, mais seule une partie des paramètres sont disponibles : 

- `position` (_float\[2\]_) : Coordonnées de la nouvelle position centrale de
  la carte (longitude en première valeur, latitude en seconde).
- `zoom` (_float_) : Nouveau niveau de zoom (nécessite que `position` soit
  également défini).
- `mapStyle` : Objet déterminant le fond de carte à utiliser. Deux
  sous-propriétés sont attendues :
  - `name` (_string_) : Identifiant du fond de carte à utiliser.
  - `theme` (_string_) : Jeu(x) de couleurs disponible(s) pour le fond de
    carte : `dark`, `light`, ou `both` si les deux versions existent.
  Ces sous-propriétés permettent de déterminer le nom du fichier de définition
  des styles cartographiques à utiliser. Voir le dossier `resources/js/map/styles`.
  Si le paramètre `mapStyle` est omis, c'est le fond de carte par défaut, nommé
  `default`, qui sera utilisé.
- `searchMode` (_search_) : Mode de « recherche » de la carte. Les valeurs
  possibles sont `all` pour considérer l'ensemble des types de contenu, ou
  l'identifiant particulier de l'un d'eux, à savoir `event`, `news`,
  `performer`, `playlist` ou `podcast` de façon à n'en considérer qu'un seul.

#### Exemple

```php
$this->dispatchBrowserEvent('map:draw', [
    'settings' => [
        'mapStyle' => [
            'name' => 'junior',
            'theme' => 'light',
        ],       
        'searchMode' => 'all',
    ],
    'content' => [
        'type' => 'FeatureCollection',
        'features' => [
            [
                'type' => 'Feature',
                'properties' => [
                    'type' => 'bar',
                    'id' => 33,
                    'label' => "Jazz chez les ducs",
                    'contentCounts' => [
                        'event' => 2,
                        'news' => 0,
                        'performer' => 0, 
                        'playlist' => 3,
                        'podcast' => 1,
                    ],
                    'todaysContentCount' => 0,
                ],
                'geometry' => [
                    'type' => 'Point',
                    'coordinates' => [-1.549386, 47.216201],
                ],
            ], [
                'type' => 'Feature',
                'properties' => [
                    'type' => 'studio_radio',
                    'id' => 49,
                    'label' => "Studio du Capitole",
                    'contentCounts' => [
                        'event' => 4,
                        'news' => 2,
                        'performer' => 5,
                        'playlist' => 3,
                        'podcast' => 0,
                    ],
                    'todaysContentCount' => 3,
                ],
                'geometry' => [
                    'type' => 'Point',
                    'coordinates' => [1.444265, 43.604423],
                ],
            ],
        ],
    ],
]);
```

### `map:move`

Centrer la carte sur une position donnée.

#### Données

- `position` (_float\[2\]_) : Coordonnées de la nouvelle position centrale de
  la carte (longitude en première valeur, latitude en seconde).
- `zoom` (_float_) : Nouveau niveau de zoom (optionnel).

#### Exemple

```php
$this->dispatchBrowserEvent('map:move', [
    'position' => [-0.87945, 47.057869],
    'zoom' => 14.5,
]);
```

## Les évènements Livewire

Les données attendues pour chaque évènement abordé dans cette section sont
à passer en argument de l'une des méthodes fournies par Livewire pour émettre
un évènement. Ces données sont listées dans l'ordre dans lequel elles doivent
être transmises à la méthode en question.

### `map:init`

Annoncer que le composant `App\Http\Livewire\Map\Map`, qui représente la carte
en elle-même côté back-end, a été initialisé. Les autres composants peuvent
alors être instanciés (voir le composant `App\Http\Livewire\Map\Index` et son
template).

Aucune donnée n'est requise pour cet évènement.

À noter : un évènement DOM porte également le même nom. 

### `map:view-state`

Indiquer un changement de position ou du niveau de zoom sur la carte. Évènement
déclenché par celle-ci après chaque mouvement effectué lors de son exploration.

#### Données

Un tableau associatif contenant les propriétés suivantes :

- `longitude` (_float_) : Longitude de la position centrale de la carte.
- `latitude` (_float_) : Latitude de la position centrale de la carte.
- `zoom` (_float_) : Niveau de zoom de la carte.

#### Exemple

```js
Livewire.emit('map:view-state', {
    "longitude": -0.87945,
    "latitude": 47.057869,
    "zoom": 11.512654
});
```

### `card:click`

Annoncer un clic survenu sur un composant de type _card_.

Certains composants de type _card_ ont un attribut `actionMode`. Lorsque cet
attribut a pour valeur `event`, le composant concerné déclenche un évènement
`card:click`. (Par défaut, c'est-à-dire quand `actionMode` a pour valeur
`route`, il déclenche un changement de page.)

Dans le contexte de la carte interactive, cet évènement provoque l'affichage
des informations détaillées du contenu représenté par le composant _card_.

#### Données

- Type du contenu (_string_) : `event`, `news`, `performer`, `playlist`
  ou `podcast`.
- Identifiant du contenu (_integer_).

#### Exemple

```js
Livewire.emit('card:click', 'performer', 456);
```

### `map:list:init`

Initialiser les comportements JavaScript du composant `App\Http\Livewire\Map\ContentRail`.
Évènement déclenché par le composant lui-même.

(Voir le fichier [front-app.js](resources/js/app/front-app.js).)

### `map:list:show`

Lister les contenus d'un certain type en lien avec un lieu donné.

#### Données

- Type des contenus à lister (_string_) : `event`, `news`, `performer`,
  `playlist` ou `podcast`.
- Identifiant du lieu (_integer_).

#### Exemple

```js
Livewire.emit('map:list:show', 'event', 42);
```

### `map:list:hide`

Masquer la liste des contenus. Aucune donnée n'est requise pour cet évènement.

Pour plus de réactivité, cette action est d'abord réalisée côté front-end
(sans mise à jour immédiate du composant Livewire concerné), un évènement
`map:list:hidden` est néanmoins déclenché consécutivement.

(Voir le fichier [front-app.js](resources/js/app/front-app.js).)

### `map:list:hidden`

Indiquer que la liste des contenus a été masquée côté front-end. Il s'agit
notamment ici de synchroniser l'état du composant Livewire concerné entre le
front-end et le back-end.

### `map:list:shown`

Indiquer que la liste des contenus est à nouveau visible (démaquée) côté
front-end. Il s'agit notamment ici de synchroniser l'état du composant Livewire
concerné entre le front-end et le back-end.

### `map:info:init`

Initialiser les comportements JavaScript du composant `App\Http\Livewire\Map\InfoPanel`.
Évènement déclenché par le composant lui-même.

(Voir le fichier [front-app.js](resources/js/app/front-app.js).)

### `map:info:show`

Afficher les informations détaillées relatives à un contenu donné (évènement,
actualité, etc.). Par la suite, cet évènement pourrait également être utilisé
pour afficher les informations relatives à un lieu.

#### Données

- Type du contenu (_string_) : `event`, `news`, `performer`, `playlist`
  ou `podcast`.
- Identifiant du contenu (_integer_).

#### Exemple

```js
Livewire.emit('map:info:show', 'news', 789);
```

### `map:info:hide`

Masquer les informations détaillées. Aucune donnée n'est requise pour cet
évènement.

Pour plus de réactivité, cette action est d'abord réalisée côté front-end
(sans mise à jour immédiate du composant Livewire concerné), un évènement
`map:info:hidden` est néanmoins déclenché consécutivement.

(Voir le fichier [front-app.js](resources/js/app/front-app.js).)

### `map:info:hidden`

Indiquer que les informations détaillées ont été masquées côté front-end. Il
s'agit notamment ici de synchroniser l'état du composant Livewire concerné
entre le front-end et le back-end.

### `map:info:shown`

Indiquer que les informations détaillées sont à nouveau visibles (démaquées)
côté front-end. Il s'agit notamment ici de synchroniser l'état du composant
Livewire concerné entre le front-end et le back-end.

### `map:place:selected`

Informer du lieu nouvellement « actif » sur la carte.

Cette notion de lieu actif est intrinsèquement liée au « menu » d'actions
multiples. Si le menu n'est pas actuellement visible pour un lieu donné, alors
aucun lieu n'est considéré comme étant actif.

#### Données

- Identifiant du lieu (_integer_|_null_).

#### Exemple

```js
Livewire.emit('map:place:selected', 123);
```

### `map:search:performed`

Annoncer qu'une recherche par texte vient d'être effectuée.

#### Données

- Mode de recherche (_string_) : `all`, `event`, `news`, `performer`,
  `playlist` ou `podcast`.
- Texte recherché (_string_).

#### Exemple

```php
$this->emit('map:search:performed', 'performer', "Phil");
```

### `map:search:reset`

Annoncer l'arrêt de la recherche par texte. C'est-à-dire que le contenu du
champ de recherche a été effacé.

Aucune donnée n'est requise pour cet évènement.

### `map:mode:changed`

Annoncer que le mode de recherche vient d'être modifié.

#### Données

- Mode de recherche (_string_) : `all`, `event`, `news`, `performer`,
  `playlist` ou `podcast`.

#### Exemple

```php
$this->emit('map:mode:changed', 'playlist');
```

### `map:filter:changed`

Annoncer que la valeur d'un filtre a été modifié.

#### Données

- Type de filtre (_string_) : seul `thematic` est actuellement disponible.
- Identifiant du filtre (_integer_|_string_)
- Statut (_boolean_) : `true` si actif/coché, `false` sinon.

#### Exemple

```php
$this->emit('map:filter:changed', MapFilterType::Thematic->value, 3, true);
```

### `map:period:changed`

Annoncer que la période considérée pour la sélection des lieux à afficher sur
la carte a été modifiée.

#### Données

- Date de début (_DateTimeImmutable_|_null_).
- Date de fin (_DateTimeImmutable_|_null_).

#### Exemple

```php
$this->emit(
    'map:period:changed',
    new DateTimeImmutable('2015-01-01 00:00:00'),
    new DateTimeImmutable('2015-12-31 23:59:59'),
);
```

### `map:station:changed`

Annoncer que la station de radio considérée pour la sélection des lieux
à afficher sur la carte a été modifiée.

#### Données

- Identifiant de la station de radio (_integer_).

#### Exemple

```php
$this->emit('map:station:changed', 5);
```

[1]: https://developer.mozilla.org/fr/docs/Web/API/CustomEvent
[2]: https://laravel-livewire.com/docs/2.x/events

// Source du schéma de données modélisé
// sur https://dbdiagram.io

Project sunmap {
  database_type: 'PostgreSQL'
  Note: 'Schéma des données spatiales.'
}

Table places {
  id integer [pk, increment]
  name varchar [not null]
  type varchar [not null, note: 'bar, centre_culturel, complexe_sportif, salle_de_concert, etc.']
  geotype varchar [note: 'point or territory']
  addr_street1 varchar
  addr_street2 varchar
  addr_city varchar
  addr_zip varchar
  enabled bool [default: true]
  created_at datetime
  updated_at datetime
}

Table points {
  id integer [pk, increment]
  coord point [not null]
  place_id integer
  since datetime
  until datetime
}

Table content_location {
  id integer [pk, increment]
  content_type string
  content_id integer
  location_type string
  location_id integer
  indexes {
    (content_type, content_id, location_type, location_id) [unique]
  }
}

// many-to-one
Ref: points.place_id > places.id

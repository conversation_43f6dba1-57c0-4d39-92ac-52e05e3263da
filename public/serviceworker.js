var staticCacheName = 'pwa-v' + new Date().getTime();
var filesToCache = [
    // Views ***********************************************************************************************************
    '/offline',
    // CSS *************************************************************************************************************
    // Base
    '/css/front-web.css',
    '/css/front-app.css',
    // Brickables
    '/css/brickables/carousel.css',
    // Web
    '/css/templates/front/web/contact/page/show.css',
    // App templates
    '/css/templates/front/app/browse.css',
    '/css/templates/front/app/event-details.css',
    '/css/templates/front/app/events.css',
    '/css/templates/front/app/news.css',
    '/css/templates/front/app/news-details.css',
    '/css/templates/front/app/performer-details.css',
    '/css/templates/front/app/playlist-details.css',
    '/css/templates/front/app/playlists.css',
    '/css/templates/front/app/podcast-details.css',
    '/css/templates/front/app/podcasts.css',
    '/css/templates/front/app/previous-broadcast-songs.css',
    '/css/templates/front/app/profile-information.css',
    '/css/templates/front/app/program-details.css',
    '/css/templates/front/app/programs.css',
    '/css/templates/front/app/search-event-results.css',
    '/css/templates/front/app/search-podcast-results.css',
    '/css/templates/front/app/search-playlist-results.css',
    '/css/templates/front/app/search-performer-results.css',
    '/css/templates/front/app/search-album-results.css',
    '/css/templates/front/app/search-results.css',
    '/css/templates/front/app/search-song-results.css',
    // JS **************************************************************************************************************
    // Base
    '/js/front-web.js',
    '/js/front-app.js',
    '/js/manifest.js',
    '/js/vendor.js',
    // Brickables
    '/js/brickables/carousel.js',
    // Web template
    '/js/templates/front/web/contact/page/show.js',
    // Fonts ***********************************************************************************************************
    // Fontawesome
    '/fonts/fontawesome/fa-brands-400.ttf',
    '/fonts/fontawesome/fa-brands-400.woff2',
    '/fonts/fontawesome/fa-regular-400.ttf',
    '/fonts/fontawesome/fa-regular-400.woff2',
    '/fonts/fontawesome/fa-solid-900.ttf',
    '/fonts/fontawesome/fa-solid-900.woff2',
    '/fonts/fontawesome/fa-v4compatibility.ttf',
    '/fonts/fontawesome/fa-v4compatibility.woff2',
    // Images **********************************************************************************************************
    // Browse
    '/images/browse/to-inspire-you-events.png',
    '/images/browse/to-inspire-you-news-articles.png',
    '/images/browse/to-inspire-you-podcasts.png',
    '/images/browse/to-inspire-you-songs.png',
    '/images/browse/to-inspire-you-playlists.png',
    '/images/browse/to-inspire-you-dedication.png',
    '/images/browse/to-inspire-you-walk-around.png',
    // Home
    `/images/home/<USER>
    // Logos
    '/images/logos/app-store.png',
    '/images/logos/apple.png',
    '/images/logos/facebook.png',
    '/images/logos/google-play.png',
    '/images/logos/instagram.png',
    '/images/logos/linkedin.png',
    '/images/logos/play-store.png',
    '/images/logos/twitter.png',
    '/images/logos/x.png',
    '/images/logos/youtube.png',
    '/images/logos/bluesky.png',
    '/images/logos/mastodon.png',
    // PWA
    '/images/pwa/icon-72x72.png',
    '/images/pwa/icon-96x96.png',
    '/images/pwa/icon-128x128.png',
    '/images/pwa/icon-144x144.png',
    '/images/pwa/icon-152x152.png',
    '/images/pwa/icon-192x192.png',
    '/images/pwa/icon-384x384.png',
    '/images/pwa/icon-512x512.png',
    // Favicon *********************************************************************************************************
    '/favicon.ico',
    // Mix manifest ****************************************************************************************************
    '/mix-manifest.json',
    // Vendor **********************************************************************************************************
    '/vendor/livewire/livewire.js',
    '/vendor/livewire/manifest.json',
    '/vendor/sweetalert/sweetalert.all.js'
];

// Cache on install
self.addEventListener('install', event => {
    this.skipWaiting();
    event.waitUntil(
        caches.open(staticCacheName)
            .then(cache => {
                return cache.addAll(filesToCache);
            })
    );
});

// Clear cache on activate
self.addEventListener('activate', event => {
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames
                    .filter(cacheName => (cacheName.startsWith('pwa-')))
                    .filter(cacheName => (cacheName !== staticCacheName))
                    .map(cacheName => caches.delete(cacheName))
            );
        })
    );
});

// Serve from Cache
self.addEventListener('fetch', event => {
    const url = new URL(event.request.url);
    const isFilesToCacheRequest = filesToCache.includes(url.pathname);
    if (isFilesToCacheRequest) {
        event.respondWith(async function () {
            try {
                var res = await fetch(event.request);
                var cache = await caches.open('cache');
                cache.put(event.request.url, res.clone());
                return res;
            } catch (error) {
                return caches.match(event.request);
            }
        }());
    } else {
        return;
    }
});

<?php

namespace Database\Seeders;

use App\Models\PageContents\PageContent;
use Illuminate\Database\Seeder;

class PageContentsSeeder extends Seeder
{
    public function run(): void
    {
        $this->home();
        $this->joinUs();
        $this->contact();
    }

    protected function home(): void
    {
        if (! PageContent::where('unique_key', 'home_page_content')->exists()) {
            PageContent::factory()->home()
            // Bricks
                ->withOneTitleTextColumnOneImageColumnBrick(
                    titleLeft: 'Une nouvelle plateforme multisupport',
                    textLeft: 'Accédez au contenu de SUN radio partout et ce dès que vous en avez envie !',
                    invertOrder: true
                )
                ->withSpacer<PERSON>rick('xxl')
                ->withOneTitleTextColumnOneImageColumnBrick(
                    titleLeft: 'Une équipe toujours de bonne humeur',
                    textLeft: 'Venez découvrir les évènements animés par nos membre dans votre région !',
                    invertOrder: false
                )
                ->with<PERSON>pace<PERSON><PERSON><PERSON>('xxl')
                ->withOneTitleTextColumnOneImageColumnBrick(
                    titleLeft: 'À consommer sans modération',
                    textLeft: 'Des contenus inédits et locaux toujours plus cool !',
                    invertOrder: true
                )
                ->withSpacerBrick('xxl')
                ->withOneTitleTextColumnOneImageColumnBrick(
                    titleLeft: 'Une radio associative locale  ',
                    textLeft: 'Riche de monde et de culture, embarquez dans cette aventure !',
                    invertOrder: false
                )
            // Content
                ->withSeoMeta('Accueil')
                ->create();
        }
    }

    protected function contact(): void
    {
        if (! PageContent::where('unique_key', 'contact_page_content')->exists()) {
            PageContent::factory()->contact()
                // Bricks
                ->withSpacerBrick('xl')
                ->withTitleBrick('Nous contacter', 'h1', 'h1')
                ->withSpacerBrick('sm')
                ->withOneTextColumnBrick()
                ->withSpacerBrick('lg')
                // Content
                ->withSeoMeta('Contact')
                ->create();
        }
    }

    protected function joinUs(): void
    {
        if (! PageContent::where('unique_key', 'join_us_page_content')->exists()) {
            PageContent::factory()->joinUs()
                // Bricks
                ->withSpacerBrick('xl')
                ->withTitleBrick('Nous rejoindre', 'h1', 'h1')
                ->withSpacerBrick('sm')
                ->withOneTextColumnBrick()
                ->withSpacerBrick('lg')
                // Content
                ->withSeoMeta('Nous rejoindre')
                ->create();
        }
    }
}

<?php

namespace Database\Seeders;

use App\Console\Commands\Elasticsearch\ResetNewsArticlesIndex;
use App\Models\Map\Place;
use App\Models\News\NewsArticle;
use Illuminate\Database\Seeder;

class NewsArticlesSeeder extends Seeder
{
    public function run(): void
    {
        if (! app()->environment('local')) {
            $this->command->call(ResetNewsArticlesIndex::class);

            return;
        }

        $this->callOnce(PlacesSeeder::class);

        NewsArticle::factory(10)
            ->withRadioStations()
            ->withMedia()
            ->active()
            ->create();

        NewsArticle::factory(10)
            ->withRadioStations()
            ->withPlace(Place::with('points')->get())
            ->withMedia()
            ->active()
            ->create();

        $this->command->call(ResetNewsArticlesIndex::class);
    }
}

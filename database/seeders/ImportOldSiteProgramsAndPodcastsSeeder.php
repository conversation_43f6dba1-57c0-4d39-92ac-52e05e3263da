<?php

namespace Database\Seeders;

use App\Console\Commands\Elasticsearch\ResetPodcastsIndex;
use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Radio\Program;
use App\Models\Radio\ProgramRecurrence;
use App\Models\Radio\RadioStation;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Date;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\ConsoleOutput;

class ImportOldSiteProgramsAndPodcastsSeeder extends Seeder
{
    /**
     * @throws \JsonException
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function run(): void
    {
        Config::set('media-library.max_file_size', 1024 * 1024 * 600);
        $oldPrograms = json_decode(
            file_get_contents(resource_path('seeds/old-site/podcasts.json')),
            true,
            512,
            JSON_THROW_ON_ERROR
        );
        $output = new ConsoleOutput();
        $progressBar = new ProgressBar($output, count($oldPrograms));
        foreach (collect($oldPrograms)->sortBy('created')->values() as $index => $oldProgram) {
            if ($index > 9 && app()->environment('local')) {
                break;
            }
            $podcastsTags = collect($oldProgram['emissions'])->pluck('tags')->toArray();
            $programTags = array_unique(array_merge(...$podcastsTags));
            $program = Program::firstOrCreate(['title' => $oldProgram['title']], [
                'title' => $oldProgram['title'],
                'tags' => implode(', ', $programTags),
                'description' => strip_tags(html_entity_decode($oldProgram['body'] ?? '', ENT_QUOTES)),
                'duration' => $oldProgram['duree'] * 60,
            ]);
            if (app()->environment('local')) {
                if ($program->subPrograms->isEmpty()) {
                    $subProgramsCount = random_int(1, 3);
                    for ($i = 0; $i < $subProgramsCount; $i++) {
                        Program::factory()
                            ->withMedia($program->getFirstMedia('cover')?->getFullUrl())
                            ->create([
                                'main_program_id' => $program->id,
                                'tags' => implode(', ', $programTags),
                                'description' => strip_tags(html_entity_decode($oldProgram['body'] ?? '', ENT_QUOTES)),
                                'duration' => $oldProgram['duree'] * 60 / $subProgramsCount,
                            ]);
                    }
                }
                ProgramRecurrence::factory()
                    ->withProgram($program)
                    ->withSubPrograms(Program::where('main_program_id', $program->id)->get())
                    ->count(random_int(3, 5))
                    ->create();
            }
            if ($oldProgram['image_source'] && $program->getMedia('cover')->isEmpty()) {
                rescue(
                    static fn () => $program->addMediaFromUrl($oldProgram['image_source'])->toMediaCollection('cover'),
                    report: false
                );
            }
            foreach ($oldProgram['emissions'] as $oldPodcast) {
                $this->importPodcast($program, $oldPodcast);
                if (app()->environment('local')) {
                    break;
                }
            }
            $progressBar->advance();
        }
        $progressBar->finish();
        $progressBar->clear();
        $this->command->call(ResetPodcastsIndex::class);
    }

    protected function importPodcast(Program $program, array $oldPodcast): void
    {
        $radioStations = RadioStation::all();
        $podcast = $program->podcasts()->create([
            'title' => $oldPodcast['title'],
            'tags' => implode(', ', $oldPodcast['tags']),
            'description' => strip_tags(html_entity_decode($oldPodcast['body'] ?? '', ENT_QUOTES)),
            'type' => Podcast::TYPE_REPLAY,
            'winmedia_audio_source_uploaded' => false,
            'duration' => $oldPodcast['date_de_fin'] - $oldPodcast['date_de_debut'],
            'published_at' => Date::parse($oldPodcast['date_de_debut']),
        ]);
        $playlistIds = $oldPodcast['playlist'] ?: [];
        $songIdsWithPivot = collect($playlistIds)
            ->filter(fn (int $id) => Song::find($id) ? true : false)
            ->mapWithKeys(fn (int $songId, int $index) => [$songId => ['index' => $index]])
            ->toArray();
        $podcast->songs()->sync($songIdsWithPivot);
        if ($oldPodcast['image_source'] && $podcast->getMedia('cover')->isEmpty()) {
            rescue(
                static fn () => $podcast->addMediaFromUrl($oldPodcast['image_source'])->toMediaCollection('cover'),
                report: false
            );
        }
        if ($oldPodcast['son'] && $podcast->getMedia('audio')->isEmpty()) {
            $fileHeaders = get_headers($oldPodcast['son'], true);
            if (
                ($fileHeaders['Content-Length'] ?? null)
                && $fileHeaders['Content-Length'] <= config('media-library.max_file_size')
            ) {
                rescue(
                    static fn () => $podcast->addMediaFromUrl($oldPodcast['son'])->toMediaCollection('audio'),
                    report: false
                );
            }
        }
        $podcastRadioStations = $radioStations->whereIn('winmedia_id', $oldPodcast['stations']);
        $podcast->radioStations()->sync($podcastRadioStations->pluck('id')->toArray());
    }
}

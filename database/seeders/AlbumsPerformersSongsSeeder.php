<?php

namespace Database\Seeders;

use App\Console\Commands\Elasticsearch\ResetPerformersIndex;
use App\Console\Commands\Elasticsearch\ResetSongsIndex;
use App\Models\Audio\Song;
use App\Models\Map\Place;
use App\Models\Performers\Album;
use App\Models\Performers\Performer;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class AlbumsPerformersSongsSeeder extends Seeder
{
    /** @throws \Exception */
    public function run(): void
    {
        if (app()->environment('local')) {
            $this->createDefaultsImages();
            Album::factory()
                ->withMedia()
                ->withPerformer()
                ->withSongs()
                ->count(10)
                ->create();

            Album::factory()
                ->withMedia()
                ->withPerformer()
                ->withPlace(Place::with('points')->get())
                ->withSongs()
                ->count(10)
                ->create();

            $performers = Song::select(['performer'])->distinct()->get();
            foreach (Song::cursor() as $song) {
                $song->update([
                    '_artistes_similaires' => $performers
                        ->random(random_int(0, min(3, $performers->count())))
                        ->implode('performer', '/')
                        ?: null,
                ]);
            }

            Song::factory()->sonoreType()->withMedia()->count(15)->create();
            Song::factory()->archivageType()->withMedia()->count(10)->create();

            $performer = Performer::factory()->withMedia()->create();
            $album = Album::factory()->withMedia()->withPerformer($performer)->create();
            Song::factory()->withMedia()->withPerformer($performer)->withAlbum($album)->withLabel()->count(2)->create();
            Song::factory()->withMedia()->withPerformer($performer)->withAlbum($album)->withLabel()->count(2)->create();
            Song::factory()->withMedia()->withPerformer($performer)->withAlbum($album)->withLabel()->count(1)->create();
        }

        $this->command->call(ResetSongsIndex::class);
        $this->command->call(ResetPerformersIndex::class);
    }

    protected function createDefaultsImages(): void
    {
        Storage::makeDirectory('public/winmedia/pochettes_thumbnail');
        File::copy(
            resource_path('seeds/default-winmedia/pochettes-thumbnail/noimage.jpg'),
            Storage::path('public/winmedia/pochettes_thumbnail/noimage.jpg')
        );
        File::copy(
            resource_path('seeds/default-winmedia/pochettes-thumbnail/noimagesonore.jpg'),
            Storage::path('public/winmedia/pochettes_thumbnail/noimagesonore.jpg')
        );
        Storage::makeDirectory('public/winmedia/pochettes_middle');
        File::copy(
            resource_path('seeds/default-winmedia/pochettes-middle/noimage.jpg'),
            Storage::path('public/winmedia/pochettes_middle/noimage.jpg')
        );
        File::copy(
            resource_path('seeds/default-winmedia/pochettes-middle/noimagesonore.jpg'),
            Storage::path('public/winmedia/pochettes_middle/noimagesonore.jpg')
        );
        Storage::makeDirectory('public/winmedia/pochettes_brut');
        File::copy(
            resource_path('seeds/default-winmedia/pochettes-brut/noimage.jpg'),
            Storage::path('public/winmedia/pochettes_brut/noimage.jpg')
        );
        File::copy(
            resource_path('seeds/default-winmedia/pochettes-brut/noimagesonore.jpg'),
            Storage::path('public/winmedia/pochettes_brut/noimagesonore.jpg')
        );
        Storage::makeDirectory('public/winmedia/images_albums');
        File::copy(
            resource_path('seeds/default-winmedia/images-albums/noimage.jpg'),
            Storage::path('public/winmedia/images_albums/noimage.jpg')
        );
        Storage::makeDirectory('public/winmedia/performers_thumbnail');
        File::copy(
            resource_path('seeds/default-winmedia/performers-thumbnail/noimage.jpg'),
            Storage::path('public/winmedia/performers_thumbnail/noimage.jpg')
        );
    }
}

<?php

namespace Database\Seeders;

use App\Models\Pages\Page;
use Illuminate\Database\Seeder;

class PagesSeeder extends Seeder
{
    public function run(): void
    {
        $this->aboutUs();
        $this->pressArea();
        $this->termsOfService();
        $this->gdpr();
        $this->listenUs();
        $this->supportUs();
    }

    protected function aboutUs(): void
    {
        Page::factory()
            ->withSpacerBrick('xl')
            ->withTitle<PERSON>rick(title: 'À propos', typeKey: 'h1', styleKey: 'h1')
            ->withSpacerBrick('sm')
            ->withOneTextColumnBrick()
            ->withSpacerBrick('xl')
            ->withSeoMeta('À propos')
            ->create([
                'unique_key' => 'about_us',
                'nav_title' => 'À propos',
                'active' => true,
            ]);
    }

    protected function pressArea(): void
    {
        Page::factory()
            ->withSpacer<PERSON><PERSON>('xl')
            ->withTitleBrick(title: 'Espace presse', typeKey: 'h1', styleKey: 'h1')
            ->withSpacer<PERSON>rick('sm')
            ->withOneTextColumnBrick()
            ->withSpacerBrick('xl')
            ->withSeoMeta('Espace presse')
            ->create([
                'unique_key' => 'press_area',
                'nav_title' => 'Espace presse',
                'active' => true,
            ]);
    }

    protected function termsOfService(): void
    {
        Page::factory()
            ->withSpacerBrick('xl')
            ->withTitleBrick(title: 'Mentions légales', typeKey: 'h1', styleKey: 'h1')
            ->withSpacerBrick('sm')
            ->withOneTextColumnBrick()
            ->withSpacerBrick('xl')
            ->withSeoMeta('Mentions légales')
            ->create([
                'unique_key' => 'terms_of_service_page',
                'nav_title' => 'Mentions légales',
                'active' => true,
            ]);
    }

    protected function gdpr(): void
    {
        Page::factory()
            ->withSpacerBrick('xl')
            ->withTitleBrick(title: 'Vie privée et RGPD', typeKey: 'h1', styleKey: 'h1')
            ->withSpacerBrick('sm')
            ->withOneTextColumnBrick()
            ->withSpacerBrick('xl')
            ->withSeoMeta('Vie privée et RGPD')
            ->create([
                'unique_key' => 'gdpr_page',
                'nav_title' => 'Vie privée et RGPD',
                'active' => true,
            ]);
    }

    protected function listenUs(): void
    {
        Page::factory()
            ->withSpacerBrick('xl')
            ->withTitleBrick(title: 'Nous écouter', typeKey: 'h1', styleKey: 'h1')
            ->withSpacerBrick('sm')
            ->withOneTextColumnBrick()
            ->withSpacerBrick('xl')
            ->withSeoMeta('Nous écouter')
            ->create([
                'unique_key' => 'listen_us',
                'nav_title' => 'Nous écouter',
                'active' => true,
            ]);
    }

    protected function supportUs(): void
    {
        Page::factory()
            ->withSpacerBrick('xl')
            ->withTitleBrick(title: 'Nous soutenir', typeKey: 'h1', styleKey: 'h1')
            ->withSpacerBrick('sm')
            ->withOneTextColumnBrick()
            ->withSpacerBrick('xl')
            ->withSeoMeta('Nous soutenir')
            ->create([
                'unique_key' => 'support_us',
                'nav_title' => 'Nous soutenir',
                'active' => true,
            ]);
    }

    protected function shop(): void
    {
        Page::factory()
            ->withSpacerBrick('xl')
            ->withTitleBrick(title: 'Boutique', typeKey: 'h1', styleKey: 'h1')
            ->withSpacerBrick('sm')
            ->withOneTextColumnBrick()
            ->withSpacerBrick('xl')
            ->withSeoMeta('Boutique')
            ->create([
                'unique_key' => 'shop',
                'nav_title' => 'Boutique',
                'active' => true,
            ]);
    }
}

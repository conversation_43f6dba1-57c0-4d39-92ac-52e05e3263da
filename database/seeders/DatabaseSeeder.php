<?php

namespace Database\Seeders;

use App\Console\Commands\Programs\GeneratePodcasts;
use Illuminate\Cache\Console\ClearCommand;
use Illuminate\Database\Seeder;
use Illuminate\Foundation\Console\DownCommand;
use Illuminate\Foundation\Console\UpCommand;
use Illuminate\Support\Facades\Storage;
use Laravel\Horizon\Console\PurgeCommand;
use Spatie\MediaLibrary\MediaCollections\Commands\CleanCommand;

class DatabaseSeeder extends Seeder
{
    /** @throws \Exception */
    public function run(): void
    {
        $this->command->call(DownCommand::class); // To avoid scheduled job to run
        $this->command->call(PurgeCommand::class); // Purge Horizon jobs
        $this->command->call(CleanCommand::class); // Clean default disk
        $this->command->call(CleanCommand::class, [ // Clean private disk
            'modelType' => null,
            'collectionName' => null,
            'disk' => 'private',
        ]);
        $this->command->call(ClearCommand::class); // Release commands mutex by clearing cache
        config()->set('media-library.queue_conversions_by_default', true);
        if (app()->environment('local')) {
            Storage::deleteDirectory('public/winmedia');
        }
        $this->call(CookieCategoriesSeeder::class);
        $this->call(CookieServicesSeeder::class);
        $this->call(PlacesSeeder::class);
        $this->call(SettingsSeeder::class);
        $this->call(TeamsSeeder::class);
        $this->call(UsersSeeder::class);
        $this->call(PagesSeeder::class);
        $this->call(PageContentsSeeder::class);
        $this->call(RadioStationsSeeder::class);
        $this->call(ThematicsSeeder::class);
        $this->call(ProgramsSeeder::class);
        $this->call(PodcastsSeeder::class);
        $this->call(NewsArticlesSeeder::class);
        $this->call(AlbumsPerformersSongsSeeder::class);
        $this->call(PublicPlaylistsSeeder::class);
        $this->call(LiveBroadcastingsSeeder::class);
        $this->call(EventsSeeder::class);
        $this->call(AnnouncementsSeeder::class);
        $this->command->call(GeneratePodcasts::class);
        $this->command->call(ClearCommand::class); // Force cache renewal on first page load
        $this->command->call(UpCommand::class); // Set app accessible
    }
}

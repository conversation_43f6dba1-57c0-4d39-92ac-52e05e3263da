<?php

namespace Database\Seeders;

use App\Console\Commands\Elasticsearch\ResetPodcastsIndex;
use App\Models\Audio\Podcast;
use App\Models\Map\Place;
use Illuminate\Database\Seeder;

class PodcastsSeeder extends Seeder
{
    public function run(): void
    {
        if (! app()->environment('local')) {
            $this->command->call(ResetPodcastsIndex::class);

            return;
        }

        Podcast::factory()
            ->withMedia()
            ->count(20)
            ->create();

        Podcast::factory()
            ->withMedia()
            ->withPlace(Place::with('points')->get())
            ->count(10)
            ->create();

        $this->command->call(ResetPodcastsIndex::class);
    }
}

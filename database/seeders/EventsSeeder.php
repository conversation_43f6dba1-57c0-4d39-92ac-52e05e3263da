<?php

namespace Database\Seeders;

use App\Console\Commands\Elasticsearch\ResetEventsIndex;
use App\Models\Events\Event;
use App\Models\Map\Place;
use Illuminate\Database\Seeder;

class EventsSeeder extends Seeder
{
    public function run(): void
    {
        if (! app()->environment('local')) {
            $this->command->call(ResetEventsIndex::class);

            return;
        }

        $this->callOnce(PlacesSeeder::class);

        Event::factory(20)
            ->withRadioStations()
            ->withPlace(Place::with('points')->get())
            ->withMedia()
            ->active()
            ->create();

        $this->command->call(ResetEventsIndex::class);
    }
}

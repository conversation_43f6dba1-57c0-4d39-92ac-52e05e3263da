<?php

namespace Database\Seeders;

use App\Console\Commands\Elasticsearch\ResetPlaylistsIndex;
use App\Models\Audio\Playlist;
use App\Models\Map\Place;
use Illuminate\Database\Seeder;

class PublicPlaylistsSeeder extends Seeder
{
    public function run(): void
    {
        $this->callOnce(PlacesSeeder::class);

        Playlist::factory(12)
            ->public()
            ->withPlace(Place::with('points')->get())
            ->withRadioStations()
            ->withMedia()
            ->withSongs()
            ->create();

        $this->command->call(ResetPlaylistsIndex::class);
    }
}

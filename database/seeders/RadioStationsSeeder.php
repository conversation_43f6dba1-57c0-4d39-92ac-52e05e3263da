<?php

namespace Database\Seeders;

use App\Models\Map\Place;
use App\Models\Map\PlaceType;
use App\Models\Radio\RadioStation;
use Illuminate\Database\Seeder;

class RadioStationsSeeder extends Seeder
{
    public function run(): void
    {
        $radioStations = [
            [
                'winmedia_id' => 2,
                'name' => 'SUN Nantes',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sunhd.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sun.flac',
                'latitude' => 47.218372,
                'longitude' => -1.553621,
                'color' => '#FFD500',
            ],
            [
                'winmedia_id' => 3,
                'name' => 'SUN Cholet',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/webradio-cholet.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/webradio-cholet.flac',
                'latitude' => 47.057869,
                'longitude' => -0.879450,
                'color' => '#FFD500',
            ],
            [
                'winmedia_id' => 5,
                'name' => 'SUN Saint-Nazaire',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/webradio-saintnazaire.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/webradio-saintnazaire.flac',
                'latitude' => 47.274330,
                'longitude' => -2.213740,
                'color' => '#FFD500',
            ],
            [
                'winmedia_id' => 11,
                'name' => 'SUN Junior',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sun-junior.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sun-junior.flac',
                'color' => '#EA5C2F',
            ],
            [
                'winmedia_id' => 8,
                'name' => 'SUN Sport',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sunsport.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sunsport.flac',
                'color' => '#27A898',
            ],
            [
                'winmedia_id' => 9,
                'name' => 'SUN Metal',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sun-metal.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sun-metal.flac',
                'color' => '#3D4850',
            ],
            [
                'winmedia_id' => 10,
                'name' => 'SUN Nouvo',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sun-nouvo.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sun-nouvo.flac',
                'color' => '#D8505A',
            ],
            [
                'winmedia_id' => 15,
                'name' => 'SUN Soul & Funk',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sun-soul.funk.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sun-soul.funk.flac',
                'color' => '#896B5B',
            ],
            [
                'winmedia_id' => 14,
                'name' => 'SUN Rock',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sun-rock.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sun-rock.flac',
                'color' => '#8399B1',
            ],
            [
                'winmedia_id' => 12,
                'name' => 'SUN Hip Hop',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sun-hip-hop.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sun-hip-hop.flac',
                'color' => '#E99300',
            ],
            [
                'winmedia_id' => 13,
                'name' => 'SUN Jazz',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sun-jazz.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sun-jazz.flac',
                'color' => '#F4EED5',
            ],
            [
                'winmedia_id' => 16,
                'name' => 'SUN Classique',
                'stream_url_ld' => 'https://diffusion.lafrap.fr/sun-classique.mp3',
                'stream_url_hd' => 'https://diffusion.lafrap.fr/sun-classique.flac',
                'color' => '#64B9E4',
            ],
        ];

        foreach ($radioStations as $data) {
            $factory = RadioStation::factory();

            if (isset($data['longitude'], $data['latitude'])) {
                $place = Place::factory()
                    ->withPoint(
                        longitude: $data['longitude'],
                        latitude: $data['latitude']
                    )
                    ->create([
                        'type' => PlaceType::RadioStudio->value,
                        'name' => $data['name'],
                    ]);

                $factory = $factory->withPlace($place);

                unset($data['longitude'], $data['latitude']);
            }

            $factory->withMedia()->active()->create($data);
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\Radio\LiveBroadcasting;
use App\Models\Radio\RadioStation;
use Illuminate\Database\Seeder;

class LiveBroadcastingsSeeder extends Seeder
{
    public function run(): void
    {
        if (! app()->environment('local')) {
            return;
        }
        foreach (RadioStation::all() as $radioStation) {
            LiveBroadcasting::factory()->count(40)->create(['winmedia_radio_station_id' => $radioStation->winmedia_id]);
        }
    }
}

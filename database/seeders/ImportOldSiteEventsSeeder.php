<?php

namespace Database\Seeders;

use App\Console\Commands\Elasticsearch\ResetEventsIndex;
use App\Models\Audio\Thematic;
use App\Models\Events\Event;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Http;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\ConsoleOutput;

class ImportOldSiteEventsSeeder extends Seeder
{
    /**
     * @throws \JsonException
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function run(): void
    {
        $thematics = Thematic::all();
        $users = User::all();
        $radioStations = RadioStation::all();
        Config::set('media-library.max_file_size', 1024 * 1024 * 600);
        $oldEvents = json_decode(
            file_get_contents(resource_path('seeds/old-site/events.json')),
            true,
            512,
            JSON_THROW_ON_ERROR
        );
        $output = new ConsoleOutput();
        $progressBar = new ProgressBar($output, count($oldEvents));
        foreach (collect($oldEvents)->sortBy('created')->values() as $index => $oldEvent) {
            if ($index > 19 && app()->environment('local')) {
                break;
            }
            $eventThematicID = $thematics->where('title', $oldEvent['categorie'])->first()?->id;
            $eventUserID = isset($oldEvent['authorMail'])
                            ? $users->where('email', $oldEvent['authorMail'])->first()?->id
                            : null;
            $event = $this->importEvent($oldEvent, $eventThematicID, $eventUserID);
            if ($oldEvent['image_source'] && $event->getMedia('cover')->isEmpty()) {
                $event->addMediaFromUrl($oldEvent['image_source'])->toMediaCollection('cover');
            }
            if ($oldEvent['son'] && $event->getMedia('audio')->isEmpty()) {
                $this->importAudio($event, $oldEvent['son']);
            }
            $eventRadioStations = $radioStations->whereIn('winmedia_id', $oldEvent['stations']);
            $event->radioStations()->sync($eventRadioStations->pluck('id')->toArray());
            $progressBar->advance();
        }
        $progressBar->finish();
        $progressBar->clear();
        $this->command->call(ResetEventsIndex::class);
    }

    /** @throws \Exception */
    public function importEvent(array $oldEvent, ?int $thematicId, ?int $userId): Event
    {
        if ($oldEvent['geo_latitude'] && $oldEvent['geo_longitude']) {
            $locationName = $this->getLocationLabel($oldEvent['geo_latitude'], $oldEvent['geo_longitude']);
        }
        if (app()->environment('local')) {
            $now = Date::now('Europe/Paris')->addDays(random_int(0, 10))->startOfDay();
            $startedAt = $now->setTimezone('UTC');
            $endedAt = $now->addDays(random_int(0, 5))->setTimezone('UTC');
        } else {
            $startedAt = Date::parse($oldEvent['date_de_debut']);
            $endedAt = Date::parse($oldEvent['date_de_fin']);
        }

        return Event::firstOrCreate(['title' => $oldEvent['title']], [
            'thematic_id' => $thematicId,
            'user_id' => $userId,
            'title' => $oldEvent['title'],
            'description' => strip_tags(html_entity_decode($oldEvent['body'] ?? '', ENT_QUOTES)),
            'tags' => implode(', ', $oldEvent['tags']),
            'location_name' => $locationName ?? null,
            'location_latitude' => $oldEvent['geo_latitude'],
            'location_longitude' => $oldEvent['geo_longitude'],
            'started_at' => $startedAt,
            'ended_at' => $endedAt,
            'active' => true,
        ]);
    }

    /** @throws \JsonException */
    protected function getLocationLabel(string $latitude, string $longitude): ?string
    {
        $apiRequest = Http::get('https://geo.api.gouv.fr/communes', ['lat' => $latitude, 'lon' => $longitude]);
        if ($apiRequest->successful()) {
            return json_decode($apiRequest->body(), true, 512, JSON_THROW_ON_ERROR)[0]['nom'] ?? null;
        }

        return null;
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    protected function importAudio(Event $event, $audioUrl): void
    {
        $fileHeaders = get_headers($audioUrl, true);
        if (
            ($fileHeaders['Content-Length'] ?? null)
            && $fileHeaders['Content-Length'] <= config('media-library.max_file_size')
        ) {
            rescue(static fn () => $event->addMediaFromUrl($audioUrl)->toMediaCollection('audio'), report: false);
        }
    }
}

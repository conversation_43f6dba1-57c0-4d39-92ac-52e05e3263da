<?php

namespace Database\Seeders;

use App\Console\Commands\Elasticsearch\ResetNewsArticlesIndex;
use App\Models\Audio\Thematic;
use App\Models\News\NewsArticle;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Symfony\Component\Console\Helper\ProgressBar;
use Symfony\Component\Console\Output\ConsoleOutput;

class ImportOldSiteNewsSeeder extends Seeder
{
    /**
     * @throws \JsonException
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function run(): void
    {
        Config::set('media-library.max_file_size', 1024 * 1024 * 600);
        $thematics = Thematic::all();
        $users = User::all();
        $radioStations = RadioStation::all();
        $oldArticles = json_decode(
            file_get_contents(resource_path('seeds/old-site/news.json')),
            true,
            512,
            JSON_THROW_ON_ERROR
        );
        $output = new ConsoleOutput();
        $progressBar = new ProgressBar($output, count($oldArticles));
        foreach (collect($oldArticles)->sortBy('created')->values() as $index => $oldArticle) {
            if ($index > 9 && app()->environment('local')) {
                break;
            }
            if (! $oldArticle['image_source']) {
                continue;
            }
            if ($oldArticle['geo_latitude'] && $oldArticle['geo_longitude']) {
                $apiRequest = Http::get('https://geo.api.gouv.fr/communes', [
                    'lat' => $oldArticle['geo_latitude'],
                    'lon' => $oldArticle['geo_longitude'],
                ]);
                if ($apiRequest->successful()) {
                    $locationName = json_decode($apiRequest->body(), true, 512, JSON_THROW_ON_ERROR)[0]['nom'] ?? null;
                }
            }
            $article = NewsArticle::firstOrCreate(['title' => $oldArticle['title']], [
                'title' => $oldArticle['title'],
                'slug' => Str::slug($oldArticle['title']),
                'description' => $oldArticle['body'],
                'tags' => implode(', ', $oldArticle['tags']),
                'location_name' => $locationName ?? null,
                'location_latitude' => $oldArticle['geo_latitude'],
                'location_longitude' => $oldArticle['geo_longitude'],
                'thematic_id' => $thematics->where('title', $oldArticle['categorie'])->first()?->id,
                'user_id' => isset($oldArticle['authorMail'])
                                ? $users->where('email', $oldArticle['authorMail'])->first()?->id
                                : null,
                'published_at' => Date::parse($oldArticle['created']),
                'active' => true,
            ]);
            $article->saveSeoMeta([
                'meta_title' => $oldArticle['title'],
                'meta_description' => Str::limit(strip_tags($oldArticle['body']), 150),
            ]);
            if ($article->getMedia('illustrations')->isEmpty()) {
                rescue(
                    static function () use ($article, $oldArticle) {
                        $article->addMediaFromUrl($oldArticle['image_source'])->toMediaCollection('illustrations');
                        $article->addMediaFromUrl($oldArticle['image_source'])->toMediaCollection('seo');
                    },
                    report: false
                );
            }
            if ($oldArticle['son'] && $article->getMedia('audio')->isEmpty()) {
                $this->importAudio($article, $oldArticle['son']);
            }
            $articleRadioStations = $radioStations->whereIn(
                'winmedia_id',
                array_filter(explode(';', $oldArticle['stations']))
            );
            $article->radioStations()->sync($articleRadioStations->pluck('id')->toArray());
            $progressBar->advance();
        }
        $progressBar->finish();
        $progressBar->clear();
        $this->command->call(ResetNewsArticlesIndex::class);
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileCannotBeAdded
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    protected function importAudio(NewsArticle $article, $audioUrl): void
    {
        $fileHeaders = get_headers($audioUrl, true);
        if (
            ($fileHeaders['Content-Length'] ?? null)
            && $fileHeaders['Content-Length'] <= config('media-library.max_file_size')
        ) {
            rescue(
                static fn () => $article->addMediaFromUrl($audioUrl)->toMediaCollection('audio'),
                report: false
            );
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\Map\Place;
use Database\Factories\Map\PlaceFactory;
use Illuminate\Database\Seeder;

class PlacesSeeder extends Seeder
{
    public function run(): void
    {
        Place::factory()->withPoint(PlaceFactory::AREA_ANGERS)->count(15)->create();
        Place::factory()->withPoint(PlaceFactory::AREA_CHOLET)->count(15)->create();
        Place::factory()->withPoint(PlaceFactory::AREA_NANTES)->count(15)->create();
    }
}

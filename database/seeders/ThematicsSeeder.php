<?php

namespace Database\Seeders;

use App\Models\Audio\Thematic;
use Illuminate\Database\Eloquent\Factories\Sequence;
use Illuminate\Database\Seeder;

class ThematicsSeeder extends Seeder
{
    public function run(): void
    {
        Thematic::factory()->withMedia()->count(5)->state(new Sequence(
            ['title' => 'Société'],
            ['title' => 'Culture'],
            ['title' => 'Sport'],
            ['title' => 'Technologie'],
            ['title' => 'Nature']
        ))->create();
    }
}

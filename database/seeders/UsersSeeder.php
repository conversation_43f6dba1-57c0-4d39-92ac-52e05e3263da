<?php

namespace Database\Seeders;

use App\Models\Teams\Team;
use App\Models\Users\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Hash;

class UsersSeeder extends Seeder
{
    public function run(): void
    {
        $avatars = array_values(array_diff(scandir(resource_path('seeds/default-avatars')), ['.', '..']));
        $randomAvatar = $avatars[rand(0, count($avatars) - 1)];
        User::factory()->withMedia(resource_path('seeds/default-avatars') . '/' . $randomAvatar)
            ->withSubscribedPrograms()
            ->create([
                'team_id' => Team::where('unique_key', 'admin')->sole()->id,
                'username' => 'ACID-Solutions',
                'email' => '<EMAIL>',
                'email_verified_at' => Date::now(),
                'password' => Hash::make('secret'),
            ]);
        foreach (Team::where('unique_key', '!=', 'admin')->get() as $team) {
            $randomAvatar = $avatars[rand(0, count($avatars) - 1)];
            User::factory()->withMedia(resource_path('seeds/default-avatars') . '/' . $randomAvatar)
                ->withSubscribedPrograms()
                ->create([
                    'team_id' => $team->id,
                    'username' => 'Utilisateur ' . $team->name,
                    'email' => $team->unique_key . '@fake.acid.fr',
                    'email_verified_at' => Date::now(),
                    'password' => Hash::make('secret'),
                ]);
        }
    }
}

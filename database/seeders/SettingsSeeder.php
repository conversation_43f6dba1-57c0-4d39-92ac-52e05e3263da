<?php

namespace Database\Seeders;

use App\Models\Settings\Settings;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    public function run(): void
    {
        Settings::factory()
            ->withMedia(['logo_squared' => resource_path('seeds/logo-sun.png')])
            ->create([
                'matomo_url' => null,
                'matomo_id_site' => null,
                'ga4_tracking_id' => null,
                'facebook_url' => 'https://www.facebook.com/lesonunique',
                'twitter_url' => 'https://twitter.com/lesonunique',
                'youtube_url' => 'https://www.youtube.com/user/lesonunique',
                'bluesky_url' => 'https://bsky.app/profile/lesonunique.com',
                'mastodon_url' => null,
                'linkedin_url' => 'https://fr.linkedin.com/company/sunlesonunique',
                'instagram_url' => 'https://www.instagram.com/lesonunique',
                'play_store_app_url' => 'https://play.google.com/store/apps/details?id=fr.frap.sunradio&hl=fr',
                'app_store_app_url' => 'https://itunes.apple.com/fr/app/mysun/id937219562?mt=8',
            ]);
    }
}

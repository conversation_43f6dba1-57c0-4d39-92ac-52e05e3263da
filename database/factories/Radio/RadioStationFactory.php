<?php

namespace Database\Factories\Radio;

use App\Models\Radio\RadioStation;
use Database\Factories\Traits\HasLocation;
use Illuminate\Database\Eloquent\Factories\Factory;

class RadioStationFactory extends Factory
{
    use HasLocation;

    /** @var string */
    protected $model = RadioStation::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->word,
            'label' => $this->faker->word,
            'winmedia_id' => $this->faker->unique()->numberBetween(),
            'stream_url_ld' => $this->faker->url,
            'stream_url_hd' => $this->faker->url,
            'color' => $this->faker->hexColor,
            'active' => $this->faker->boolean,
        ];
    }

    public function withMedia(?string $imageUrl = null): self
    {
        return $this->afterCreating(function (RadioStation $radioStation) use ($imageUrl) {
            $radioStation->addMedia($imageUrl ?? $this->faker->image(null, 400, 400))
                ->preservingOriginal()
                ->toMediaCollection('cover');
        });
    }

    public function active(): self
    {
        return $this->state(fn () => ['active' => true]);
    }

    public function inactive(): self
    {
        return $this->state(fn () => ['active' => false]);
    }
}

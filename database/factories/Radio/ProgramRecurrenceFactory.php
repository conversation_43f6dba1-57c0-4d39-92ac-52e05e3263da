<?php

namespace Database\Factories\Radio;

use App\Models\Radio\Program;
use App\Models\Radio\ProgramRecurrence;
use App\Models\Radio\RadioStation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;

class ProgramRecurrenceFactory extends Factory
{
    /** @var string */
    protected $model = ProgramRecurrence::class;

    public function definition(): array
    {
        $timeLocal = Date::createFromFormat('H:i', $this->faker->time('H:i'), 'Europe/Paris');

        return [
            'label' => $this->faker->catchphrase,
            'program_id' => Program::whereNull('main_program_id')->inRandomOrder()->first()->id,
            'months' => $this->faker->boolean
                ? collect(range(1, 12))->shuffle()->take($this->faker->numberBetween(1, 6))->toArray()
                : [],
            'month_days' => $this->faker->boolean
                ? collect(range(1, 31))->shuffle()->take($this->faker->numberBetween(1, 15))->toArray()
                : [],
            'week_days' => $this->faker->boolean
                ? collect(range(1, 7))->shuffle()->take($this->faker->numberBetween(1, 4))->toArray()
                : [],
            'time' => $timeLocal->timezone('UTC')->format('H:i'),
            'local_time' => $timeLocal->timezone('Europe/Paris')->format('H:i'),
            'local_time_tz' => 'Europe/Paris',
            'month_week' => $this->faker->boolean
                ? collect(range(1, 5))->shuffle()->take($this->faker->numberBetween(1, 5))->toArray()
                : [],
        ];
    }

    public function withProgram(Program $program): self
    {
        return $this->state(fn () => ['program_id' => $program->id]);
    }

    public function withSubPrograms(Collection $subPrograms): self
    {
        return $this->afterCreating(function (ProgramRecurrence $programRecurrence) use ($subPrograms) {
            $subProgramIdsWithPivot = $subPrograms
                ->mapWithKeys(function (Program $subProgram) use ($programRecurrence) {
                    $recurrenceTime = Date::createFromFormat(
                        'H:i',
                        $programRecurrence->local_time->format('H:i'),
                        'Europe/Paris'
                    );
                    $subProgramTime = $recurrenceTime->addMinutes($this->faker->numberBetween(15, 90));

                    return [
                        $subProgram['id'] => [
                            'time' => $subProgramTime->setTimezone('UTC')->format('H:i'),
                            'local_time' => $subProgramTime->setTimezone('Europe/Paris')->format('H:i'),
                            'local_time_tz' => 'Europe/Paris',
                        ],
                    ];
                })
                ->toArray();
            $programRecurrence->subPrograms()->sync($subProgramIdsWithPivot);
        });
    }

    public function withRadioStations(?array $radioStationIds = null): self
    {
        return $this->afterCreating(function (ProgramRecurrence $programRecurrence) use ($radioStationIds) {
            if (! $radioStationIds) {
                $radioStationIds = RadioStation::inRandomOrder()
                    ->limit($this->faker->numberBetween(1, round(RadioStation::count() / 4)))
                    ->pluck('id')
                    ->toArray();
            }
            $programRecurrence->radioStations()->sync($radioStationIds);
        });
    }
}

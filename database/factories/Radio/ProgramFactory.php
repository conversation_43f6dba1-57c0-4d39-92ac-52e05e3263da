<?php

namespace Database\Factories\Radio;

use App\Models\Audio\Thematic;
use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Radio\Program;
use App\Models\Radio\ProgramRecurrence;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProgramFactory extends Factory
{
    /** @var string */
    protected $model = Program::class;

    /** @throws \Exception */
    public function definition(): array
    {
        return [
            'thematic_id' => Thematic::inRandomOrder()->first()->id,
            'title' => $this->faker->unique()->catchphrase,
            'tags' => implode(', ', $this->faker->words(random_int(1, 3))),
            'description' => $this->faker->realText(300),
            'duration' => $this->faker->numberBetween(180, 5400), // In seconds => between 3min and 1h30
            'start_podcast_title' => $this->faker->word,
            'start_podcast_artist' => $this->faker->name,
            'start_podcast_version' => 'v' . $this->faker->randomFloat(1),
            'end_podcast_title' => $this->faker->word,
            'end_podcast_artist' => $this->faker->name,
            'end_podcast_version' => 'v' . $this->faker->randomFloat(1),
        ];
    }

    public function withThematic(Thematic $thematic): self
    {
        return $this->state(fn () => ['thematic_id' => $thematic->id]);
    }

    public function withAuthors(array $authorsIds = []): self
    {
        return $this->afterCreating(function (Program $program) use ($authorsIds) {
            $authorsIdsWithPivot = collect($authorsIds)->mapWithKeys(fn (
                int $songId,
                int $index
            ) => [$songId => ['index' => $index]])->toArray();
            $program->authors()->sync($authorsIdsWithPivot);
        });
    }

    public function withPlace(?Place $place = null): self
    {
        return $this->afterCreating(function (Program $program) use ($place) {
            if (! $place) {
                $place = Place::factory()->withPoint()->create();
            }

            $contentLocation = new ContentLocation();
            $contentLocation->content()->associate($program);
            $contentLocation->location()->associate($place);
            $contentLocation->save();
        });
    }

    public function withSubPrograms(?int $subProgramsCount = null): self
    {
        return $this->afterCreating(fn (Program $program) => Program::factory()
            ->count($subProgramsCount ?: random_int(3, 7))
            ->withMedia()
            ->create(['main_program_id' => $program->id]));
    }

    public function withMedia(?string $imageUrl = null): self
    {
        return $this->afterCreating(function (Program $program) use ($imageUrl) {
            $program->addMedia($imageUrl ?? $this->faker->image(null, 1400, 1400))
                ->preservingOriginal()
                ->toMediaCollection('cover');
        });
    }

    public function withRecurrences(?int $recurrencesCount = null): self
    {
        return $this->afterCreating(function (Program $program) use ($recurrencesCount) {
            // Forbid recurrences for sub-programs
            if ($program->main_program_id) {
                return;
            }
            ProgramRecurrence::factory()
                ->withSubPrograms($program->subPrograms->isNotEmpty()
                    ? $program->subPrograms->random(min($program->subPrograms->count(), 3))
                    : collect())
                ->withRadioStations()
                ->count($recurrencesCount ?: random_int(5, 8))
                ->create(['program_id' => $program->id]);
        });
    }
}

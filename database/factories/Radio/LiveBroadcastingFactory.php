<?php

namespace Database\Factories\Radio;

use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Radio\LiveBroadcasting;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class LiveBroadcastingFactory extends Factory
{
    protected $model = LiveBroadcasting::class;

    public function definition(): array
    {
        return [
            'winmedia_radio_station_id' => RadioStation::inRandomOrder()->first()->winmedia_id,
            'song_id' => Song::inRandomOrder()->first()->id,
            'real_duration' => $this->faker->numberBetween(120000, 420000), // Milliseconds => between 2 and 7 minutes
            'started_at' => $this->faker->dateTimeBetween('-2 days'),
        ];
    }

    public function withRadioStation(RadioStation $radioStation): self
    {
        return $this->state(fn () => ['winmedia_radio_station_id' => $radioStation->winmedia_id]);
    }

    public function withSong(Song $song): self
    {
        return $this->state(fn () => ['song_id' => $song->id]);
    }

    public function withPodcast(Podcast $podcast): self
    {
        return $this->state(fn () => [
            'song_id' => null,
            'podcast_id' => $podcast->id,
        ]);
    }

    public function withDedicationUser(?int $userId = null): self
    {
        $user = $userId ? User::findOrFail($userId) : User::inRandomOrder()->first();

        return $this->state(fn () => ['dedication_user_id' => $user->id]);
    }
}

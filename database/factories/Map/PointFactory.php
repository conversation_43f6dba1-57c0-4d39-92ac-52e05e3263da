<?php

namespace Database\Factories\Map;

use App\Models\Map\Place;
use App\Models\Map\Point;
use Illuminate\Database\Eloquent\Factories\Factory;

class PointFactory extends Factory
{
    protected $model = Point::class;

    public function definition(): array
    {
        return [
            'coord' => [
                'type' => 'Point',
                'coordinates' => [$this->faker->longitude(), $this->faker->latitude()],
            ],
            'place_id' => Place::inRandomOrder()->first()?->id,
            'since' => null, // $this->faker->dateTimeBetween('-1 year', 'now')
            'until' => null, // $this->faker->dateTimeBetween('now', '+1 year')
        ];
    }
}

<?php

namespace Database\Factories\Map;

use App\Models\Map\Place;
use App\Models\Map\PlaceType;
use App\Models\Map\Point;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

/**
 * @method Place create()
 */
class PlaceFactory extends Factory
{
    public const AREA_ANGERS = 'angers';

    public const AREA_CHOLET = 'cholet';

    public const AREA_NANTES = 'nantes';

    public const AREAS = [
        self::AREA_ANGERS => [
            'minLongitude' => -0.6925868176605547,
            'maxLongitude' => -0.4444375659189177,
            'minLatitude' => 47.40350108024421,
            'maxLatitude' => 47.525614869009615,
        ],
        self::AREA_CHOLET => [
            'minLongitude' => -0.9531690564814141,
            'maxLongitude' => -0.7919055338436465,
            'minLatitude' => 47.021253640489704,
            'maxLatitude' => 47.09372640309428,
        ],
        self::AREA_NANTES => [
            'minLongitude' => -1.779477928989877,
            'maxLongitude' => -1.3225229724577048,
            'minLatitude' => 47.11424364860531,
            'maxLatitude' => 47.328203663263906,
        ],
    ];

    protected $model = Place::class;

    public function definition(): array
    {
        return [
            'name' => implode(' ', $this->faker->words(random_int(1, 3))),
            'type' => Arr::random(PlaceType::cases()),
            'addr_street1' => $this->faker->streetAddress(),
            'addr_city' => $this->faker->city(),
        ];
    }

    public function withPoint(
        ?string $area = null,
        ?float $longitude = null,
        ?float $latitude = null,
        ?\DateTimeInterface $since = null,
        ?\DateTimeInterface $until = null,
    ): static {
        return $this->afterCreating(function (Place $place) use ($area, $longitude, $latitude, $since, $until): void {
            $attr = ['place_id' => $place->id];

            if (isset($longitude, $latitude)) {
                $attr['coord'] = [
                    'type' => 'Point',
                    'coordinates' => [$longitude, $latitude],
                ];
            } else {
                if (! isset($area, self::AREAS[$area])) {
                    $area = \array_rand(self::AREAS);
                }

                $boundaries = self::AREAS[$area];

                $attr['coord'] = [
                    'type' => 'Point',
                    'coordinates' => [
                        $this->faker->longitude(
                            $boundaries['minLongitude'],
                            $boundaries['maxLongitude']
                        ),
                        $this->faker->latitude(
                            $boundaries['minLatitude'],
                            $boundaries['maxLatitude']
                        ),
                    ],
                ];
            }

            if ($since) {
                $attr['since'] = $since;
            }
            if ($until) {
                $attr['until'] = $until;
            }

            Point::factory()->create($attr);
        });
    }
}

<?php

namespace Database\Factories\Map;

use App\Models\Events\Event;
use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Map\Point;
use App\Models\News\NewsArticle;
use App\Models\Radio\RadioStation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

class ContentLocationFactory extends Factory
{
    protected $model = ContentLocation::class;

    public function definition(): array
    {
        $contentClass = Arr::random([
            Event::class,
            NewsArticle::class,
            RadioStation::class,
        ]);

        $locationClass = Arr::random([
            Place::class,
            Point::class,
        ]);

        return [
            'content_type' => $contentClass,
            'content_id' => $contentClass::factory(),
            'location_type' => $locationClass,
            'location_id' => $locationClass::factory(),
        ];
    }
}

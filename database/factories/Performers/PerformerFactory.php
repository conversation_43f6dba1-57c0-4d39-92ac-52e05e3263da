<?php

namespace Database\Factories\Performers;

use App\Models\Performers\Member;
use App\Models\Performers\Performer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PerformerFactory extends Factory
{
    protected $model = Performer::class;

    public function definition(): array
    {
        return ['name' => $this->faker->unique()->name()];
    }

    public function withMedia(): self
    {
        return $this->afterCreating(function (Performer $performer) {
            $performerNameSlug = Str::slug(Str::ascii($performer->name));
            Storage::makeDirectory('public/winmedia/performers_thumbnail');
            $path = $this->faker->image(Storage::path('public/winmedia/performers_thumbnail'), 300, 300);
            File::move(
                $path,
                Storage::path("public/winmedia/performers_thumbnail/$performerNameSlug.jpg")
            );
        });
    }

    public function withDetail(): self
    {
        return $this->afterCreating(function (Performer $performer) {
            $performer->detail->update([
                'biography' => $this->faker->realText,
                'alias_name' => $this->faker->word,
                'begin_city' => $this->faker->word,
                'current_city' => $this->faker->word,
                'begin_date' => random_int(2000, 2015) . '-01-01',
                'end_date' => random_int(2016, 2024) . '-01-01',
                'genres' => $this->faker->word,
                'url_web' => $this->faker->url(),
                'url_wiki' => $this->faker->url(),
                'url_bandcamp' => $this->faker->url(),
                'url_discogs' => $this->faker->url(),
                'url_instagram' => $this->faker->url(),
                'url_facebook' => $this->faker->url(),
                'url_youtube' => $this->faker->url(),
            ]);
        });
    }

    public function withMembers(array $membersIds = []): self
    {
        return $this->afterCreating(function (Performer $performer) use ($membersIds) {
            $membersIdsWithPivot = $membersIds
                ? collect($membersIds)
                    ->mapWithKeys(fn (int $memberId, int $index) => [$memberId => ['index' => $index]])
                    ->toArray()
                : Member::inRandomOrder()
                    ->select('id')
                    ->limit($this->faker->numberBetween(1, min(30, Member::count())))
                    ->get()
                    ->mapWithKeys(fn (Member $member, int $index) => [$member->id => ['index' => $index]])
                    ->toArray();
            $performer->members()->sync($membersIdsWithPivot);
        });
    }
}

<?php

namespace Database\Factories\Performers;

use App\Models\Performers\Member;
use Illuminate\Database\Eloquent\Factories\Factory;

class MemberFactory extends Factory
{
    protected $model = Member::class;

    public function definition(): array
    {
        return [
            'first_name' => $this->faker->name(),
            'last_name' => $this->faker->name(),
            'nick_name' => $this->faker->name(),
        ];
    }
}

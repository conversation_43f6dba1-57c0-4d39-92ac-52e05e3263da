<?php

namespace Database\Factories\Performers;

use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Performers\Label;
use Illuminate\Database\Eloquent\Factories\Factory;

class LabelFactory extends Factory
{
    protected $model = Label::class;

    public function definition(): array
    {
        return ['name' => $this->faker->unique()->name()];
    }

    public function withPlace(?Place $place = null): self
    {
        return $this->afterCreating(function (Label $label) use ($place) {
            if (! $place) {
                $place = Place::factory(['name' => $label->name])->withPoint()->create();
            }

            $contentLocation = new ContentLocation();
            $contentLocation->content()->associate($label);
            $contentLocation->location()->associate($place);
            $contentLocation->save();
        });
    }
}

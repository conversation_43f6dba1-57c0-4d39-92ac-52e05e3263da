<?php

namespace Database\Factories\Performers;

use App\Models\Audio\Song;
use App\Models\Performers\Album;
use App\Models\Performers\Label;
use App\Models\Performers\Performer;
use Database\Factories\Traits\HasLocation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AlbumFactory extends Factory
{
    use HasLocation;

    protected $model = Album::class;

    public function definition(): array
    {
        return [
            'performer_id' => null,
            'name' => Str::title($this->faker->unique()->words(asText: true)),
            'genre' => Arr::random(['Pop', 'Rap & Hip-Hop', 'Soul', 'Funk', 'Alternative & Punk', 'Folk', 'Rock']),
            'published_at' => $this->faker->dateTime(),
        ];
    }

    public function withPerformer(?Performer $performer = null): self
    {
        return $this->afterMaking(function (Album $album) use ($performer) {
            if (! $performer) {
                $performer = Performer::factory()->withMedia()->create();
            }
            $album->performer_id = $performer->id;
        });
    }

    public function withLabel(?Label $label = null): self
    {
        if (! $label) {
            $label = Label::factory()->create();
        }

        return $this->state(['label_id' => $label->id]);
    }

    public function withSongs(?Collection $songs = null): self
    {
        return $this->afterCreating(function (Album $album) use ($songs) {
            if (! $songs) {
                Song::factory()
                    ->withPerformer($album->performer)
                    ->withAlbum($album)
                    ->withMedia()
                    ->count($this->faker->numberBetween(9, 21))
                    ->create();
            } else {
                $songs->each->update([
                    'album_id' => $album->id,
                    'album' => $album->name,
                ]);
            }
        });
    }

    public function withMedia(): self
    {
        return $this->afterCreating(function (Album $album) {
            $performerNamesSlug = Str::of($album->performer->name)->ascii()->slug();
            $albumNameSlug = Str::of($album->name)->ascii()->slug();
            Storage::makeDirectory('public/winmedia/images_albums');
            Storage::makeDirectory("public/winmedia/images_albums/$performerNamesSlug");
            $path = $this->faker->image(Storage::path("public/winmedia/images_albums/$performerNamesSlug"), 300, 300);
            File::move($path, Storage::path("public/winmedia/images_albums/$performerNamesSlug/$albumNameSlug.jpg"));
        });
    }
}

<?php

namespace Database\Factories\Users;

use App\Http\Livewire\Nav\Tabs;
use App\Models\Radio\Program;
use App\Models\Teams\Team;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Laravel\Fortify\Contracts\TwoFactorAuthenticationProvider as TwoFactorAuthenticationProviderContract;
use Laravel\Fortify\RecoveryCode;

class UserFactory extends Factory
{
    /** @var string */
    protected $model = User::class;

    public function definition(): array
    {
        return [
            'username' => $this->faker->unique()->userName,
            'email' => $this->faker->unique()->email,
            'email_verified_at' => Date::now(),
            'password' => Hash::make('secret'),
            'address' => $this->faker->streetAddress,
            'city' => $this->faker->city,
            'birth_date' => $this->faker->date,
            'remember_token' => Str::random(10),
        ];
    }

    public function admin(): self
    {
        return $this->state(fn (array $attributes) => [
            'team_id' => Team::where('unique_key', 'admin')->sole()->id,
        ]);
    }

    public function employee(): self
    {
        return $this->state(fn (array $attributes) => [
            'team_id' => Team::where('unique_key', 'employee')->sole()->id,
        ]);
    }

    public function volunteer(): self
    {
        return $this->state(fn (array $attributes) => [
            'team_id' => Team::where('unique_key', 'volunteer')->sole()->id,
        ]);
    }

    public function editor(): self
    {
        return $this->state(fn (array $attributes) => [
            'team_id' => Team::where('unique_key', 'editor')->sole()->id,
        ]);
    }

    public function listener(): self
    {
        return $this->state(fn (array $attributes) => [
            'team_id' => Team::where('unique_key', 'listener')->sole()->id,
        ]);
    }

    public function unverified(): self
    {
        return $this->state(fn () => ['email_verified_at' => null]);
    }

    public function withSettings(
        ?bool $useFlac = null,
        ?bool $darkMode = null,
        ?bool $useGeolocation = null,
        ?string $defaultHomePageKey = null,
        ?int $defaultRadioStationId = null,
        ?int $defaultWebradioId = null,
    ): self {
        return $this->afterCreating(fn (User $user) => $user->settings()->update([
            'use_flac' => $useFlac ?? $this->faker->boolean,
            'dark_mode' => $darkMode ?? $this->faker->boolean,
            'use_geolocation' => $useGeolocation ?? $this->faker->boolean,
            'default_home_page' => $defaultHomePageKey ?? Arr::random(app(Tabs::class)->navTabs)['key'],
            'default_radio_station_id' => $defaultRadioStationId,
            'default_webradio_id' => $defaultWebradioId,
        ]));
    }

    public function withFavoriteSongs(array $songsIds): self
    {
        return $this->afterCreating(fn (User $user) => $user->favoriteSongs()->sync($songsIds));
    }

    public function withSubscribedPrograms(array $programsIds = []): self
    {
        return $this->afterCreating(function (User $user) use ($programsIds) {
            if ($programsIds) {
                $programsIds = Program::inRandomOrder()
                    ->limit($this->faker->numberBetween(1, 5))
                    ->pluck('id')
                    ->toArray();
            }
            $user->subscribedPrograms()->sync($programsIds);
        });
    }

    public function twoFactorAuthenticationActivated(): self
    {
        return $this->state(fn () => [
            'two_factor_secret' => encrypt(app(TwoFactorAuthenticationProviderContract::class)->generateSecretKey()),
            'two_factor_recovery_codes' => encrypt(json_encode(Collection::times(8, static function () {
                return RecoveryCode::generate();
            })->all(), JSON_THROW_ON_ERROR)),
        ]);
    }

    public function withMedia(?string $mediaPath = null): self
    {
        return $this->afterCreating(fn (User $user) => $user->addMedia($mediaPath
            ?: $this->faker->image(null, 250, 250))
            ->preservingOriginal()
            ->toMediaCollection('profile_picture'));
    }
}

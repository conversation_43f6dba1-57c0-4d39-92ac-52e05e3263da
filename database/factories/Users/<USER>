<?php

namespace Database\Factories\Users;

use App\Models\Users\User;
use App\Models\Users\UserJourney;
use Illuminate\Database\Eloquent\Factories\Factory;

class UserJourneyFactory extends Factory
{
    /** @var string */
    protected $model = UserJourney::class;

    public function definition(): array
    {
        return [
            'user_id' => null,
            'session_id' => null,
            'selected_radio_station_universe_id' => null,
            'current_route_key' => null,
            'played_audio_source_class' => null,
            'played_audio_source_id' => null,
            'played_audio_source_params' => null,
            'played_sub_audio_source_class' => null,
            'played_sub_audio_source_id' => null,
            'player_audio_stream_is_playing' => null,
            'selected_default_setting_radio_station_id' => null,
            'selected_default_setting_webradio_id' => null,
        ];
    }

    public function withSessionId(string $sessionId): self
    {
        return $this->state(['session_id' => $sessionId]);
    }

    public function withUser(User $user): self
    {
        return $this->state(['user_id' => $user->id]);
    }

    public function withCurrentRouteKey(string $currentRouteKey): self
    {
        return $this->state(['current_route_key' => $currentRouteKey]);
    }
}

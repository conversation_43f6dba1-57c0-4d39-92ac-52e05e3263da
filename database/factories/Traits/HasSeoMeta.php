<?php

namespace Database\Factories\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

trait HasSeoMeta
{
    public function withSeoMeta(?string $title = null, ?string $description = null, ?string $image = null): self
    {
        return $this->afterCreating(function (Model $model) use ($image, $title, $description) {
            $model->saveSeoMeta([
                'meta_title' => $title ?: Str::title($this->faker->words(3, true)),
                'meta_description' => $description === 'dummy' ? $this->faker->realText(150) : $description,
            ]);
            if ($image) {
                /** @var \Spatie\MediaLibrary\HasMedia $model */
                $image === 'dummy'
                    ? $model->addMedia($this->faker->image(null, 600, 600))->toMediaCollection('seo')
                    : $model->addMedia($image)->preservingOriginal()->toMediaCollection('seo');
            }
        });
    }
}

<?php

declare(strict_types=1);

namespace Database\Factories\Traits;

use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Map\Point;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

trait HasLocation
{
    /** @param Place|Collection<int, Place>|null $place */
    public function withPlace(Place|Collection|null $place = null): static
    {
        return $this->afterCreating(function (Model $model) use ($place): void {
            if ($place instanceof Collection) {
                $place = $place->random();
            }
            if ($place === null) {
                $place = Place::factory()->create();
            }

            $this->linkToPlace($model, $place);
        });
    }

    private function linkToPlace(Model $model, Place $place): void
    {
        if ($place->points->isEmpty()) {
            Point::factory()->create(['place_id' => $place->id]);
        }

        ContentLocation::factory()->create([
            'content_type' => $model::class,
            'content_id' => $model->getKey(),
            'location_type' => Place::class,
            'location_id' => $place->id,
        ]);
    }
}

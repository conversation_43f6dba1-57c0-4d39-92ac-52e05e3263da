<?php

namespace Database\Factories\Traits;

use App\Brickables\Carousel;
use App\Brickables\ColoredBackgroundContainer;
use App\Brickables\OneTextColumn;
use App\Brickables\OneTitleTextColumnOneImageColumn;
use App\Brickables\Spacer;
use App\Brickables\ThreeTextColumns;
use App\Brickables\Title;
use App\Brickables\TwoTextColumns;
use App\Models\Brickables\CarouselBrickSlide;
use App\Models\Brickables\ColoredBackgroundContainerBrick;
use App\View\Components\Front\Spacer as SpacerComponent;
use App\View\Components\Front\Title as TitleComponent;
use Closure;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Okipa\LaravelBrickables\Contracts\HasBrickables;

trait HasBricks
{
    /** @throws \Exception */
    public function withCarouselBrick(array $slides = []): self
    {
        return $this->afterCreating(function (HasBrickables $model) use ($slides) {
            $carouselBrick = $model->addBrick(Carousel::class, ['full_width' => true]);
            $slidesCount = $slides ? count($slides) : random_int(1, 3);
            for ($ii = 0; $ii <= $slidesCount; $ii++) {
                $slide = CarouselBrickSlide::create([
                    'brick_id' => $carouselBrick->id,
                    'label' => Str::title($this->faker->words(random_int(1, 3), true)),
                    'caption' => $this->faker->catchPhrase,
                    'active' => true,
                ]);
                $slide->addMedia(data_get($slides, $ii . '.image') ?: $this->faker->image(null, 2560, 700))
                    ->preservingOriginal()
                    ->toMediaCollection('images');
            }
        });
    }

    public function withTitleBrick(?string $title = null, ?string $typeKey = null, ?string $styleKey = null): self
    {
        return $this->afterCreating(fn (HasBrickables $model) => $model->addBrick(Title::class, [
            'title' => $title ?: Str::title($this->faker->words(random_int(1, 3), true)),
            'type' => $typeKey
                ? TitleComponent::TYPES[$typeKey]['key']
                : Arr::random(TitleComponent::TYPES)['key'],
            'style' => $styleKey
                ? TitleComponent::STYLES[$styleKey]['key']
                : Arr::random(TitleComponent::STYLES)['key'],
        ]));
    }

    public function withOneTextColumnBrick(?string $text = null): self
    {
        return $this->afterCreating(fn (HasBrickables $model) => $model->addBrick(OneTextColumn::class, [
            'text' => $text ?: $this->faker->realText(800),
        ]));
    }

    public function withTwoTextColumnsBrick(?string $textLeft = null, ?string $textRight = null): self
    {
        return $this->afterCreating(fn (HasBrickables $model) => $model->addBrick(TwoTextColumns::class, [
            'text_left' => $textLeft ?: $this->faker->realText(500),
            'text_right' => $textRight ?: $this->faker->realText(500),
        ]));
    }

    public function withThreeTextColumnsBrick(
        ?string $textLeft = null,
        ?string $textCenter = null,
        ?string $textRight = null
    ): self {
        return $this->afterCreating(fn (HasBrickables $model) => $model->addBrick(ThreeTextColumns::class, [
            'text_left' => $textLeft ?: $this->faker->realText(300),
            'text_center' => $textCenter ?: $this->faker->realText(300),
            'text_right' => $textRight ?: $this->faker->realText(300),
        ]));
    }

    public function withOneTitleTextColumnOneImageColumnBrick(
        ?string $titleLeft = null,
        ?string $textLeft = null,
        ?string $imageRight = null,
        ?bool $invertOrder = null
    ): self {
        return $this->afterCreating(function (HasBrickables $model) use (
            $titleLeft,
            $textLeft,
            $imageRight,
            $invertOrder
        ) {
            $oneColumnTextOneColumnImage = $model->addBrick(OneTitleTextColumnOneImageColumn::class, [
                'title_left' => $titleLeft ?: $this->faker->realText(30),
                'text_left' => $textLeft ?: $this->faker->realText(100),
                'invert_order' => $invertOrder ?? $this->faker->boolean,
            ]);
            /** @var \Spatie\MediaLibrary\HasMedia $oneColumnTextOneColumnImage */
            $oneColumnTextOneColumnImage->addMedia($imageRight ?: $this->faker->image(null, 470, 410))
                ->preservingOriginal()
                ->toMediaCollection('images');
        });
    }

    public function withColoredBackgroundContainerBrick(
        Closure $addSubBricks,
        ?string $backgroundColorKey = null,
        ?string $alignmentKey = null,
    ): self {
        return $this->afterCreating(function (HasBrickables $model) use (
            $backgroundColorKey,
            $alignmentKey,
            $addSubBricks
        ) {
            $factory = ColoredBackgroundContainerBrick::factory()->relatedToModel($model);
            $factory = $addSubBricks($factory);
            $factory->create([
                'data' => [
                    'background_color' => $backgroundColorKey
                        ? ColoredBackgroundContainer::BACKGROUND_COLORS[$backgroundColorKey]['key']
                        : Arr::random(ColoredBackgroundContainer::BACKGROUND_COLORS)['key'],
                    'alignment' => $alignmentKey
                        ? ColoredBackgroundContainer::ALIGNMENTS[$alignmentKey]['key']
                        : Arr::random(ColoredBackgroundContainer::ALIGNMENTS)['key'],
                ],
            ]);
        });
    }

    public function withSpacerBrick(string $type): self
    {
        return $this->afterCreating(fn (HasBrickables $model) => $model->addBrick(Spacer::class, [
            'type' => SpacerComponent::TYPES[$type]['key'],
        ]));
    }
}

<?php

namespace Database\Factories\Cookies;

use App\Models\Cookies\CookieCategory;
use App\Models\Cookies\CookieService;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CookieServiceFactory extends Factory
{
    /** @var string */
    protected $model = CookieService::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->unique()->catchPhrase,
            'description' => $this->faker->realText(),
            'cookies' => $this->faker->words(),
            'required' => $this->faker->boolean,
            'enabled_by_default' => $this->faker->boolean,
            'active' => $this->faker->boolean,
        ];
    }

    public function configure(): self
    {
        return $this->afterMaking(fn (CookieService $cookieService) => $cookieService->unique_key =
            $cookieService->unique_key ?: Str::slug(Str::ascii($cookieService->title), '_'));
    }

    public function withCategories(?array $categoryUniqueKeys): self
    {
        return $this->afterCreating(function (CookieService $cookieService) use ($categoryUniqueKeys) {
            $categories = CookieCategory::whereIn('unique_key', $categoryUniqueKeys)->get();
            $cookieService->categories()->sync($categories->pluck('id'));
        });
    }

    public function active(): self
    {
        return $this->state(fn () => ['active' => true]);
    }
}

<?php

namespace Database\Factories\Cookies;

use App\Models\Cookies\CookieCategory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class CookieCategoryFactory extends Factory
{
    /** @var string */
    protected $model = CookieCategory::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->unique()->catchPhrase,
            'description' => $this->faker->realText(),
        ];
    }

    public function configure(): self
    {
        return $this->afterMaking(fn (CookieCategory $cookieCategory) => $cookieCategory->unique_key =
            $cookieCategory->unique_key ?: Str::slug(Str::ascii($cookieCategory->title), '_'));
    }
}

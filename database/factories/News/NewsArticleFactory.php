<?php

namespace Database\Factories\News;

use App\Models\Audio\Thematic;
use App\Models\News\NewsArticle;
use App\Models\Users\User;
use Database\Factories\Traits\HasLocation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;

class NewsArticleFactory extends Factory
{
    use HasLocation;

    /** @var string */
    protected $model = NewsArticle::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->catchPhrase,
            'description' => $this->faker->realText(),
            'tags' => implode(', ', $this->faker->words(random_int(1, 3))),
            'thematic_id' => Thematic::inRandomOrder()->first()?->id,
            'user_id' => User::inRandomOrder()->first()?->id,
            'active' => $this->faker->boolean,
            'published_at' => Date::now(),
        ];
    }

    public function configure(): self
    {
        return $this->afterMaking(fn (NewsArticle $newsArticle) => $newsArticle->slug = $newsArticle->slug
            ?: Str::slug($newsArticle->title))
            ->afterCreating(function (NewsArticle $newsArticle) {
                $newsArticle->saveSeoMeta([
                    'meta_title' => $newsArticle->title,
                    'meta_description' => Str::limit($newsArticle->title, 150),
                ]);
            });
    }

    public function withThematic(Thematic $thematic): self
    {
        return $this->state(fn () => ['thematic_id' => $thematic->id]);
    }

    public function withAuthor(User $user): self
    {
        return $this->state(fn () => ['user_id' => $user->id]);
    }

    public function withMedia(): self
    {
        return $this->afterCreating(function (NewsArticle $newsArticle) {
            $illustrationsCount = random_int(1, 3);
            for ($ii = 1; $ii <= $illustrationsCount; $ii++) {
                $newsArticle->addMedia($this->faker->image(null, 1140, 500))
                    ->toMediaCollection('illustrations');
            }
            $audioPath = resource_path('seeds/audio/' . random_int(1, 15) . '.mp3');
            $newsArticle->addMedia($audioPath)->preservingOriginal()->toMediaCollection('audio');
        });
    }

    public function withRadioStations(array $radioStationsIds = []): self
    {
        return $this->afterCreating(function (NewsArticle $newsArticle) use ($radioStationsIds) {
            $newsArticle->radioStations()
                ->sync($radioStationsIds
                    ?: radioStations()->shuffle()->take($this->faker->numberBetween(
                        1,
                        min(3, radioStations()->count())
                    ))->pluck('id')->toArray());
        });
    }

    public function active(): self
    {
        return $this->state(fn () => ['active' => true]);
    }

    public function inactive(): self
    {
        return $this->state(fn () => ['active' => false]);
    }
}

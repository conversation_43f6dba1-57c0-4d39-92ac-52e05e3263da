<?php

namespace Database\Factories\Events;

use App\Models\Audio\Thematic;
use App\Models\Events\Event;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use Database\Factories\Traits\HasLocation;
use DateInterval;
use DateTimeZone;
use Illuminate\Database\Eloquent\Factories\Factory;

class EventFactory extends Factory
{
    use HasLocation;

    protected $model = Event::class;

    /** @throws \Exception */
    public function definition(): array
    {
        $startedAt = $this->faker->unique()->dateTimeBetween('-1 year', '+1 year')
            ->setTimezone(new DateTimeZone('Europe/Paris'))
            ->setTime(0, 0)
            ->setTimezone(new DateTimeZone('UTC'));

        return [
            'thematic_id' => Thematic::inRandomOrder()->first()?->id,
            'user_id' => User::inRandomOrder()->first()?->id,
            'title' => $this->faker->unique()->catchphrase,
            'tags' => implode(', ', $this->faker->words(random_int(1, 3))),
            'description' => $this->faker->text(),
            'started_at' => $startedAt,
            'ended_at' => (clone $startedAt)->add(DateInterval::createFromDateString(random_int(0, 5) . ' days')),
            'active' => $this->faker->boolean(),
        ];
    }

    public function withThematic(Thematic $thematic): self
    {
        return $this->state(fn () => ['thematic_id' => $thematic->id]);
    }

    public function withAuthor(User $user): self
    {
        return $this->state(fn () => ['user_id' => $user->id]);
    }

    public function withRadioStations(array $radioStationIds = []): self
    {
        return $this->afterCreating(function (Event $event) use ($radioStationIds) {
            if (! $radioStationIds) {
                $radioStationIds = RadioStation::inRandomOrder()
                    ->limit($this->faker->numberBetween(0, 5))
                    ->pluck('id')->toArray();
            }
            $event->radioStations()->sync($radioStationIds);
        });
    }

    public function withMedia(): self
    {
        return $this->afterCreating(function (Event $event) {
            $event->addMedia($this->faker->image(null, 475, 285))->toMediaCollection('cover');
            $event->addMedia(resource_path('seeds/audio/' . random_int(1, 15) . '.mp3'))
                ->preservingOriginal()
                ->toMediaCollection('audio');
        });
    }

    public function active(): self
    {
        return $this->afterMaking(static function (Event $event) {
            $event->active = true;
        });
    }

    public function inactive(): self
    {
        return $this->afterMaking(static function (Event $event) {
            $event->active = false;
        });
    }
}

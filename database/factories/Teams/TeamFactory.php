<?php

namespace Database\Factories\Teams;

use App\Models\Teams\Team;
use Illuminate\Database\Eloquent\Factories\Factory;
use Str;

class TeamFactory extends Factory
{
    /** @var string */
    protected $model = Team::class;

    public function definition(): array
    {
        $name = $this->faker->jobTitle;

        return ['name' => $name, 'unique_key' => Str::slug($name)];
    }

    public function admin(): TeamFactory
    {
        return $this->state(fn (array $attributes) => ['unique_key' => 'admin', 'name' => 'Administrateur']);
    }

    public function employee(): TeamFactory
    {
        return $this->state(fn (array $attributes) => ['unique_key' => 'employee', 'name' => 'Salarié']);
    }

    public function volunteer(): TeamFactory
    {
        return $this->state(fn (array $attributes) => ['unique_key' => 'volunteer', 'name' => 'Bénévole']);
    }

    public function editor(): TeamFactory
    {
        return $this->state(fn (array $attributes) => ['unique_key' => 'editor', 'name' => 'Editeur']);
    }

    public function listener(): TeamFactory
    {
        return $this->state(fn (array $attributes) => ['unique_key' => 'listener', 'name' => 'Auditeur']);
    }
}

<?php

namespace Database\Factories\Pages;

use App\Models\Pages\Page;
use Database\Factories\Traits\HasBricks;
use Database\Factories\Traits\HasSeoMeta;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

class PageFactory extends Factory
{
    use HasBricks;
    use HasSeoMeta;

    /** @var string */
    protected $model = Page::class;

    public function definition(): array
    {
        return [
            'nav_title' => $this->faker->unique()->catchPhrase,
            'active' => $this->faker->boolean,
        ];
    }

    public function configure(): self
    {
        return $this->afterMaking(function (Page $page) {
            $page->unique_key = $page->unique_key ?: Str::slug(Str::ascii($page->nav_title), '_');
            $page->slug = $page->slug ?: Str::slug($page->nav_title);
        });
    }
}

<?php

namespace Database\Factories\Audio;

use App\Models\Audio\Thematic;
use Illuminate\Database\Eloquent\Factories\Factory;

class ThematicFactory extends Factory
{
    /** @var string */
    protected $model = Thematic::class;

    public function definition(): array
    {
        return ['title' => $this->faker->catchphrase];
    }

    public function withMedia(): self
    {
        return $this->afterCreating(function (Thematic $thematic) {
            $thematic->addMedia($this->faker->image(null, 132, 132))
                ->toMediaCollection('illustrations');
        });
    }
}

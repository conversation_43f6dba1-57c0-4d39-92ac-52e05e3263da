<?php

namespace Database\Factories\Audio;

use App\Models\Audio\Podcast;
use App\Models\Audio\Thematic;
use App\Models\Radio\Program;
use Database\Factories\Traits\HasLocation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class PodcastFactory extends Factory
{
    use HasLocation;

    /** @var string */
    protected $model = Podcast::class;

    /** @throws \Exception */
    public function definition(): array
    {
        return [
            'program_id' => Program::inRandomOrder()->first('id')->id,
            'thematic_id' => Thematic::inRandomOrder()->first('id')->id,
            'type' => Arr::random(array_keys(Podcast::TYPES)),
            'title' => $this->faker->unique()->catchPhrase,
            'tags' => implode(', ', $this->faker->words(random_int(1, 3))),
            'description' => $this->faker->realText,
            'duration' => $this->faker->numberBetween(180, 5400), // In seconds => between 3min and 1h30
            'winmedia_audio_source_uploaded' => false,
            'published_at' => $this->faker->unique()->dateTimeBetween(),
            'active' => true,
        ];
    }

    public function withThematic(Thematic $thematic): self
    {
        return $this->state(fn () => ['thematic_id' => $thematic->id]);
    }

    public function withAuthors(array $authorsIds = []): self
    {
        return $this->afterCreating(function (Podcast $podcast) use ($authorsIds) {
            $authorsIdsWithPivot = collect($authorsIds)->mapWithKeys(fn (
                int $songId,
                int $index
            ) => [$songId => ['index' => $index]])->toArray();
            $podcast->authors()->sync($authorsIdsWithPivot);
        });
    }

    public function withProgram(Program $program): self
    {
        return $this->state(fn () => ['program_id' => $program->id]);
    }

    public function withMedia(): self
    {
        return $this->afterCreating(function (Podcast $podcast) {
            // Cover
            $podcast->addMedia($this->faker->image(null, 172, 172))
                ->toMediaCollection('cover');
            // Audio file
            Storage::makeDirectory('public/winmedia/podcasts');
            $audioPath = resource_path('seeds/audio/' . random_int(1, 15) . '.mp3');
            if ($podcast->type === Podcast::TYPE_ORIGINAL) {
                $podcast->addMedia($audioPath)->preservingOriginal()->toMediaCollection('audio');

                return;
            }
            File::copy($audioPath, Storage::path('public/winmedia/podcasts/' . $podcast->id . '.mp3'));
            $podcast->update(['winmedia_audio_source_uploaded' => true]);
        });
    }

    public function active(): self
    {
        return $this->state(fn () => ['active' => true]);
    }

    public function inactive(): self
    {
        return $this->state(fn () => ['active' => false]);
    }
}

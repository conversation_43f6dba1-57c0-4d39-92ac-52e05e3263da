<?php

namespace Database\Factories\Audio;

use App\Models\Audio\Song;
use App\Models\Audio\SongHistory;
use Illuminate\Database\Eloquent\Factories\Factory;

class SongHistoryFactory extends Factory
{
    /** @var string */
    protected $model = SongHistory::class;

    public function definition(): array
    {
        return [
            'imedia' => null,
            'dateheure_diffusion' => $this->faker->dateTime(),
        ];
    }

    public function withSong(Song $song): self
    {
        return $this->state(['imedia' => $song->imedia]);
    }
}

<?php

namespace Database\Factories\Audio;

use App\Models\Audio\Song;
use App\Models\Performers\Album;
use App\Models\Performers\Label;
use App\Models\Performers\Performer;
use App\Models\Winmedia\WinmediaPath;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class SongFactory extends Factory
{
    protected static int $nextId = 1;

    /** @var string */
    protected $model = Song::class;

    public function definition(): array
    {
        $releaseDate = $this->faker->boolean(90) ? ( // 90% chance of a date
            $this->faker->boolean(50) ? // 50% chance of being from last year
                $this->faker->dateTimeBetween('-1 year')->format('Y-m-d') :
                $this->faker->dateTimeBetween('-30 years', '-1 year')->format('Y-m-d')
        ) : null;

        return [
            'imedia' => self::$nextId++,
            'performer' => $this->faker->name(),
            'title' => Str::title($this->faker->words(asText: true)),
            'version' => Arr::random([
                Str::title($this->faker->words(asText: true)),
                '',
            ]),
            'duration' => $this->faker->numberBetween(120000, 300000), // In milliseconds => between 2 and 5 minutes
            'album' => Str::title($this->faker->words(asText: true)),
            'genre' => Arr::random(['Pop', 'Rap & Hip-Hop', 'Soul', 'Funk', 'Alternative & Punk', 'Folk', 'Rock']),
            'publisher' => Arr::random([
                Str::title($this->faker->words(asText: true)),
                '',
            ]),
            'comment' => Arr::random([
                Str::title($this->faker->words(asText: true)),
                '',
            ]),
            'language' => Arr::random(['', 'VE', 'VF', 'Grand Ouest']),
            '_proprietes' => Arr::random(['/Nouveauté/', '/Artiste Grand Ouest//Local/', '/Nouveauté//Artiste Grand Ouest//Local/', '/HIT/', '']),
            '_station' => Arr::random(['/2//3//5/', '/2/', '/3//5/', '/2//5/', '/3/']),
            'dateheure_mise_a_jour' => $this->faker->dateTimeBetween('-1 year'),
            'elasticsearch_action' => Arr::random([
                Song::ES_ACTION_UP_TO_DATE,
                Song::ES_ACTION_TO_SYNC,
                Song::ES_ACTION_TO_DELETE,
            ]),
            'year' => $releaseDate ? Arr::random([
                Date::parse($releaseDate)->format('Y'), // 50% chance of having the year alone
                $releaseDate, // 50% chance of getting the full date
            ]) : null,
            '_release_date' => $releaseDate,
            'category_plateforme' => Song::CATEGORY_PLATEFORME_MUSICS,
            'winmedia_presence' => Song::WINMEDIA_PRESENCE_IS_PRESENT,
        ];
    }

    public function configure(): self
    {
        self::$nextId = ((int) Song::withoutGlobalScopes()->max('imedia')) + 1;

        return $this->afterCreating(function (Song $song) {
            // 1 chance on 10 to set no song created_at field
            if ($this->faker->numberBetween(1, 10) > 9) {
                return;
            }
            if ($song->imedia) {
                WinmediaPath::create([
                    'media' => $song->imedia,
                    'extension' => $this->faker->numberBetween(0, 5),
                    'modify' => $this->faker->unique()->dateTimeBetween(),
                ]);
            }
        });
    }

    public function withPerformer(?Performer $performer = null): self
    {
        if (! $performer) {
            $performer = Performer::factory()->withMedia()->create();
        }

        return $this->state(['performer' => $performer->name]);
    }

    public function withAlbum(Album $album): self
    {
        return $this->state(['album_id' => $album->id, 'album' => $album->name]);
    }

    public function withLabel(?Label $label = null): self
    {
        if (! $label) {
            $label = Label::factory()->withPlace()->create();
        }

        return $this->state(['label_id' => $label->id, 'publisher' => $label->name]);
    }

    public function withMedia(): self
    {
        return $this->afterCreating(function (Song $song) {
            // Cover thumb
            Storage::makeDirectory('public/winmedia/pochettes_thumbnail');
            $path = $this->faker->image(Storage::path('public/winmedia/pochettes_thumbnail'), 300, 300);
            File::move($path, Storage::path('public/winmedia/pochettes_thumbnail/' . $song->id . '.jpg'));
            // Cover middle
            Storage::makeDirectory('public/winmedia/pochettes_middle');
            $path = $this->faker->image(Storage::path('public/winmedia/pochettes_middle'), 600, 600);
            File::move($path, Storage::path('public/winmedia/pochettes_middle/' . $song->id . '.jpg'));
            // Cover full size
            Storage::makeDirectory('public/winmedia/pochettes_brut');
            $path = $this->faker->image(Storage::path('public/winmedia/pochettes_brut'), 640, 640);
            File::move($path, Storage::path('public/winmedia/pochettes_brut/' . $song->id . '.jpg'));
            // Audio files
            $songAudioFile = resource_path('seeds/audio/' . random_int(1, 15) . '.mp3');
            Storage::makeDirectory('public/winmedia/extraits');
            File::copy(
                $songAudioFile,
                Storage::path('public/' . Song::AUDIO_EXTRACT_STORAGE_DIR . $song->id . '.mp3')
            );
        });
    }

    public function sonoreType(): self
    {
        return $this->state([
            'category_plateforme' => Song::CATEGORY_PLATEFORME_SONORES,
            'winmedia_presence' => Song::WINMEDIA_PRESENCE_IS_PRESENT,
            'elasticsearch_action' => Arr::random([
                Song::ES_ACTION_NOT_INDEXED,
                Song::ES_ACTION_TO_SYNC,
                Song::ES_ACTION_TO_DELETE,
            ]),
        ]);
    }

    public function archivageType(): self
    {
        return $this->state([
            'imedia' => null,
            'version' => '',
            'language' => '',
            '_proprietes' => '/Archivage Sonore/',
            '_proprietaire' => 'Structure',
            '_station' => '',
            'category_plateforme' => Song::CATEGORY_PLATEFORME_ARCHIVAGE,
            'winmedia_presence' => Song::WINMEDIA_PRESENCE_NOT_PRESENT,
        ]);
    }
}

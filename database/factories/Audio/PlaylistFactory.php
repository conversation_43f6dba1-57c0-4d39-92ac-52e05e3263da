<?php

namespace Database\Factories\Audio;

use App\Models\Audio\Playlist;
use App\Models\Audio\Song;
use App\Models\Audio\Thematic;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use Database\Factories\Traits\HasLocation;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

class PlaylistFactory extends Factory
{
    use HasLocation;

    /** @var string */
    protected $model = Playlist::class;

    /** @throws \Exception */
    public function definition(): array
    {
        return [
            'user_id' => User::inRandomOrder()->first('id')?->id,
            'thematic_id' => Thematic::inRandomOrder()->first()?->id,
            'title' => $this->faker->catchPhrase,
            'description' => Arr::random([$this->faker->realText(300), null]),
            'tags' => implode(', ', $this->faker->words(random_int(1, 3))),
            'rail_displayed' => $this->faker->boolean,
            'active' => $this->faker->boolean,
            'published_at' => $this->faker->unique()->dateTimeBetween(),
            'unpublished_at' => null,
        ];
    }

    public function withMedia(): self
    {
        return $this->afterCreating(function (Playlist $playlist) {
            $playlist->addMedia($this->faker->image(null, 1695, 210))->toMediaCollection('cover');
        });
    }

    public function withRadioStations(array $radioStationIds = []): self
    {
        return $this->afterCreating(function (Playlist $playlist) use ($radioStationIds) {
            if (! $radioStationIds) {
                $radioStationIds = RadioStation::inRandomOrder()
                    ->limit($this->faker->numberBetween(0, 5))
                    ->pluck('id')
                    ->toArray();
            }
            $playlist->radioStations()->sync($radioStationIds);
        });
    }

    public function withSongs(array $songsIds = []): self
    {
        return $this->afterCreating(function (Playlist $playlist) use ($songsIds) {
            $songsIdsWithPivot = $songsIds
                ? collect($songsIds)
                    ->mapWithKeys(fn (int $songId, int $index) => [$songId => ['index' => $index]])
                    ->toArray()
                : Song::inRandomOrder()
                    ->select('id')
                    ->limit($this->faker->numberBetween(1, min(30, Song::count())))
                    ->get()
                    ->mapWithKeys(fn (Song $song, int $index) => [$song->id => ['index' => $index]])
                    ->toArray();
            $playlist->songs()->sync($songsIdsWithPivot);
        });
    }

    public function withThematic(Thematic $thematic): self
    {
        return $this->state(fn () => ['thematic_id' => $thematic->id]);
    }

    public function withUser(User $user): self
    {
        return $this->state(fn () => ['user_id' => $user->id]);
    }

    public function public(): self
    {
        return $this->state(fn () => ['user_id' => null]);
    }

    public function active(): self
    {
        return $this->state(fn () => ['active' => true]);
    }

    public function inactive(): self
    {
        return $this->state(fn () => ['active' => true]);
    }
}

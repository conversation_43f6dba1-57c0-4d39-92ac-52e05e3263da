<?php

namespace Database\Factories\UserSettings;

use App\Enums\ColorModesEnum;
use App\Http\Livewire\Nav\Tabs;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Arr;

class UserSettingFactory extends Factory
{
    public function definition(): array
    {
        return [
            'user_id' => User::inRandomOrder()->first('id')->id,
            'use_flac' => $this->faker->boolean,
            'dark_mode' => $this->faker->boolean,
            'dark_mode_enum' => ColorModesEnum::class,
            'use_geolocation' => $this->faker->boolean,
            'default_home_page' => Arr::random(app(Tabs::class)->navTabs)['key'],
            'default_radio_station_id' => null,
            'default_webradio_id' => null,
        ];
    }
}

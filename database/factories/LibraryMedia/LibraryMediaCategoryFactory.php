<?php

namespace Database\Factories\LibraryMedia;

use App\Models\LibraryMedia\LibraryMediaCategory;
use Illuminate\Database\Eloquent\Factories\Factory;

class LibraryMediaCategoryFactory extends Factory
{
    /** @var string */
    protected $model = LibraryMediaCategory::class;

    public function definition(): array
    {
        return [
            'title' => $this->faker->unique()->catchPhrase,
        ];
    }
}

<?php

namespace Database\Factories\LibraryMedia;

use App\Models\LibraryMedia\LibraryMediaCategory;
use App\Models\LibraryMedia\LibraryMediaFile;
use Illuminate\Database\Eloquent\Factories\Factory;

class LibraryMediaFileFactory extends Factory
{
    /** @var string */
    protected $model = LibraryMediaFile::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->catchPhrase,
            'category_id' => LibraryMediaCategory::inRandomOrder()->first()?->id,
        ];
    }

    public function withMedia(?string $imageUrl = null): self
    {
        return $this->afterCreating(function (LibraryMediaFile $libraryMediaFile) use ($imageUrl) {
            $libraryMediaFile->addMedia($imageUrl ?? $this->faker->image(null, 40, 40))
                ->preservingOriginal()
                ->toMediaCollection('media');
        });
    }
}

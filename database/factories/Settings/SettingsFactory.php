<?php

namespace Database\Factories\Settings;

use App\Models\Settings\Settings;
use Illuminate\Database\Eloquent\Factories\Factory;

class SettingsFactory extends Factory
{
    protected $model = Settings::class;

    public function definition(): array
    {
        return [
            'email' => $this->faker->unique()->safeEmail,
            'phone_number' => $this->faker->phoneNumber,
            'address' => $this->faker->address,
            'zip_code' => $this->faker->postcode,
            'city' => $this->faker->city,
            'facebook_url' => $this->faker->unique()->url,
            'twitter_url' => $this->faker->unique()->url,
            'instagram_url' => $this->faker->unique()->url,
            'linkedin_url' => $this->faker->unique()->url,
            'youtube_url' => $this->faker->unique()->url,
            'bluesky_url' => $this->faker->unique()->url,
            'mastodon_url' => $this->faker->unique()->url,
            'newsletter_url' => $this->faker->unique()->url,
            'play_store_app_url' => $this->faker->unique()->url,
            'app_store_app_url' => $this->faker->unique()->url,
            'matomo_url' => $this->faker->url,
            'matomo_id_site' => $this->faker->uuid,
            'ga4_tracking_id' => null,
            'login_modal_on_init' => false,
            'map_events_week_interval' => $this->faker->numberBetween(1, 5),
            'map_news_week_interval' => $this->faker->numberBetween(1, 5),
            'map_playlists_week_interval' => $this->faker->numberBetween(1, 5),
            'map_podcasts_week_interval' => $this->faker->numberBetween(1, 5),
            'map_performers_week_interval' => $this->faker->numberBetween(1, 5),
        ];
    }

    public function withMedia(?array $media = null): self
    {
        return $this->afterCreating(function (Settings $settings) use ($media) {
            $settings->addMedia(data_get($media, 'logo_squared') ?: $this->faker->image(null, 250, 250))
                ->preservingOriginal()
                ->toMediaCollection('logo_squared');
        });
    }
}

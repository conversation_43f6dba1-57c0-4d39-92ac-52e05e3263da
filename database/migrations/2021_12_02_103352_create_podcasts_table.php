<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('podcasts', static function (Blueprint $table) {
            $table->id();
            $table->foreignId('program_id')->constrained('programs')->cascadeOnDelete();
            $table->foreignId('thematic_id')->nullable()->constrained('thematics')->nullOnDelete();
            $table->string('type');
            $table->string('title');
            $table->string('tags');
            $table->text('description');
            $table->unsignedInteger('duration');
            $table->dateTime('published_at');
            $table->boolean('winmedia_audio_source_uploaded')->default(false);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('podcasts');
    }
};

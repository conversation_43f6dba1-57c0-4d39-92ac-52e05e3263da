<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::create('winmedia_path', static function (Blueprint $table) {
            $table->id('ipath');
            $table->integer('media');
            $table->integer('extension');
            $table->dateTime('modify');
        });
    }

    public function down(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::dropIfExists('winmedia_path');
    }
};

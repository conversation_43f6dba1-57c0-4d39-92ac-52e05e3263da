<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('radio_stations', static function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('winmedia_id')->unique();
            $table->string('name');
            $table->string('stream_url_ld');
            $table->string('stream_url_hd');
            $table->double('latitude')->nullable();
            $table->double('longitude')->nullable();
            $table->string('color');
            $table->boolean('active');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('radio_stations');
    }
};

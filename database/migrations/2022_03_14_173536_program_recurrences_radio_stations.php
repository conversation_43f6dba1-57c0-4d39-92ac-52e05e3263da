<?php

use App\Models\Radio\ProgramRecurrence;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('program_recurrences_radio_stations', static function (Blueprint $table) {
            $table->foreignId('program_recurrence_id')->constrained('program_recurrences')->cascadeOnDelete();
            $table->foreignId('radio_station_id')->constrained('radio_stations')->cascadeOnDelete();
            $table->timestamps();
            $table->primary(
                ['program_recurrence_id', 'radio_station_id'],
                'program_recurrence_id_radio_station_id_primary'
            );
        });
        foreach (ProgramRecurrence::get() as $programRecurrence) {
            $radioStationIdsArray = json_decode(
                $programRecurrence->getRawOriginal('radio_station_ids'),
                true,
                512,
                JSON_THROW_ON_ERROR
            );
            $programRecurrence->radioStations()->sync($radioStationIdsArray);
        }
        Schema::table('program_recurrences', static function (Blueprint $table) {
            $table->dropColumn('radio_station_ids');
        });
    }

    public function down(): void
    {
        Schema::table('program_recurrences', static function (Blueprint $table) {
            $table->json('radio_station_ids')->after('label');
        });
        foreach (ProgramRecurrence::with('radioStations:id')->get() as $programRecurrence) {
            $radioStationIds = $programRecurrence->radioStations->pluck('id');
            $programRecurrence->forceFill(['radio_station_ids' => $radioStationIds])->save();
        }
        Schema::dropIfExists('program_recurrences_radio_stations');
    }
};

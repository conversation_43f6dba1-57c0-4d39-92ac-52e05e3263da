<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cookie_service_category', static function (Blueprint $table) {
            $table->foreignId('cookie_service_id')->constrained('cookie_services')->cascadeOnDelete();
            $table->foreignId('cookie_category_id')->constrained('cookie_categories')->cascadeOnDelete();
            $table->primary(['cookie_service_id', 'cookie_category_id'], 'cookie_service_id_category_id_primary');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cookie_service_category');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cookie_services', static function (Blueprint $table) {
            $table->id();
            $table->string('unique_key')->unique();
            $table->string('title');
            $table->string('description')->nullable();
            $table->string('cookies')->nullable();
            $table->boolean('required');
            $table->boolean('enabled_by_default');
            $table->boolean('active');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cookie_services');
    }
};

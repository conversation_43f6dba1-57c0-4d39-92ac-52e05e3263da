<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('events', static function (Blueprint $table) {
            $table->id();
            $table->foreignId('thematic_id')->nullable()->constrained('thematics')->nullOnDelete();
            $table->string('title');
            $table->mediumText('description');
            $table->string('location_name');
            $table->string('location_latitude');
            $table->string('location_longitude');
            $table->boolean('active');
            $table->dateTime('started_at');
            $table->dateTime('ended_at');
            $table->timestamps();
        });
        Schema::create('event_radio_station', static function (Blueprint $table) {
            $table->foreignId('event_id')->constrained('events')->cascadeOnDelete();
            $table->foreignId('radio_station_id')->constrained('radio_stations')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('event_radio_station');
        Schema::dropIfExists('events');
    }
};

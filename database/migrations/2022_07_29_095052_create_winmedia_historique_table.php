<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::create('winmedia_historique', static function (Blueprint $table) {
            $table->id();
            $table->integer('imedia');
            $table->foreign('imedia')->on('winmedia_media')->references('imedia')->cascadeOnDelete();
            $table->dateTime('dateheure_diffusion');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::dropIfExists('winmedia_historique');
    }
};

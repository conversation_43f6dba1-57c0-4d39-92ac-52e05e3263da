<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('programs', static function (Blueprint $table) {
            $table->boolean('theorical_timing_live_display')->after('end_podcast_version')->default(false);
        });
    }

    public function down(): void
    {
        Schema::table('programs', static function (Blueprint $table) {
            $table->dropColumn('theorical_timing_live_display');
        });
    }
};

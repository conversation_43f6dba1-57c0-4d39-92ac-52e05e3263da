<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
    {
        Schema::table('playlists', static function (Blueprint $table) {
            $table->foreignId('user_id')->nullable()->change();
            $table->foreignId('thematic_id')->after('user_id')->nullable()->constrained('thematics')->nullOnDelete();
            $table->text('tags')->after('title')->nullable();
            $table->boolean('rail_displayed')->after('tags')->default(false);
            $table->boolean('active')->after('rail_displayed')->default(false);
            $table->dateTime('published_at')->after('active')->nullable();
            $table->dateTime('unpublished_at')->after('published_at')->nullable();
        });
    }

    public function down()
    {
        Schema::table('playlists', static function (Blueprint $table) {
            $table->dropColumn(['thematic_id', 'tags', 'rail_displayed', 'active', 'published_at', 'unpublished_at']);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('news_articles', static function (Blueprint $table) {
            $table->string('audio_caption')->nullable()->after('description');
        });
        Schema::table('events', static function (Blueprint $table) {
            $table->string('audio_caption')->nullable()->after('description');
        });
    }

    public function down(): void
    {
        Schema::table('news_articles', static function (Blueprint $table) {
            $table->dropColumn('audio_caption');
        });
        Schema::table('events', static function (Blueprint $table) {
            $table->dropColumn('audio_caption');
        });
    }
};

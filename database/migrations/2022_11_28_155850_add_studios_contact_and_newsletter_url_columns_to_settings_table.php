<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('settings', static function (Blueprint $table) {
            $table->text('studios_contact')->nullable()->after('city');
            $table->string('newsletter_url', 510)->nullable()->after('youtube_url');
        });
    }

    public function down(): void
    {
        Schema::table('settings', static function (Blueprint $table) {
            $table->dropColumn('studios_contact');
            $table->dropColumn('newsletter_url');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('holidays', static function (Blueprint $table) {
            $table->id();
            $table->string('label');
            $table->date('started_at');
            $table->date('ended_at');
            $table->boolean('active');
            $table->timestamps();
        });
        Schema::create('holidays_programs', static function (Blueprint $table) {
            $table->foreignId('holiday_id')->constrained('holidays')->cascadeOnDelete();
            $table->foreignId('program_id')->constrained('programs')->cascadeOnDelete();
            $table->primary(['holiday_id', 'program_id']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('holidays');
        Schema::dropIfExists('holidays_programs');
    }
};

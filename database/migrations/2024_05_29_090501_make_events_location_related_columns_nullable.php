<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('events', static function (Blueprint $table) {
            $table->string('location_name')->nullable()->change();
            $table->string('location_latitude')->nullable()->change();
            $table->string('location_longitude')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('events', static function (Blueprint $table) {
            $table->string('location_name')->nullable(false)->change();
            $table->string('location_latitude')->nullable(false)->change();
            $table->string('location_longitude')->nullable(false)->change();
        });
    }
};

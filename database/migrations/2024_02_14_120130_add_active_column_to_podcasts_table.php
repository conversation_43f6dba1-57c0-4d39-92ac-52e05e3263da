<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('podcasts', static function (Blueprint $table) {
            $table->boolean('active')->default(true)->after('winmedia_audio_source_uploaded');
        });
    }

    public function down(): void
    {
        Schema::table('podcasts', static function (Blueprint $table) {
            $table->dropColumn('active');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('places', static function (Blueprint $table) {
            $table->string('informations')->nullable()->after('enabled');
            $table->string('source_name')->nullable()->after('informations');
            $table->string('source_id')->nullable()->after('source_name');
        });
    }

    public function down(): void
    {
        Schema::table('places', static function (Blueprint $table) {
            $table->dropColumn('informations');
            $table->dropColumn('source_name');
            $table->dropColumn('source_id');
        });
    }
};

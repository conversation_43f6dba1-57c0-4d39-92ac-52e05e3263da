<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('carousel_brick_slides', static function (Blueprint $table) {
            $table->boolean('url_target_blank')->after('url')->default(false);
        });
    }

    public function down(): void
    {
        Schema::table('carousel_brick_slides', static function (Blueprint $table) {
            $table->dropColumn('url_target_blank');
        });
    }
};

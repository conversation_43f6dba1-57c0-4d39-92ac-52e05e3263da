<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('albums', static function (Blueprint $table) {
            $table->foreignId('performer_id')
                ->after('id')
                ->nullable()
                ->constrained('performers')
                ->references('id')
                ->cascadeOnDelete();
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->string('_release_date')->nullable()->after('_compilation_id');
        });
    }

    public function down(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->dropColumn('_release_date');
        });
    }
};

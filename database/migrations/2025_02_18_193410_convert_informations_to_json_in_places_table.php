<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // Première étape : convertir les données existantes en JSON
        DB::table('places')->whereNotNull('informations')->where('informations', '<>', '')->update([
            'informations' => DB::raw("jsonb_build_object('data', informations)"),
        ]);

        // Deuxième étape : convertir les chaînes vides en JSON null
        DB::table('places')->where('informations', '')->update([
            'informations' => null,
        ]);

        // Troisième étape : modifier le type de la colonne en JSON
        DB::statement('ALTER TABLE places ALTER COLUMN informations TYPE JSON USING informations::jsonb');
    }

    public function down(): void
    {
        // Première étape : changer le type de la colonne en text
        Schema::table('places', function (Blueprint $table) {
            $table->string('informations')->nullable()->change();
        });

        // Deuxième étape : reconvertir les données JSON en string
        DB::statement("UPDATE places SET informations = CAST(informations::json->>'data' AS VARCHAR) WHERE informations IS NOT NULL");
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('announcements_radio_stations', static function (Blueprint $table) {
            $table->foreignId('announcement_id')->constrained('announcements')->cascadeOnDelete();
            $table->foreignId('radio_station_id')->constrained('radio_stations')->cascadeOnDelete();
            $table->timestamps();
            $table->primary(['announcement_id', 'radio_station_id'], 'announcement_id_radio_station_id_primary');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('announcements_radio_stations');
    }
};

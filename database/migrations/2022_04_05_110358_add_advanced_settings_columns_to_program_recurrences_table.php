<?php

use App\Models\Radio\ProgramRecurrence;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('program_recurrences', function (Blueprint $table) {
            $table->json('month_week')->after('time');
            $table->enum('broadcast_week', array_keys(ProgramRecurrence::BROADCAST_WEEK))
                ->default(1)->after('month_week');
            $table->boolean('holidays_break')->default(true)->after('broadcast_week');
        });
    }

    public function down(): void
    {
        Schema::table('program_recurrences', function (Blueprint $table) {
            $table->dropColumn('month_week');
            $table->dropColumn('broadcast_week');
            $table->dropColumn('holidays_break');
        });
    }
};

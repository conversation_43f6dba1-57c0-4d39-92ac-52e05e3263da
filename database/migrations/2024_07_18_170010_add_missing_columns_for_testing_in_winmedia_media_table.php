<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->string('disc')->nullable()->after('album');
            $table->string('track')->nullable()->after('disc');
            $table->dateTime('modify')->nullable()->after('comment');
            $table->string('_image_artiste')->nullable()->after('_proprietes');
            $table->string('_chemin_morceau')->nullable()->after('_image_artiste');
            $table->string('_image_cover')->nullable()->after('_artistes_similaires');
            $table->string('_proprietaire')->nullable()->after('_image_cover');
            $table->string('_compilation_id')->nullable()->after('_proprietaire');
        });
    }

    public function down(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->dropColumn('disc');
            $table->dropColumn('track');
            $table->dropColumn('modify');
            $table->dropColumn('_image_artiste');
            $table->dropColumn('_chemin_morceau');
            $table->dropColumn('_image_cover');
            $table->dropColumn('_proprietaire');
            $table->dropColumn('_compilation_id');
        });
    }
};

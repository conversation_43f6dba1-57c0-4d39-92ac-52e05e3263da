<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->timestamp('archivage_created_at')->nullable();
            $table->string('winmedia_presence')->default('not_present')->comment('is_present not_present to_transfer');
        });

        DB::statement('UPDATE winmedia_media SET winmedia_presence = \'is_present\' WHERE imedia IS NOT NULL');
        DB::statement('UPDATE winmedia_media SET winmedia_presence = \'not_present\', category_plateforme = 0 WHERE imedia IS NULL');
    }

    public function down(): void
    {
        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->dropColumn('archivage_created_at');
            $table->dropColumn('winmedia_presence');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('content_location', function (Blueprint $table) {
            $table->id();
            $table->morphs('content');
            $table->morphs('location');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('content_location');
    }
};

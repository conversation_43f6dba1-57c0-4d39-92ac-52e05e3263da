<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('performers_members', function (Blueprint $table) {
            $table->dropPrimary();
            $table->dropUnique(['performer_id', 'member_id', 'index']);
        });

        Schema::table('performers_members', function (Blueprint $table) {
            $table->id();
            $table->renameColumn('role', 'roles');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('performers_members', function (Blueprint $table) {
            $table->dropColumn('id');
            $table->renameColumn('roles', 'role');
        });

        Schema::table('performers_members', function (Blueprint $table) {
            $table->primary(['performer_id', 'member_id']);
            $table->unique(['performer_id', 'member_id', 'index']);
        });
    }
};

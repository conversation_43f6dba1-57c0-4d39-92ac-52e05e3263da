<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('settings', static function (Blueprint $table) {
            $table->string('bluesky_url')->nullable()->after('youtube_url');
            $table->string('mastodon_url')->nullable()->after('bluesky_url');
        });
    }

    public function down(): void
    {
        Schema::table('settings', static function (Blueprint $table) {
            $table->dropColumn('bluesky_url');
            $table->dropColumn('mastodon_url');
        });
    }
};

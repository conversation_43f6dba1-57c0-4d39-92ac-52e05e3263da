<?php

use App\Models\Events\Event;
use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Map\PlaceType;
use App\Models\Map\Point;
use App\Models\News\NewsArticle;
use App\Models\Radio\RadioStation;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use MatanYadaev\EloquentSpatial\Objects\Point as EloquentSpatialPoint;

return new class extends Migration
{
    public function up(): void
    {
        $this->moveGeoData(
            'events',
            Event::class,
            'location_name',
            'location_latitude',
            'location_longitude',
        );

        $this->moveGeoData(
            'news_articles',
            NewsArticle::class,
            'location_name',
            'location_latitude',
            'location_longitude',
        );

        $this->moveGeoData(
            'radio_stations',
            RadioStation::class,
            'name',
            'latitude',
            'longitude',
            PlaceType::RadioStudio,
        );

        Schema::table('events', function (Blueprint $table) {
            $table->dropColumn(['location_name', 'location_latitude', 'location_longitude']);
        });
        Schema::table('news_articles', function (Blueprint $table) {
            // On conserve la colonne "location_name" dans le cas
            // des actualités, car celle-ci a pu être renseignée sans
            // coordonnées associées.
            $table->dropColumn(['location_latitude', 'location_longitude']);
        });
        Schema::table('radio_stations', function (Blueprint $table) {
            $table->dropColumn(['latitude', 'longitude']);
        });
    }

    public function down(): void
    {
        Schema::table('events', function (Blueprint $table) {
            $table->string('location_name')->nullable();
            $table->string('location_latitude')->nullable();
            $table->string('location_longitude')->nullable();
        });

        Schema::table('news_articles', function (Blueprint $table) {
            //$table->string('location_name')->nullable();
            $table->string('location_latitude')->nullable();
            $table->string('location_longitude')->nullable();
        });

        Schema::table('radio_stations', function (Blueprint $table) {
            $table->double('latitude')->nullable();
            $table->double('longitude')->nullable();
        });

        foreach (ContentLocation::lazy() as $contentLocation) {
            if ($contentLocation->location_type === Point::class) {
                $point = DB::table('points')
                    ->where('id', $contentLocation->location_id)
                    ->select([
                        'id',
                        'place_id',
                        DB::raw('ST_X(coord) as lat'),
                        DB::raw('ST_Y(coord) as lng'),
                        'former_location_name',
                    ])
                    ->orderByDesc('id')
                    ->first();

                if ($point) {
                    if ($contentLocation->content_type === Event::class) {
                        DB::table('events')
                            ->where('id', $contentLocation->content_id)
                            ->update([
                                'location_name' => $point->former_location_name,
                                'location_latitude' => $point->lat,
                                'location_longitude' => $point->lng,
                            ]);
                    } elseif ($contentLocation->content_type === NewsArticle::class) {
                        if ($point->former_location_name) {
                            DB::table('news_articles')
                                ->where('id', $contentLocation->content_id)
                                ->update(['location_name' => $point->former_location_name]);
                        }

                        DB::table('news_articles')
                            ->where('id', $contentLocation->content_id)
                            ->update([
                                'location_latitude' => $point->lat,
                                'location_longitude' => $point->lng,
                            ]);
                    } elseif ($contentLocation->content_type === RadioStation::class) {
                        DB::table('radio_stations')
                            ->where('id', $contentLocation->content_id)
                            ->update([
                                'latitude' => $point->lat,
                                'longitude' => $point->lng,
                            ]);
                    }
                }

            } elseif ($contentLocation->location_type === Place::class) {
                $place = DB::table('places')->find($contentLocation->location_id);

                if ($place) {
                    // Nous ignorons le cas des stations radio dans le "if"
                    // ci-dessous, car le nom du lieu associé est directement
                    // issu du nom de la station, nous n'avons donc rien à
                    // rétablir dans leur cas, elles possèdent toujours
                    // leurs noms.
                    if ($contentLocation->content_type === Event::class) {
                        DB::table('events')
                            ->where('id', $contentLocation->content_id)
                            ->update(['location_name' => $place->name]);
                    } elseif ($contentLocation->content_type === NewsArticle::class) {
                        DB::table('news_articles')
                            ->where('id', $contentLocation->content_id)
                            ->update(['location_name' => $place->name]);
                    }

                    $point = DB::table('points')
                        ->where('place_id', $place->id)
                        ->select([
                            'id',
                            'place_id',
                            DB::raw('ST_X(coord) as lat'),
                            DB::raw('ST_Y(coord) as lng'),
                        ])
                        ->orderByDesc('id')
                        ->first();

                    if ($point) {
                        if ($contentLocation->content_type === Event::class) {
                            DB::table('events')
                                ->where('id', $contentLocation->content_id)
                                ->update([
                                    'location_latitude' => $point->lat,
                                    'location_longitude' => $point->lng,
                                ]);
                        } elseif ($contentLocation->content_type === NewsArticle::class) {
                            DB::table('news_articles')
                                ->where('id', $contentLocation->content_id)
                                ->update([
                                    'location_latitude' => $point->lat,
                                    'location_longitude' => $point->lng,
                                ]);
                        } elseif ($contentLocation->content_type === RadioStation::class) {
                            DB::table('radio_stations')
                                ->where('id', $contentLocation->content_id)
                                ->update([
                                    'latitude' => $point->lat,
                                    'longitude' => $point->lng,
                                ]);
                        }
                    }
                }
            }
        }

        DB::table('points')->truncate();
        DB::table('places')->delete();
        DB::table('content_location')->truncate();
    }

    private function moveGeoData(
        string $tableName,
        string $contentType,
        string $nameColumn,
        string $latitudeColumn,
        string $longitudeColumn,
        ?PlaceType $placeType = null,
    ): void {
        $geoData = DB::table($tableName)
            ->select('id', $nameColumn, $latitudeColumn, $longitudeColumn)
            ->whereNotNull($latitudeColumn)
            ->whereNotNull($longitudeColumn)
            ->get();

        foreach ($geoData as $data) {
            $lng = (float) $data->{$longitudeColumn};
            $lat = (float) $data->{$latitudeColumn};

            if ($placeType && ! is_null($data->{$nameColumn}) && trim($data->{$nameColumn}) !== '') {
                $place = Place::create([
                    'name' => trim($data->{$nameColumn}),
                    'type' => $placeType->value,
                ]);

                Point::create([
                    'coord' => new EloquentSpatialPoint($lat, $lng),
                    'place_id' => $place->id,
                ]);

                ContentLocation::create([
                    'content_type' => $contentType,
                    'content_id' => $data->id,
                    'location_type' => Place::class,
                    'location_id' => $place->id,
                ]);
            } else {
                $point = Point::create([
                    'coord' => new EloquentSpatialPoint($lat, $lng),
                ]);

                // Parce que la colonne "former_location_name" n'est pas
                // déclarée comme "fillable" au sein du model Point et qu'il
                // n'est pas spécialement pertinent qu'elle le soit.
                $locationName = isset($data->{$nameColumn}) ? trim($data->{$nameColumn}) : null;
                if ($locationName) {
                    DB::table('points')
                        ->where('id', $point->id)
                        ->update(['former_location_name' => $locationName]);
                }

                ContentLocation::create([
                    'content_type' => $contentType,
                    'content_id' => $data->id,
                    'location_type' => Point::class,
                    'location_id' => $point->id,
                ]);
            }
        }
    }
};

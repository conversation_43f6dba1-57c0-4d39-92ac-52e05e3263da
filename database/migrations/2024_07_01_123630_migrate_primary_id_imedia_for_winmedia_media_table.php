<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // DropForeign before dropPrimary on imedia
        if (app()->environment('local', 'testing')) {
            Schema::table('winmedia_historique', function (Blueprint $table) {
                $table->dropForeign(['imedia']);
            });
        }
        Schema::table('users_favorite_songs', function (Blueprint $table) {
            $table->dropForeign(['winmedia_song_id']);
        });
        Schema::table('podcasts_songs', function (Blueprint $table) {
            $table->dropForeign(['winmedia_song_id']);
        });
        Schema::table('playlists_songs', function (Blueprint $table) {
            $table->dropForeign(['winmedia_song_id']);
        });
        Schema::table('live_broadcastings', function (Blueprint $table) {
            $table->dropForeign(['winmedia_song_id']);
        });

        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->dropPrimary('imedia');
            $table->integer('id')->nullable()->first();
        });

        DB::statement('UPDATE winmedia_media SET id = imedia');

        if (config('database.default') === 'pgsql') {
            DB::statement('DROP SEQUENCE IF EXISTS winmedia_media_id_seq');
        }

        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->primary('id');
            $table->integer('imedia')->nullable()->change();
            $table->index('imedia');
        });

        $maxImedia = DB::table('winmedia_media')->max('imedia');

        Schema::table('winmedia_media', static function (Blueprint $table) use ($maxImedia) {
            $table->integer('id')->autoIncrement()->from($maxImedia + 1)->change();
        });

        Schema::table('users_favorite_songs', function (Blueprint $table) {
            $table->renameColumn('winmedia_song_id', 'song_id');
            $table->foreign('song_id')
                ->references('id')
                ->on('winmedia_media')
                ->cascadeOnDelete();
        });
        Schema::table('podcasts_songs', function (Blueprint $table) {
            $table->renameColumn('winmedia_song_id', 'song_id');
            $table->foreign('song_id')
                ->references('id')
                ->on('winmedia_media')
                ->cascadeOnDelete();
        });
        Schema::table('playlists_songs', function (Blueprint $table) {
            $table->renameColumn('winmedia_song_id', 'song_id');
            $table->foreign('song_id')
                ->references('id')
                ->on('winmedia_media')
                ->cascadeOnDelete();
        });
        Schema::table('live_broadcastings', function (Blueprint $table) {
            $table->renameColumn('winmedia_song_id', 'song_id');
            $table->foreign('song_id')
                ->references('id')
                ->on('winmedia_media')
                ->cascadeOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('users_favorite_songs', function (Blueprint $table) {
            $table->dropForeign(['song_id']);
        });
        Schema::table('podcasts_songs', function (Blueprint $table) {
            $table->dropForeign(['song_id']);
        });
        Schema::table('playlists_songs', function (Blueprint $table) {
            $table->dropForeign(['song_id']);
        });
        Schema::table('live_broadcastings', function (Blueprint $table) {
            $table->dropForeign(['song_id']);
        });

        // Drop the new primary key and the id column
        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->dropIndex(['imedia']);
            $table->dropColumn('id');
        });

        // Restore the original primary key and column settings
        Schema::table('winmedia_media', static function (Blueprint $table) {
            $table->integer('imedia')->nullable(false)->change();
            $table->primary('imedia');
        });

        // Restore foreign keys
        if (app()->environment('local', 'testing')) {
            Schema::table('winmedia_historique', function (Blueprint $table) {
                $table->foreign('imedia')
                    ->references('imedia')
                    ->on('winmedia_media')
                    ->cascadeOnDelete();
            });
        }
        Schema::table('users_favorite_songs', function (Blueprint $table) {
            $table->renameColumn('song_id', 'winmedia_song_id');
            $table->foreign('winmedia_song_id')
                ->references('imedia')
                ->on('winmedia_media')
                ->cascadeOnDelete();
        });
        Schema::table('podcasts_songs', function (Blueprint $table) {
            $table->renameColumn('song_id', 'winmedia_song_id');
            $table->foreign('winmedia_song_id')
                ->references('imedia')
                ->on('winmedia_media')
                ->cascadeOnDelete();
        });
        Schema::table('playlists_songs', function (Blueprint $table) {
            $table->renameColumn('song_id', 'winmedia_song_id');
            $table->foreign('winmedia_song_id')
                ->references('imedia')
                ->on('winmedia_media')
                ->cascadeOnDelete();
        });
        Schema::table('live_broadcastings', function (Blueprint $table) {
            $table->renameColumn('song_id', 'winmedia_song_id');
            $table->foreign('winmedia_song_id')
                ->references('imedia')
                ->on('winmedia_media')
                ->cascadeOnDelete();
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (DB::getDriverName() === 'pgsql') {
            DB::statement('CREATE EXTENSION IF NOT EXISTS postgis;');
        }

        Schema::create('points', static function (Blueprint $table) {
            $table->id();
            $table->point('coord');
            $table->foreignId('place_id')->constrained('places')->cascadeOnDelete();
            $table->dateTime('since')->nullable();
            $table->dateTime('until')->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('points');

        if (DB::getDriverName() === 'pgsql') {
            DB::statement('DROP EXTENSION IF EXISTS postgis;');
        }
    }
};

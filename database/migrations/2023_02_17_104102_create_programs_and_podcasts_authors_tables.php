<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('programs_authors', static function (Blueprint $table) {
            $table->foreignId('program_id')->constrained('programs')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->unsignedSmallInteger('index');
            $table->primary(['program_id', 'user_id']);
            $table->unique(['program_id', 'user_id', 'index']);
            $table->timestamps();
        });

        Schema::create('podcasts_authors', static function (Blueprint $table) {
            $table->foreignId('podcast_id')->constrained('podcasts')->cascadeOnDelete();
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->unsignedSmallInteger('index');
            $table->primary(['podcast_id', 'user_id']);
            $table->unique(['podcast_id', 'user_id', 'index']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('programs_authors');
        Schema::dropIfExists('podcasts_authors');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('live_broadcastings', static function (Blueprint $table) {
            $table->id();
            $table->foreignId('winmedia_radio_station_id')
                ->constrained('radio_stations', 'winmedia_id')
                ->cascadeOnDelete();
            $table->integer('winmedia_song_id');
            $table->foreign('winmedia_song_id')
                ->references('imedia')
                ->on('winmedia_media')
                ->cascadeOnDelete();
            $table->foreignId('dedication_user_id')->nullable()->constrained('users')->nullOnDelete();
            $table->unsignedInteger('real_duration');
            $table->dateTime('started_at');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('live_broadcastings');
    }
};

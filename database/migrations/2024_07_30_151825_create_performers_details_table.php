<?php

use App\Models\Performers\Album;
use App\Models\Performers\AlbumDetail;
use App\Models\Performers\Performer;
use App\Models\Performers\PerformerDetail;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('performer_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('performer_id')->nullable()->constrained('performers')->onDelete('set null');
            $table->string('performer_name')->nullable();
            $table->string('musicbrainz_id')->nullable();
            $table->string('musicbrainz_type')->nullable();
            $table->text('biography')->nullable();
            $table->string('alias_name')->nullable();
            $table->string('begin_city')->nullable();
            $table->string('current_city')->nullable();
            $table->year('begin_year')->nullable();
            $table->year('end_year')->nullable();
            $table->string('url_web')->nullable();
            $table->string('url_wiki')->nullable();
            $table->string('url_discogs')->nullable();
            $table->string('url_instagram')->nullable();
            $table->string('url_facebook')->nullable();
            $table->timestamps();
        });

        Performer::all()->each(function ($performer) {
            PerformerDetail::create([
                'performer_id' => $performer->id,
                'performer_name' => $performer->name,
            ]);
        });

        Schema::create('album_details', function (Blueprint $table) {
            $table->id();
            $table->foreignId('album_id')->nullable()->constrained('albums')->onDelete('set null');
            $table->string('album_name')->nullable();
            $table->string('musicbrainz_id')->nullable();
            $table->string('musicbrainz_type')->nullable();
            $table->text('localisation')->nullable();
            $table->timestamps();
        });

        Album::all()->each(function ($album) {
            AlbumDetail::create([
                'album_id' => $album->id,
                'album_name' => $album->name,
            ]);
        });

        Schema::create('members', static function (Blueprint $table) {
            $table->id();
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('nick_name')->nullable();
            $table->timestamps();
        });
        Schema::create('performers_members', static function (Blueprint $table) {
            $table->foreignId('performer_id')->constrained('performers')->cascadeOnDelete();
            $table->foreignId('member_id')->constrained('members')->cascadeOnDelete();
            $table->unsignedSmallInteger('index');
            $table->primary(['performer_id', 'member_id']);
            $table->unique(['performer_id', 'member_id', 'index']);
            $table->string('role')->nullable();
            $table->year('begin_year')->nullable();
            $table->year('end_year')->nullable();
            $table->timestamps();
        });

    }

    public function down(): void
    {
        Schema::dropIfExists('performers_members');
        Schema::dropIfExists('members');
        Schema::dropIfExists('album_details');
        Schema::dropIfExists('performer_details');
    }
};

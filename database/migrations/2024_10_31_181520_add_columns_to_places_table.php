<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('places', static function (Blueprint $table) {
            $table->text('description')->nullable()->after('informations');
            $table->string('url')->nullable()->after('description');
        });
    }

    public function down(): void
    {
        Schema::table('places', static function (Blueprint $table) {
            $table->dropColumn('description');
            $table->dropColumn('url');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('program_recurrences', static function (Blueprint $table) {
            $table->time('local_time')->after('time');
            $table->string('local_time_tz')->after('local_time');
        });
    }

    public function down(): void
    {
        Schema::table('program_recurrences', static function (Blueprint $table) {
            $table->dropColumn('local_time');
            $table->dropColumn('local_time_tz');
        });
    }
};

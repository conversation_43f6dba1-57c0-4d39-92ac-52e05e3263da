<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('winmedia_media', function (Blueprint $table) {
            $table->foreignId('album_id')->nullable()->constrained('albums')->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('winmedia_media', function (Blueprint $table) {
            $table->dropConstrainedForeignId('album_id');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        // performer_details

        Schema::table('performer_details', function (Blueprint $table) {
            $table->date('begin_date')->nullable()->after('begin_year');
            $table->date('end_date')->nullable()->after('end_year');
        });

        DB::table('performer_details')->get()->each(function ($row) {
            if ($row->begin_year) {
                DB::table('performer_details')
                    ->where('id', $row->id)
                    ->update(['begin_date' => $row->begin_year . '-01-01']);
            }
            if ($row->end_year) {
                DB::table('performer_details')
                    ->where('id', $row->id)
                    ->update(['end_date' => $row->end_year . '-01-01']);
            }
        });

        Schema::table('performer_details', function (Blueprint $table) {
            $table->dropColumn('begin_year');
            $table->dropColumn('end_year');
        });

        // performers_members

        Schema::table('performers_members', function (Blueprint $table) {
            $table->date('begin_date')->nullable()->after('begin_year');
            $table->date('end_date')->nullable()->after('end_year');
        });

        DB::table('performers_members')->get()->each(function ($row) {
            if ($row->begin_year) {
                DB::table('performers_members')
                    ->where('id', $row->id)
                    ->update(['begin_date' => $row->begin_year . '-01-01']);
            }
            if ($row->end_year) {
                DB::table('performers_members')
                    ->where('id', $row->id)
                    ->update(['end_date' => $row->end_year . '-01-01']);
            }
        });

        Schema::table('performers_members', function (Blueprint $table) {
            $table->dropColumn('begin_year');
            $table->dropColumn('end_year');
        });
    }

    public function down(): void
    {
        // performer_details

        Schema::table('performer_details', function (Blueprint $table) {
            $table->year('begin_year')->nullable()->after('begin_date');
            $table->year('end_year')->nullable()->after('end_date');
        });

        DB::table('performer_details')->get()->each(function ($row) {
            if ($row->begin_date) {
                DB::table('performer_details')
                    ->where('id', $row->id)
                    ->update(['begin_year' => substr($row->begin_date, 0, 4)]);
            }
            if ($row->end_date) {
                DB::table('performer_details')
                    ->where('id', $row->id)
                    ->update(['end_year' => substr($row->end_date, 0, 4)]);
            }
        });

        Schema::table('performer_details', function (Blueprint $table) {
            $table->dropColumn('begin_date');
            $table->dropColumn('end_date');
        });

        // performers_members

        Schema::table('performers_members', function (Blueprint $table) {
            $table->year('begin_year')->nullable()->after('begin_date');
            $table->year('end_year')->nullable()->after('end_date');
        });

        DB::table('performers_members')->get()->each(function ($row) {
            if ($row->begin_date) {
                DB::table('performers_members')
                    ->where('id', $row->id)
                    ->update(['begin_year' => substr($row->begin_date, 0, 4)]);
            }
            if ($row->end_date) {
                DB::table('performers_members')
                    ->where('id', $row->id)
                    ->update(['end_year' => substr($row->end_date, 0, 4)]);
            }
        });

        Schema::table('performers_members', function (Blueprint $table) {
            $table->dropColumn('begin_date');
            $table->dropColumn('end_date');
        });
    }
};

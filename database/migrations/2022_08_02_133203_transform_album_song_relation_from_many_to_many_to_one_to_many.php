<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('albums', static function (Blueprint $table) {
            $table->dropConstrainedForeignId('performer_id');
        });
        Schema::dropIfExists('albums_songs');
    }

    public function down(): void
    {
        Schema::table('albums', static function (Blueprint $table) {
            $table->foreignId('performer_id')->after('id')->nullable()->constrained('performers')->cascadeOnDelete();
        });
        Schema::create('albums_songs', static function (Blueprint $table) {
            $table->foreignId('album_id')->constrained('albums')->cascadeOnDelete();
            $table->integer('song_id');
            $table->foreign('song_id')->on('winmedia_media')->references('imedia')->cascadeOnDelete();
            $table->timestamps();
            $table->primary(['album_id', 'song_id']);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('live_broadcastings', static function (Blueprint $table) {
            $table->foreignId('podcast_id')
                ->nullable()
                ->after('winmedia_song_id')
                ->constrained('podcasts')
                ->cascadeOnDelete();
            $table->integer('winmedia_song_id')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('live_broadcastings', static function (Blueprint $table) {
            $table->dropConstrainedForeignId('podcast_id');
            $table->integer('winmedia_song_id')->nullable(false)->change();
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('announcements', static function (Blueprint $table) {
            $table->id();
            $table->morphs('announceable');
            $table->string('title')->nullable();
            $table->text('subtitle')->nullable();
            $table->dateTime('published_at');
            $table->dateTime('unpublished_at')->nullable();
            $table->boolean('active');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('announcements');
    }
};

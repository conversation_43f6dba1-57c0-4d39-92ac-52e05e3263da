<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('settings', static function (Blueprint $table) {
            $table->boolean('login_modal_on_init')->default(false)->after('matomo_id_site');
        });
    }

    public function down(): void
    {
        Schema::table('settings', static function (Blueprint $table) {
            $table->dropColumn('login_modal_on_init');
        });
    }
};

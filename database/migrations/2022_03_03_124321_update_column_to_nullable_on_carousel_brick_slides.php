<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('carousel_brick_slides', static function (Blueprint $table) {
            $table->string('label')->nullable()->change();
            $table->string('caption')->nullable()->change();
        });
    }

    public function down(): void
    {
        Schema::table('carousel_brick_slides', static function (Blueprint $table) {
            $table->string('label')->change();
            $table->string('caption')->change();
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('albums_songs', static function (Blueprint $table) {
            $table->foreignId('album_id')->constrained('albums')->cascadeOnDelete();
            $table->integer('song_id');
            $table->foreign('song_id')->on('winmedia_media')->references('imedia')->cascadeOnDelete();
            $table->timestamps();
            $table->primary(['album_id', 'song_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('albums_songs');
    }
};

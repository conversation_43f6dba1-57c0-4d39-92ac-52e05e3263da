<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('news_articles', static function (Blueprint $table) {
            $table->text('tags')->after('slug');
        });
        Schema::table('events', static function (Blueprint $table) {
            $table->text('tags')->after('title');
        });
    }

    public function down(): void
    {
        Schema::table('news_articles', static function (Blueprint $table) {
            $table->dropColumn('tags');
        });
        Schema::table('events', static function (Blueprint $table) {
            $table->dropColumn('tags');
        });
    }
};

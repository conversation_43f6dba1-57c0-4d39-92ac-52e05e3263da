<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('playlists_songs', static function (Blueprint $table) {
            $table->foreignId('playlist_id')->constrained('playlists')->cascadeOnDelete();
            $table->integer('winmedia_song_id');
            $table->foreign('winmedia_song_id')
                ->references('imedia')
                ->on('winmedia_media')
                ->cascadeOnDelete();
            $table->primary(['playlist_id', 'winmedia_song_id']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('playlists_songs');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('playlists_radio_stations', static function (Blueprint $table) {
            $table->foreignId('playlist_id')->constrained('playlists')->cascadeOnDelete();
            $table->foreignId('radio_station_id')->constrained('radio_stations')->cascadeOnDelete();
            $table->primary(['playlist_id', 'radio_station_id']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('playlists_radio_stations');
    }
};

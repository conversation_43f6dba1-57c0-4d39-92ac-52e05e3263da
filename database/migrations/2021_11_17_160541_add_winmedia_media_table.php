<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::create('winmedia_media', static function (Blueprint $table) {
            $table->integer('imedia')->primary();
            $table->string('performer');
            $table->string('title');
            $table->string('version');
            $table->unsignedInteger('duration');
            $table->string('album');
            $table->string('genre');
            $table->string('publisher');
            $table->string('comment');
            $table->string('language')->nullable();
            $table->string('_proprietes', 384)->nullable();
            $table->string('_station', 384)->nullable();
            $table->string('_artistes_similaires', 384)->nullable();
            $table->dateTime('dateheure_mise_a_jour');
            $table->string('elasticsearch_action');
            $table->string('year', 16)->nullable();
            $table->integer('category_plateforme')->default(\App\Models\Audio\Song::CATEGORY_PLATEFORME_MUSICS);
        });
    }

    public function down(): void
    {
        if (! app()->environment('local', 'testing')) {
            return;
        }
        Schema::dropIfExists('winmedia_media');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('playlists_songs', static function (Blueprint $table) {
            $table->unsignedSmallInteger('index')->after('winmedia_song_id');
            $table->unique(['playlist_id', 'winmedia_song_id', 'index']);
        });
    }

    public function down(): void
    {
        Schema::table('playlists_songs', static function (Blueprint $table) {
            $table->dropUnique(['playlist_id', 'winmedia_song_id', 'index']);
            $table->dropColumn('index');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('labels', static function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->timestamps();
        });

        Schema::table('winmedia_media', function (Blueprint $table) {
            $table->foreignId('label_id')->nullable()->constrained('labels')->nullOnDelete();
        });

        Schema::table('albums', function (Blueprint $table) {
            $table->foreignId('label_id')->nullable()->constrained('labels')->nullOnDelete()->after('genre');
        });
    }

    public function down(): void
    {
        Schema::table('winmedia_media', function (Blueprint $table) {
            $table->dropConstrainedForeignId('label_id');
        });

        Schema::table('albums', function (Blueprint $table) {
            $table->dropConstrainedForeignId('label_id');
        });

        Schema::dropIfExists('labels');
    }
};

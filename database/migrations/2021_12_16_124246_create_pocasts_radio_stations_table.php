<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('podcasts_radio_stations', static function (Blueprint $table) {
            $table->foreignId('podcast_id')->constrained('podcasts')->cascadeOnDelete();
            $table->foreignId('radio_station_id')->constrained('radio_stations')->cascadeOnDelete();
            $table->primary(['podcast_id', 'radio_station_id']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('podcasts_radio_stations');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('performer_details', function (Blueprint $table) {
            $table->string('url_bandcamp')->nullable()->after('url_youtube');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('performer_details', function (Blueprint $table) {
            $table->dropColumn('url_bandcamp');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_journeys', function (Blueprint $table) {
            $table->index(
                ['played_audio_source_class', 'played_audio_source_id'],
                'idx_audio_source'
            );
            $table->index(
                ['played_sub_audio_source_class', 'played_sub_audio_source_id'],
                'idx_sub_audio_source'
            );
            $table->index('current_route_key', 'idx_current_route');
        });
    }

    public function down(): void
    {
        Schema::table('user_journeys', function (Blueprint $table) {
            $table->dropIndex('idx_current_route');
            $table->dropIndex('idx_sub_audio_source');
            $table->dropIndex('idx_audio_source');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('users_favorite_songs', static function (Blueprint $table) {
            $table->foreignId('user_id')->constrained('users')->cascadeOnDelete();
            $table->integer('winmedia_song_id');
            $table->foreign('winmedia_song_id')
                ->references('imedia')
                ->on('winmedia_media')
                ->cascadeOnDelete();
            $table->primary(['user_id', 'winmedia_song_id']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('users_favorite_songs');
    }
};

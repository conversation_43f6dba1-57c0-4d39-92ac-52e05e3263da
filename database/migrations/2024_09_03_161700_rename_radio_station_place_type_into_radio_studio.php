<?php

use App\Models\Map\PlaceType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        if (Schema::hasTable('places')) {
            DB::table('places')
                ->where('type', 'radio_station')
                ->update([
                    'type' => PlaceType::RadioStudio->value,
                ]);
        }
    }

    public function down(): void
    {
        if (Schema::hasTable('places')) {
            DB::table('places')
                ->where('type', PlaceType::RadioStudio->value)
                ->update([
                    'type' => 'radio_station',
                ]);
        }
    }
};

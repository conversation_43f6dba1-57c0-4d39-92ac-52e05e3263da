<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_journeys', static function (Blueprint $table) {
            $table->foreignId('selected_default_setting_radio_station_id')
                ->after('selected_radio_station_universe_id')
                ->nullable()
                ->constrained('radio_stations')
                ->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('user_journeys', static function (Blueprint $table) {
            $table->dropConstrainedForeignId('selected_default_setting_radio_station_id');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('podcasts_songs', static function (Blueprint $table) {
            $table->foreignId('podcast_id')->constrained('podcasts')->cascadeOnDelete();
            $table->integer('winmedia_song_id');
            $table->foreign('winmedia_song_id')
                ->references('imedia')
                ->on('winmedia_media')
                ->cascadeOnDelete();
            $table->unsignedSmallInteger('index');
            $table->primary(['podcast_id', 'winmedia_song_id']);
            $table->unique(['podcast_id', 'winmedia_song_id', 'index']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('podcasts_songs');
    }
};

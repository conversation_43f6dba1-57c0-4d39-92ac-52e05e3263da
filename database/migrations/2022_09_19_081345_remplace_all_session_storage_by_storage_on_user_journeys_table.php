<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('user_journeys', static function (Blueprint $table) {
            $table->string('current_route_key')
                ->after('selected_radio_station_universe_id')
                ->nullable();
            $table->string('played_audio_source_class')
                ->after('current_route_key')
                ->nullable();
            $table->unsignedBigInteger('played_audio_source_id')
                ->after('played_audio_source_class')
                ->nullable();
            $table->json('played_audio_source_params')
                ->after('played_audio_source_id')
                ->nullable();
            $table->string('played_sub_audio_source_class')
                ->after('played_audio_source_params')
                ->nullable();
            $table->unsignedBigInteger('played_sub_audio_source_id')
                ->after('played_sub_audio_source_class')
                ->nullable();
            $table->boolean('player_audio_stream_is_playing')
                ->after('played_sub_audio_source_id')
                ->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('user_journeys', static function (Blueprint $table) {
            $table->dropColumn([
                'current_route_key',
                'played_audio_source_class',
                'played_audio_source_id',
                'played_audio_source_params',
                'played_sub_audio_source_class',
                'played_sub_audio_source_id',
                'player_audio_stream_is_playing',
            ]);
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('programs', static function (Blueprint $table) {
            $table->id();
            $table->foreignId('main_program_id')->nullable()->constrained('programs')->cascadeOnDelete();
            $table->foreignId('thematic_id')->nullable()->constrained('thematics')->nullOnDelete();
            $table->string('title');
            $table->string('tags');
            $table->text('description');
            $table->unsignedInteger('duration');
            $table->string('start_podcast_artist')->nullable();
            $table->string('start_podcast_title')->nullable();
            $table->string('start_podcast_version')->nullable();
            $table->string('end_podcast_artist')->nullable();
            $table->string('end_podcast_title')->nullable();
            $table->string('end_podcast_version')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('programs');
    }
};

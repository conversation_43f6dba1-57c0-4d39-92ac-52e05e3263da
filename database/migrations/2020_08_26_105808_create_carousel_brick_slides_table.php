<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('carousel_brick_slides', static function (Blueprint $table) {
            $table->id();
            $table->foreignId('brick_id')->constrained('bricks')->cascadeOnDelete();
            $table->string('label');
            $table->string('caption');
            $table->unsignedInteger('position');
            $table->boolean('active');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('carousel_brick_slides');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('podcasts', static function (Blueprint $table) {
            $table->text('tags')->change();
        });
    }

    public function down(): void
    {
        Schema::table('podcasts', static function (Blueprint $table) {
            $table->string('tags')->change();
        });
    }
};

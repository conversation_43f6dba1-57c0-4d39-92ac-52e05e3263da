<?php

use App\Models\Radio\Program;
use App\Models\Radio\ProgramRecurrence;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('program_recurrences_sub_programs', static function (Blueprint $table) {
            $table->foreignId('program_recurrence_id')->constrained('program_recurrences')->cascadeOnDelete();
            $table->foreignId('sub_program_id')->constrained('programs')->cascadeOnDelete();
            $table->time('time');
            $table->timestamps();
            $table->primary(
                ['program_recurrence_id', 'sub_program_id'],
                'program_recurrences_id_sub_program_id_primary'
            );
        });
        foreach (ProgramRecurrence::get() as $programRecurrence) {
            $subProgramIdsArray = json_decode(
                $programRecurrence->getRawOriginal('sub_program_ids'),
                true,
                512,
                JSON_THROW_ON_ERROR
            );
            $subProgramIdsWithPivot = collect($subProgramIdsArray)
                ->mapWithKeys(static fn (array $subProgramId) => [
                    $subProgramId['id'] => ['time' => $subProgramId['time']],
                ])
                ->toArray();
            $programRecurrence->subPrograms()->sync($subProgramIdsWithPivot);
        }
        Schema::table('program_recurrences', static function (Blueprint $table) {
            $table->dropColumn('sub_program_ids');
        });
    }

    public function down(): void
    {
        Schema::table('program_recurrences', static function (Blueprint $table) {
            $table->json('sub_program_ids')->after('radio_station_ids');
        });
        foreach (ProgramRecurrence::with('subPrograms:id')->get() as $programRecurrence) {
            $subProgramsJson = $programRecurrence->subPrograms->map(static fn (Program $program) => [
                'id' => $program->id,
                'time' => $program->pivot->time,
            ]);
            $programRecurrence->forceFill(['sub_program_ids' => $subProgramsJson])->save();
        }
        Schema::dropIfExists('program_recurrences_sub_programs');
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('settings', static function (Blueprint $table) {
            $table->integer('map_events_week_interval')->default(3)->after('ga4_tracking_id');
            $table->integer('map_news_week_interval')->default(3)->after('map_events_week_interval');
            $table->integer('map_playlists_week_interval')->default(5)->after('map_news_week_interval');
            $table->integer('map_podcasts_week_interval')->default(4)->after('map_podcasts_week_interval');
            $table->integer('map_performers_week_interval')->default(4)->after('map_performers_week_interval');
        });

        Schema::table('radio_stations', static function (Blueprint $table) {
            $table->integer('map_events_week_interval')->nullable()->after('position');
            $table->integer('map_news_week_interval')->nullable()->after('map_events_week_interval');
            $table->integer('map_playlists_week_interval')->nullable()->after('map_news_week_interval');
            $table->integer('map_podcasts_week_interval')->nullable()->after('map_podcasts_week_interval');
            $table->integer('map_performers_week_interval')->nullable()->after('map_performers_week_interval');
        });
    }

    public function down(): void
    {
        Schema::table('settings', static function (Blueprint $table) {
            $table->dropColumn('map_events_week_interval');
            $table->dropColumn('map_news_week_interval');
            $table->dropColumn('map_playlists_week_interval');
            $table->dropColumn('map_podcasts_week_interval');
            $table->dropColumn('map_performers_week_interval');
        });

        Schema::table('radio_stations', static function (Blueprint $table) {
            $table->dropColumn('map_events_week_interval');
            $table->dropColumn('map_news_week_interval');
            $table->dropColumn('map_playlists_week_interval');
            $table->dropColumn('map_podcasts_week_interval');
            $table->dropColumn('map_performers_week_interval');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('news_articles', static function (Blueprint $table) {
            $table->foreignId('user_id')->after('thematic_id')->nullable()->constrained('users')->nullOnDelete();
        });
        Schema::table('events', static function (Blueprint $table) {
            $table->foreignId('user_id')->after('thematic_id')->nullable()->constrained('users')->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('news_articles', static function (Blueprint $table) {
            $table->dropConstrainedForeignId('user_id');
        });
        Schema::table('events', static function (Blueprint $table) {
            $table->dropConstrainedForeignId('user_id');
        });
    }
};

<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('news_articles', static function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('slug');
            $table->string('location_name')->nullable();
            $table->string('location_latitude')->nullable();
            $table->string('location_longitude')->nullable();
            $table->mediumText('description')->nullable();
            $table->foreignId('thematic_id')->nullable()->constrained('thematics')->nullOnDelete();
            $table->dateTime('published_at');
            $table->boolean('active');
            $table->timestamps();
        });
        Schema::create('news_article_radio_station', static function (Blueprint $table) {
            $table->foreignId('news_article_id')->constrained('news_articles')->cascadeOnDelete();
            $table->foreignId('radio_station_id')->constrained('radio_stations')->cascadeOnDelete();
            $table->primary(['news_article_id', 'radio_station_id'], 'news_article_radio_station_primary');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('news_article_radio_station');
        Schema::dropIfExists('news_articles');
    }
};

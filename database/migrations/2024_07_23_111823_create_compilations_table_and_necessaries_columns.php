<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('compilations', function (Blueprint $table) {
            $table->id();
            $table->timestamps();
        });
        Schema::table('albums', static function (Blueprint $table) {
            $table->foreignId('compilation_id')
                ->nullable()
                ->after('performer_id')
                ->constrained('compilations')
                ->nullOnDelete();
        });
    }

    public function down(): void
    {
        Schema::table('albums', function (Blueprint $table) {
            $table->dropForeign(['compilation_id']);
            $table->dropColumn('compilation_id');
        });
        Schema::dropIfExists('compilations');
    }
};

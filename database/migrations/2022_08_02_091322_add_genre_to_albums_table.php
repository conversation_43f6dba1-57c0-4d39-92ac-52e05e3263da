<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('albums', static function (Blueprint $table) {
            $table->string('genre')->after('name')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('albums', static function (Blueprint $table) {
            $table->dropColumn('genre');
        });
    }
};

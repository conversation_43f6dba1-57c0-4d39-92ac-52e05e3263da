<div {{ $attributes->merge(['class' => 'position-relative rounded rounded-lg program-card-component']) }}>
    <div class="program-img overflow-hidden">
        <img src="{{ $programCoverThumbUrl }}"
             alt="{{ $programName }}"
             width="122"
             height="122"
             loading="lazy">
    </div>
    <div class="mt-2">
        <div class="fw-bold text-truncate">{{ $programName }}</div>
        <div class="mt-1 small text-muted text-truncate">{{ $podcastsCount }} podcast{{ $podcastsCount > 1 ? 's' : null }}</div>
    </div>
    @if($actionMode === 'route')
        <a
            wire:click.prevent="$emitTo('router', 'nav:to', 'program_details', {{ collect([
                'bindings' => [
                    'program' => [
                        'model' => App\Models\Radio\Program::class,
                        'id' => $programId
                    ]
                ]
            ]) }})"
            class="stretched-link"
            href="{{ route('app.program.show', $programId) }}"
            title="Emission {{ $programName }}"
        ></a>
    @elseif($actionMode === 'event')
        <a
            wire:click="$emit('card:click', 'program', {{ $programId }})"
            class="stretched-link"
            title="Emission {{ $programName }}"
        ></a>
    @endif
</div>

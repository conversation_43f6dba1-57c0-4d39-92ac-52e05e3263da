@extends('layouts.admin.full')
@section('template')
    <h1>
        <i class="fas fa-tags fa-fw"></i>
        {{ __('breadcrumbs.orphan.edit', ['entity' => 'Label', 'detail' => $label->name]) }}
    </h1>
    <hr>
    <x:form::form method="PUT"
                  :action="route('label.update', $label)"
                  :bind="$label"
                  enctype="multipart/form-data"
                  x-data="{ buttonDisabled: false }"
                  x-on:submit="buttonDisabled = true">
        <div class="d-flex">
            <x:form::button.link class="btn-secondary me-3" :href="route('labels.index') . ($previousParams ?? null)">
                <i class="fas fa-undo fa-fw"></i>
                Retour
            </x:form::button.link>
            <x:form::button.submit x-bind:disabled="buttonDisabled">
                <i class="fas fa-save fa-fw"></i>
                Enregistrer
            </x:form::button.submit>
        </div>
        <x:common.forms.notice class="mt-3"/>
        <div class="row mb-n3" data-masonry>
            <div class="col-xl-6 mb-3">
                <x:admin.forms.card title="Nom label">
                    <x:form::input name="name" required />
                </x:admin.forms.card>
            </div>
            <div class="col-xl-6 mb-3">
                <livewire:admin.map.content-location
                    :content="$label ?? null"
                    placeSelectLabel="Lieu associé au label"
                    class="mb-3"
                />
            </div>
        </div>
    </x:form::form>
@endsection

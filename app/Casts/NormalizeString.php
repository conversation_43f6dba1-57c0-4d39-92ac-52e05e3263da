<?php

namespace App\Casts;

use Illuminate\Contracts\Database\Eloquent\CastsAttributes;
use Normalizer;

class NormalizeString implements CastsAttributes
{
    public function get($model, string $key, $value, array $attributes)
    {
        return Normalizer::normalize($value, Normalizer::FORM_C);
    }

    public function set($model, string $key, $value, array $attributes)
    {
        return Normalizer::normalize($value, Normalizer::FORM_C);
    }
}

<?php

namespace App\Console;

use App\Console\Commands\Announcements\ClearAnnouncements;
use App\Console\Commands\Dev\SimulateLiveBroadcasting;
use App\Console\Commands\Elasticsearch\RefreshSongsIndex;
use App\Console\Commands\Elasticsearch\ResetAll;
use App\Console\Commands\FilePond\DeleteTempUploadedFiles;
use App\Console\Commands\Performers\SyncPerformersAlbums;
use App\Console\Commands\Programs\GeneratePodcasts;
use App\Console\Commands\Stats\GetCurrentListeners;
use App\Console\Commands\Users\ClearSessionUserJourneys;
use Carbon\CarbonInterface;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;
use Illuminate\Support\Facades\Date;

class Kernel extends ConsoleKernel
{
    /** Define the application's command schedule. */
    protected function schedule(Schedule $schedule): void
    {
        if ($this->shouldRun()) {
            // Not run in a scheduled daily maintenance period
            $schedule->command('horizon:snapshot')->everyFiveMinutes();
            $schedule->command('sitemap:generate')->twiceDaily();
            $schedule->command('auth:clear-resets')->daily();
            $schedule->command('telescope:prune')->daily();
            $schedule->command('queue:stuck:notify')->twiceDaily(10, 16);
            $schedule->command('supervisor:downtime:notify')->everyFifteenMinutes();
            $schedule->command(RefreshSongsIndex::class)->withoutOverlapping()->everyFourMinutes();
            $schedule->command(GeneratePodcasts::class)->withoutOverlapping()->hourly();
        }

        if (app()->environment('local')) {
            $schedule->command(SimulateLiveBroadcasting::class)->everyThreeMinutes();
            $schedule->command('debugbar:clear')->everyThirtyMinutes();
        }
        $schedule->command(ClearSessionUserJourneys::class)->everyMinute();
        $schedule->command(ClearAnnouncements::class)->withoutOverlapping()->dailyAt('3:30');
        $schedule->command(SyncPerformersAlbums::class)->withoutOverlapping()->dailyAt('3:40');
        $schedule->command(ResetAll::class)->withoutOverlapping()->weeklyOn(Schedule::SUNDAY, '4:50');
        $schedule->command(GetCurrentListeners::class)->everyMinute();
        $schedule->command(DeleteTempUploadedFiles::class)->withoutOverlapping()->dailyAt('5:15');
    }

    /** Whether scheduled commands should run or not. */
    protected function shouldRun(): bool
    {
        // Add other conditions if necessary here.
        return ! $this->onScheduledDailyMaintenance();
    }

    /** Check if we currently are in a scheduled daily maintenance period. */
    protected function onScheduledDailyMaintenance(): bool
    {
        $dailyMaintenanceStart = $this->carbonDateFromStringTime('01:30');
        $dailyMaintenanceEnd = $this->carbonDateFromStringTime('06:30');

        return Date::now($this->scheduleTimezone())->between($dailyMaintenanceStart, $dailyMaintenanceEnd);
    }

    protected function carbonDateFromStringTime(string $time): CarbonInterface
    {
        $segments = explode(':', $time);

        return Date::now($this->scheduleTimezone())
            ->startOfDay()
            ->hours((int) $segments[0])
            ->minutes((int) $segments[1]);
    }

    /** Get the timezone that should be used by default for scheduled events. */
    protected function scheduleTimezone(): string
    {
        return 'Europe/Paris';
    }

    /** Register the commands for the application. */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');
        require base_path('routes/console.php');
    }
}

<?php

namespace App\Console\Commands\Performers;

use App\Console\Commands\CommandAbstract;
use App\Console\Commands\Elasticsearch\ResetPerformersIndex;
use App\Console\Commands\Elasticsearch\ResetSongsIndex;
use App\Models\Audio\Song;
use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Performers\Album;
use App\Models\Performers\Compilation;
use App\Models\Performers\Label;
use App\Models\Performers\Performer;
use App\Services\Cache\CacheService;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;

class SyncPerformersAlbums extends CommandAbstract
{
    protected $signature = 'performers:albums:sync {--reset}';

    protected $description = 'Sync performers/albums/labels from songs.
                             {--reset : Whether the performers, albums and labels table should be truncated before syncing.}';

    protected array $labels;

    protected array $performers;

    protected array $albums;

    protected Collection $songs;

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->lock();
        $reset = $this->option('reset');
        if ($reset) {
            Label::query()->delete();
            Album::query()->delete();
            Performer::query()->delete();
            $this->resetPerformersAndSongsElasticsearchIndex();
        }
        $this->labels = Label::get(['id', 'name'])->map(function (Label $label) {
            $label->comparison_name = $this->formatForComparison($label->name);

            return $label;
        })->toArray();
        $this->performers = Performer::get(['id', 'name'])->map(function (Performer $performer) {
            $performer->comparison_name = $this->formatForComparison($performer->name);

            return $performer;
        })->toArray();
        $this->albums = Album::get(['id', 'name', 'performer_id', 'compilation_id'])->map(function (Album $album) {
            $album->comparison_name = $this->formatForComparison($album->name);

            return $album->withoutRelations();
        })->toArray();
        $this->songs = Song::get(['id', 'album_id', 'label_id', 'performer', 'album', 'publisher', '_compilation_id', '_proprietaire'])->map(function (Song $song) {
            $song->performer_comparison_name = $this->formatForComparison($song->performer);
            $song->album_comparison_name = $this->formatForComparison($song->album);
            $song->publisher_comparison_name = $this->formatForComparison($song->publisher);

            return $song->setHidden(['created_at'])->withoutRelations();
        });
        $this->syncPerformersAlbumsAndLabelsFromSongs();
        $this->syncAlbumsMusicStyleLabelsAndPublishedAt();
        $this->clearSonoresAssociationAlbums();
        $this->clearRemovedLabels();
        $this->clearRemovedAlbums();
        $this->clearRemovedPerformers();
        $this->clearRemovedCompilations();
        $this->syncAlbumsMusicCompilations();
        $this->resetPerformersAndSongsElasticsearchIndex();
        app(CacheService::class)->clearBrowseSongsCache();
        $this->unlock();

        return Command::SUCCESS;
    }

    protected function formatForComparison(?string $value): ?string
    {
        return $value ? Str::of($value)->lower()->ascii()->trim() : null;
    }

    protected function syncPerformersAlbumsAndLabelsFromSongs(): void
    {
        $this->log('Syncing performers, albums and labels from songs...', 'title');
        $this->output->progressStart($this->songs->count());
        /** @var \App\Models\Audio\Song $song */
        foreach ($this->songs->lazy() as $song) {
            $label = $this->firstOrCreateLabel($song->publisher, $song->publisher_comparison_name);
            if ($label) {
                if ($song->label_id !== $label['id']) {
                    $this->updateSongLabel($song->id, $label['id']);
                }
            } elseif ($song->label_id !== null) {
                $this->updateSongLabel($song->id, null);
            }
            $performer = $this->firstOrCreatePerformer($song->performer, $song->performer_comparison_name);
            if (! $performer) {
                continue;
            }
            $album = $this->firstOrCreateAlbum(
                $song->album,
                $song->album_comparison_name,
                $performer['id'],
                $song->isMusicArchivage ? $song->_proprietaire : null
            );
            if (! $album) {
                continue;
            }
            if ($song->album_id !== $album['id']) {
                $this->updateSongAlbum($song->id, $album['id']);
            }
            $this->output->progressAdvance();
        }
        $this->output->progressFinish();
        $this->log('Synced performers and albums from songs.', 'success');
    }

    protected function firstOrCreatePerformer(
        string $songPerformerName,
        ?string $songPerformerComparisonName
    ): array {
        if (! $songPerformerComparisonName) {
            return [];
        }
        $performer = Arr::first(Arr::where(
            $this->performers,
            static fn (array $performer) => $performer['comparison_name'] === $songPerformerComparisonName
        ));
        if (! $performer) {
            $performer = $this->createPerformer($songPerformerName, $songPerformerComparisonName);
        }

        return $performer;
    }

    protected function createPerformer(string $performerName, string $songPerformerComparisonName): array
    {
        $performer = Performer::create(['name' => $performerName])->only('id', 'name');
        $performer['comparison_name'] = $songPerformerComparisonName;
        $this->performers[] = $performer;

        return $performer;
    }

    protected function updateSongAlbum(int $songId, int $albumId)
    {
        Song::where('id', $songId)->update(['album_id' => $albumId]);
    }

    protected function updateSongLabel(int $songId, ?int $labelId)
    {
        Song::where('id', $songId)->update(['label_id' => $labelId]);
    }

    protected function firstOrCreateAlbum(
        string $songAlbumName,
        ?string $songAlbumComparisonName,
        int $performerId,
        ?string $songAlbumOwner
    ): array {
        if (! $songAlbumComparisonName) {
            return [];
        }
        $album = Arr::first(Arr::where(
            $this->albums,
            static fn (array $album) => $album['comparison_name'] === $songAlbumComparisonName
                && (int) $album['performer_id'] === $performerId
        ));
        if (! $album) {
            $album = $this->createAlbum($songAlbumName, $songAlbumComparisonName, $performerId, $songAlbumOwner);
        }

        return $album;
    }

    protected function createAlbum(
        string $albumName,
        string $albumComparisonName,
        int $performerId,
        ?string $albumOwner
    ): array {
        /** @var \App\Models\Performers\Album $album */
        $album = Album::create(['name' => $albumName, 'performer_id' => $performerId]);

        if ($albumOwner) {
            $owner = $this->decodeOwnerString($albumOwner);
            $place = Place::find($owner['id']);
            if ($place) {
                $contentLocation = new ContentLocation();
                $contentLocation->content()->associate($album);
                $contentLocation->location()->associate($place);
                $contentLocation->save();
            }
        }

        $albumArray = $album->only('id', 'performer_id', 'name');
        $albumArray['comparison_name'] = $albumComparisonName;
        $this->albums[] = $albumArray;

        return $albumArray;
    }

    protected function firstOrCreateLabel(
        ?string $songLabelName,
        ?string $songLabelComparisonName
    ): array {
        if (! $songLabelName || ! $songLabelComparisonName || $songLabelName == '' || $songLabelComparisonName == '') {
            return [];
        }
        $label = Arr::first(Arr::where(
            $this->labels,
            static fn (array $label) => $label['comparison_name'] === $songLabelComparisonName
        ));
        if (! $label) {
            $label = $this->createLabel($songLabelName, $songLabelComparisonName);
        }

        return $label;
    }

    protected function createLabel(string $labelName, string $songLabelComparisonName): array
    {
        $label = Label::create(['name' => $labelName])->only('id', 'name');
        $label['comparison_name'] = $songLabelComparisonName;
        $this->labels[] = $label;

        return $label;
    }

    protected function syncAlbumsMusicStyleLabelsAndPublishedAt(): void
    {
        $this->log('Syncing albums music style, labels and published_at date...', 'title');
        $this->output->progressStart(Album::count());
        foreach (Album::with(['songs'])->whereHas('songs')->lazy() as $album) {
            $musicStyles = $album->songs->sortDesc()->countBy('genre')->sortDesc();
            $mostRepresentativeMusicStyle = $musicStyles->take(1)->keys()->first();

            $labels = $album->songs->whereNotNull('label_id')->sortDesc()->countBy('label_id')->sortDesc();
            $mostRepresentativeLabelId = $labels->take(1)->keys()->first() ?: null;

            $publishedAtDate = $album->songs->pluck('release_date', 'year')
                ->map(function (?string $releaseDate, ?string $year) {
                    // Essayer d'abord release_date
                    if ($releaseDate) {
                        return rescue(
                            fn () => Date::createFromFormat('Y-m-d', $releaseDate)->startOfDay(),
                            report: false
                        );
                    }
                    // Si pas de release_date, utiliser year
                    if ($year) {
                        return rescue(
                            fn () => Date::createFromFormat('Y-m-d', $year)->startOfDay(),
                            report: false
                        ) ?: rescue(
                            fn () => Date::createFromFormat('Y', $year)->startOfYear(),
                            report: false
                        );
                    }

                    return null;
                })
                ->filter()
                ->filter(fn ($date) => $date > Date::create(1900, 1, 1))
                ->max();

            $album->update([
                'genre' => $mostRepresentativeMusicStyle,
                'published_at' => $publishedAtDate ?: $album->published_at,
                'label_id' => $mostRepresentativeLabelId,
            ]);

            $this->output->progressAdvance();
        }
        $this->output->progressFinish();
        $this->log('Synced albums music style, labels and published_at date.', 'success');
    }

    protected function syncAlbumsMusicCompilations(): void
    {
        $this->log('Syncing albums music compilations...', 'title');
        $this->output->progressStart(Compilation::count());
        foreach (Compilation::all() as $compilation) {
            $albumsForCompilation = Song::where('_compilation_id', '=', $compilation->id)->distinct('album_id')->pluck('album_id')->toArray();
            $previousAlbumsInCompilation = Album::where('compilation_id', '=', $compilation->id)->distinct('id')->pluck('id')->toArray();

            $albumsToAddCompilation = array_values(array_diff($albumsForCompilation, $previousAlbumsInCompilation));
            $albumsToRemoveCompilation = array_values(array_diff($previousAlbumsInCompilation, $albumsForCompilation));

            if (count($albumsToRemoveCompilation) > 0) {
                Album::whereIn('id', $albumsToRemoveCompilation)->update(['compilation_id' => null]);
            }
            if (count($albumsToAddCompilation) > 0) {
                Album::whereIn('id', $albumsToAddCompilation)->update(['compilation_id' => $compilation->id]);
            }
            $this->output->progressAdvance();
        }

        $this->output->progressFinish();
        $this->log('Synced albums music compilations.', 'success');
    }

    protected function clearRemovedLabels(): void
    {
        $this->log('Clearing previously extracted labels not found in songs...', 'title');
        $labelsToDelete = Label::orWhereDoesntHave('songs')->get();
        $labelsToDelete->each(static fn (Label $label) => $label->delete());
        $this->log(
            $labelsToDelete->count() . ' previously extracted labels not found in songs have been removed.',
            'success'
        );
    }

    protected function clearRemovedAlbums(): void
    {
        $this->log('Clearing previously extracted albums not found in songs...', 'title');
        $albumsToDelete = Album::orWhereDoesntHave('songs')->get();
        $albumsToDelete->each(static fn (Album $album) => $album->delete());
        $this->log(
            $albumsToDelete->count() . ' previously extracted albums not found in songs have been removed.',
            'success'
        );
    }

    protected function clearRemovedPerformers(): void
    {
        $this->log('Clearing previously extracted performers not found in songs...', 'title');
        $performersToDelete = Performer::whereDoesntHave('albums')->get();
        $performersToDelete->each(static fn (Performer $performer) => $performer->delete());
        $this->log(
            $performersToDelete->count() . ' previously extracted performers not found in songs have been removed.',
            'success'
        );
    }

    protected function clearSonoresAssociationAlbums(): void
    {
        $this->log('Clearing previously associated sonores songs with album...', 'title');
        $songsToClearAlbum = Song::sonoreType()->whereNotNull('album_id')->get();
        $songsToClearAlbum->each(static fn (Song $song) => $song->update(['album_id' => null]));
        $this->log(
            $songsToClearAlbum->count() . ' previously associated sonores songs with album have been removed.',
            'success'
        );
    }

    protected function clearRemovedCompilations(): void
    {
        $this->log('Clearing removed compilations in songs...', 'title');
        $compilations = Compilation::pluck('id');
        Song::whereNotIn('_compilation_id', $compilations)
            ->whereNotNull('_compilation_id')
            ->where('_compilation_id', '!=', '')
            ->update(['_compilation_id' => null]);
        $this->log('Clear removed compilations in songs.', 'success');
    }

    protected function resetPerformersAndSongsElasticsearchIndex(): void
    {
        $this->call(ResetPerformersIndex::class);
        $this->call(ResetSongsIndex::class);
    }

    protected function decodeOwnerString(string $owner): array
    {
        $pattern = '/^\[(\d+)\]\s*(.*)$/';
        if (preg_match($pattern, $owner, $matches)) {
            return [
                'id' => (int) $matches[1],
                'label' => trim($matches[2]),
            ];
        } else {
            return [
                'id' => null,
                'label' => null,
            ];
        }
    }
}

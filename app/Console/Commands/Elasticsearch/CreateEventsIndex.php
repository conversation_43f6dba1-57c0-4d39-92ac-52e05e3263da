<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;

class CreateEventsIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:create:events {--force-recreate}';

    /** @var string */
    protected $description = 'Create index Events on the Elasticsearch.
                             {--force-recreate : Whether the potential pre-existing index should be reset.}';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $forceRecreate = $this->option('force-recreate');
        $this->log(($forceRecreate ? 'Recreating' : 'Creating') .
            ' Elasticsearch events index...', 'title');
        $indexName = config('services.elasticsearch.indexes.events');
        if (! $indexName) {
            $this->log('ELASTICSEARCH_INDEX_EVENTS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        if ($forceRecreate) {
            app(ElasticsearchService::class)->deleteIndex($indexName);
            $this->log('Elasticsearch events index deleted.', 'comment');
        }
        $this->createIndex($indexName);
        $this->log('Elasticsearch events index ' . ($forceRecreate ? 'recreated' : 'created') . '.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }

    protected function createIndex(string $indexName): void
    {
        if (app(ElasticsearchService::class)->indexExists($indexName)) {
            $this->log('Aborting as the ES index ' . $indexName . ' already exists.', 'warning');

            return;
        }
        app(ElasticsearchService::class)->createIndex($indexName, [
            'id' => [
                'type' => 'integer',
                'index' => true,
            ],
            'audio_source_class' => [
                'type' => 'text',
                'index' => false,
            ],
            'title' => [
                'type' => 'text',
            ],
            'description' => [
                'type' => 'text',
                'analyzer' => 'french',
            ],
            'tags' => [
                'type' => 'text',
                'analyzer' => 'tags_analyzer',
            ],
            'cover_thumb' => [
                'type' => 'text',
                'index' => false,
            ],
            'location_id' => [
                'type' => 'keyword',
                'index' => false,
            ],
            'location_type' => [
                'type' => 'keyword',
                'index' => false,
            ],
            'location_name' => [
                'type' => 'text',
                'index' => false,
            ],
            'location_latitude' => [
                'type' => 'float',
                'index' => false,
            ],
            'location_longitude' => [
                'type' => 'float',
                'index' => false,
            ],
            'active' => [
                'type' => 'boolean',
                'index' => false,
            ],
            'started_at' => [
                'type' => 'date',
                'format' => 'yyyy-MM-dd HH:mm:ss',
            ],
            'ended_at' => [
                'type' => 'date',
                'format' => 'yyyy-MM-dd HH:mm:ss',
            ],
            'thematic_id' => [
                'type' => 'integer',
                'index' => false,
            ],
            'thematic_title' => [
                'type' => 'text',
                'index' => false,
            ],
            'radio_stations' => [
                'type' => 'object',
            ],
            'authors' => [
                'type' => 'object',
            ],
            'has_audio' => [
                'type' => 'boolean',
            ],
        ]);
    }
}

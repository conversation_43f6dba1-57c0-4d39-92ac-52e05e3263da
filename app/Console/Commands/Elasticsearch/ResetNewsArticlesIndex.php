<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Models\News\NewsArticle;
use App\Services\Elasticsearch\ElasticsearchService;
use App\Services\Elasticsearch\NewsArticlesIndexService;
use Illuminate\Console\Command;

class ResetNewsArticlesIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:reset:news-articles';

    /** @var string */
    protected $description = 'Reset Elasticsearch news index from local database.';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Resetting Elasticsearch news articles index...', 'title');
        if (! config('services.elasticsearch.indexes.news_articles')) {
            $this->log('ELASTICSEARCH_INDEX_NEWS_ARTICLES is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        $this->call(CreateNewsArticlesIndex::class, ['--force-recreate' => true]);
        app(ElasticsearchService::class)->clearIndex(config('services.elasticsearch.indexes.news_articles'));
        $this->output->progressStart(NewsArticle::count());
        NewsArticle::with(['contentLocations', 'media', 'radioStations', 'thematic'])->get()->each(function (NewsArticle $newsArticle) {
            app(NewsArticlesIndexService::class)->updateOrCreate($newsArticle);
            $this->output->progressAdvance();
        });
        $this->output->progressFinish();
        $this->log('Elasticsearch news articles index reset.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }
}

<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;

class CreateNewsArticlesIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:create:news-articles {--force-recreate}';

    /** @var string */
    protected $description = 'Create index NewsArticles on the Elasticsearch.
                             {--force-recreate : Whether the potential pre-existing index should be reset.}';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $forceRecreate = $this->option('force-recreate');
        $this->log(($forceRecreate ? 'Recreating' : 'Creating') .
            ' Elasticsearch news articles index...', 'title');
        $indexName = config('services.elasticsearch.indexes.news_articles');
        if (! $indexName) {
            $this->log('ELASTICSEARCH_INDEX_NEWS_ARTICLES is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        if ($forceRecreate) {
            app(ElasticsearchService::class)->deleteIndex($indexName);
            $this->log('Elasticsearch news index deleted.', 'comment');
        }
        $this->createIndex($indexName);
        $this->log('Elasticsearch news articles index ' . ($forceRecreate ? 'recreated' : 'created') . '.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }

    protected function createIndex(string $indexName): void
    {
        if (app(ElasticsearchService::class)->indexExists($indexName)) {
            $this->log('Aborting as the ES index ' . $indexName . ' already exists.', 'warning');

            return;
        }
        app(ElasticsearchService::class)->createIndex($indexName, [
            'id' => [
                'type' => 'integer',
                'index' => true,
            ],
            'audio_source_class' => [
                'type' => 'text',
                'index' => false,
            ],
            'title' => [
                'type' => 'text',
            ],
            'slug' => [
                'type' => 'text',
                'index' => false,
            ],
            'description' => [
                'type' => 'text',
                'analyzer' => 'french',
            ],
            'tags' => [
                'type' => 'text',
                'analyzer' => 'tags_analyzer',
            ],
            'published_at' => [
                'type' => 'date',
            ],
            'cover_card' => [
                'type' => 'text',
                'index' => false,
            ],
            'thematic_id' => [
                'type' => 'integer',
                'index' => false,
            ],
            'thematic_title' => [
                'type' => 'text',
                'index' => false,
            ],
            'radio_stations' => [
                'type' => 'object',
            ],
            'authors' => [
                'type' => 'object',
            ],
            'location_id' => [
                'type' => 'keyword',
                'index' => false,
            ],
            'location_type' => [
                'type' => 'keyword',
                'index' => false,
            ],
            'has_audio' => [
                'type' => 'boolean',
            ],
            'active' => [
                'type' => 'boolean',
                'index' => false,
            ],
        ]);
    }
}

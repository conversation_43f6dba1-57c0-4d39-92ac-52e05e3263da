<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Models\Audio\Song;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;
use Illuminate\Support\Arr;

class RefreshSongsIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:refresh:songs';

    /** @var string */
    protected $description = 'Refresh Elasticsearch songs index from WinMedia database.';

    /** @throws \Exception */
    public function handle(): int
    {
        $this->log('Refreshing Elasticsearch songs index...', 'title');
        if (! config('services.elasticsearch.indexes.songs')) {
            $this->log('ELASTICSEARCH_INDEX_SONGS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        $songsRequiringAction = Song::with(['performerRelationship', 'albumRelationship', 'labelRelationship'])
            ->allTypes()
            ->where('elasticsearch_action', '!=', Song::ES_ACTION_UP_TO_DATE)
            ->where('elasticsearch_action', '!=', Song::ES_ACTION_NOT_INDEXED)
            ->orderBy('id', 'DESC')
            ->limit(500)
            ->get();
        $songsHasChanged = $songsRequiringAction->count() > 0;
        $this->output->progressStart($songsRequiringAction->count());
        $songsRequiringAction->each(function (Song $song) {
            try {
                $esSong = $this->findElasticsearchSong($song);
                if ($song->elasticsearch_action === Song::ES_ACTION_TO_DELETE) {
                    if ($esSong) {
                        $this->deleteElasticsearchSong($esSong['_id']);
                    }
                    $song->delete();
                } elseif ($song->category_plateforme === Song::CATEGORY_PLATEFORME_SONORES) {
                    $esSong
                        ? $this->updateElasticsearchSong($song, $esSong['_id'], true)
                        : $this->createElasticsearchSong($song, true);
                    $song->update(['elasticsearch_action' => Song::ES_ACTION_NOT_INDEXED]);
                } elseif ($song->elasticsearch_action === Song::ES_ACTION_TO_SYNC || ! $esSong) {
                    $esSong
                        ? $this->updateElasticsearchSong($song, $esSong['_id'], false)
                        : $this->createElasticsearchSong($song, false);
                    $song->update(['elasticsearch_action' => Song::ES_ACTION_UP_TO_DATE]);
                }
            } catch (\Exception $exception) {
                app('sentry')->captureException($exception);
                $this->log('Error on song ' . $song->id . ' : ' . $exception->getMessage(), 'error');
            } finally {
                $this->output->progressAdvance();
            }
        });
        if ($songsHasChanged) {
            app(CacheService::class)->clearBrowseSongsCache();
        }
        $this->output->progressFinish();
        $this->log($songsRequiringAction->count() . ' Elasticsearch songs index refreshed.', 'success');
        $this->deleteEsSongsNotFoundInWinMediaDatabase();
        $this->unlock();

        return Command::SUCCESS;
    }

    protected function findElasticsearchSong(Song $song): ?array
    {
        $esResults = app(ElasticsearchService::class)->findByField(
            config('services.elasticsearch.indexes.songs'),
            'id',
            $song->id
        );

        return Arr::first($esResults['hits']['hits']);
    }

    protected function updateElasticsearchSong(Song $song, string $esSongId, bool $hidden = true): void
    {
        app(ElasticsearchService::class)->updateDocument(
            config('services.elasticsearch.indexes.songs'),
            $esSongId,
            [
                'id' => $song->id,
                'imedia' => $song->imedia,
                'audio_source_class' => $song::class,
                'cover_thumb' => $song->cover_thumb,
                'title' => $song->title,
                'performer_id' => $song->performerRelationship?->id,
                'performer_name' => $song->performer,
                'version' => $song->version,
                'album_id' => $song->albumRelationship?->id,
                'album_name' => $song->album !== '0' ? $song->album : '',
                'year' => $song->yearFourDigit,
                'genre' => $song->genre !== '0' ? $song->genre : '',
                'place_label_id' => $song->labelRelationship?->place()?->id,
                'label_name' => $song->labelRelationship?->name ?: $song->publisher,
                'comment' => $song->comment,
                'human_readable_duration' => $song->human_readable_duration,
                'created_at' => $song->created_at,
                'hidden' => $hidden,
            ]
        );
    }

    protected function createElasticsearchSong(Song $song, bool $hidden = true): void
    {
        app(ElasticsearchService::class)->indexDocument(config('services.elasticsearch.indexes.songs'), [
            'id' => $song->id,
            'imedia' => $song->imedia,
            'audio_source_class' => $song::class,
            'cover_thumb' => $song->cover_thumb,
            'title' => $song->title,
            'performer_id' => $song->performerRelationship?->id,
            'performer_name' => $song->performer,
            'version' => $song->version,
            'album_id' => $song->albumRelationship?->id,
            'album_name' => $song->album !== '0' ? $song->album : '',
            'year' => $song->yearFourDigit,
            'genre' => $song->genre !== '0' ? $song->genre : '',
            'place_label_id' => $song->labelRelationship?->place()?->id,
            'label_name' => $song->labelRelationship?->name ?: $song->publisher,
            'comment' => $song->comment,
            'human_readable_duration' => $song->human_readable_duration,
            'created_at' => $song->created_at,
            'hidden' => $hidden,
        ]);
    }

    protected function deleteElasticsearchSong(string $esSongId): void
    {
        app(ElasticsearchService::class)->deleteDocument(
            config('services.elasticsearch.indexes.songs'),
            $esSongId
        );
    }

    protected function deleteEsSongsNotFoundInWinMediaDatabase(): void
    {
        $esSongIndex = config('services.elasticsearch.indexes.songs');
        $esSongsShouldBeDeleted = app(ElasticsearchService::class)->countIndex($esSongIndex) !== Song::allTypes()->count();
        if ($esSongsShouldBeDeleted) {
            $localSongIds = Song::allTypes()->pluck('id');
            $esSongs = app(ElasticsearchService::class)->all($esSongIndex, 1000, ['id']);
            $esSongsToDelete = $esSongs->pluck('fields.id.0')->diff($localSongIds);
            $esSongsToDeleteCount = $esSongsToDelete->count();
            $esSongsToDelete->each(function (int $winMediaSongId) use ($esSongIndex, $esSongs) {
                $esSong = $esSongs->where('fields.id.0', $winMediaSongId)->sole();
                try {
                    app(ElasticsearchService::class)->deleteDocument($esSongIndex, $esSong['_id']);
                } catch (\Exception) {
                    $this->log(
                        'Song ' . $winMediaSongId . ' / ' . $esSong['_id'] . ' does not exist on ES index '
                        . $esSongIndex . ' and could not been deleted.',
                        'error'
                    );
                }
            });
            $this->log(
                $esSongsToDeleteCount . ' Elasticsearch songs not found in Winmedia have been deleted.',
                'success'
            );
        }
    }
}

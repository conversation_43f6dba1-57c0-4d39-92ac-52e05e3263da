<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Models\Audio\Playlist;
use App\Models\Audio\Song;
use App\Models\Radio\RadioStation;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ResetPlaylistsIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:reset:playlists';

    /** @var string */
    protected $description = 'Reset Elasticsearch playlists index from Winmedia database.';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Resetting Elasticsearch playlists index...', 'title');
        if (! config('services.elasticsearch.indexes.playlists')) {
            $this->log('ELASTICSEARCH_INDEX_PLAYLISTS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        $this->call(CreatePlaylistsIndex::class, ['--force-recreate' => true]);
        app(ElasticsearchService::class)->clearIndex(config('services.elasticsearch.indexes.playlists'));
        $this->output->progressStart(Playlist::count());
        Playlist::with(['contentLocations', 'media', 'radioStations', 'songs', 'thematic'])
            ->orderBy('id')
            ->chunk(1000, function (Collection $chunk) {
                $data = $chunk->reduce(function (array $data, Playlist $playlist) {
                    $location = $playlist->location();

                    $data[] = [
                        'id' => $playlist->id,
                        'audio_source_class' => $playlist::class,
                        'cover_thumb' => $playlist->getFirstMediaUrl('cover', 'medium'),
                        'title' => $playlist->title,
                        'tags' => $playlist->tags,
                        'user_id' => $playlist->user_id,
                        'thematic_id' => $playlist->thematic_id,
                        'thematic_title' => $playlist->thematic?->title,
                        'radio_stations' => $playlist->radioStations->map(
                            static fn (RadioStation $radio) => [
                                'id' => $radio->id,
                                'name' => $radio->name,
                            ]
                        ),
                        'songs_performers' => $playlist->songs->pluck('performer')->implode(', '),
                        'songs_titles' => $playlist->songs->pluck('title')->implode(', '),
                        'songs_cover_urls' => $playlist->songs->take(4)->map(static fn (Song $song) => $song->cover_thumb),
                        'songs_count' => $playlist->songs->count(),
                        'location_id' => $location?->id,
                        'location_type' => $location ? $location::class : null,
                        'published_at' => $playlist->published_at?->toDateTimeString(),
                        'unpublished_at' => $playlist->unpublished_at?->toDateTimeString(),
                        'active' => $playlist->active,
                    ];

                    return $data;
                }, []);

                app(ElasticsearchService::class)->bulkIndex(config('services.elasticsearch.indexes.playlists'), $data);
                $this->output->progressAdvance(count($data));
            });

        $this->output->progressFinish();
        $this->log('Elasticsearch playlists index reset.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }
}

<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;

class CreatePerformersIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:create:performers {--force-recreate}';

    /** @var string */
    protected $description = 'Create index Performers on the Elasticsearch.
                             {--force-recreate : Whether the potential pre-existing index should be reset.}';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $forceRecreate = $this->option('force-recreate');
        $this->log(($forceRecreate ? 'Recreating' : 'Creating') .
            ' Elasticsearch performers index...', 'title');
        $indexName = config('services.elasticsearch.indexes.performers');
        if (! $indexName) {
            $this->log('ELASTICSEARCH_INDEX_PERFORMERS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        if ($forceRecreate) {
            app(ElasticsearchService::class)->deleteIndex($indexName);
            $this->log('Elasticsearch performers index deleted.', 'comment');
        }
        $this->createIndex($indexName);
        $this->log('Elasticsearch performers index ' . ($forceRecreate ? 'recreated' : 'created') . '.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }

    protected function createIndex(string $indexName): void
    {
        if (app(ElasticsearchService::class)->indexExists($indexName)) {
            $this->log('Aborting as the ES index ' . $indexName . ' already exists.', 'warning');

            return;
        }
        app(ElasticsearchService::class)->createIndex($indexName, [
            'id' => [
                'type' => 'text',
                'index' => true,
            ],
            'thumb' => [
                'type' => 'text',
                'index' => false,
            ],
            'name' => [
                'type' => 'text',
                'fields' => [
                    'keyword' => [
                        'type' => 'keyword',
                    ],
                ],
            ],
            'music_styles' => [
                'type' => 'text',
                'analyzer' => 'tags_analyzer',
            ],
            'updated_at' => [
                'type' => 'date',
                'format' => 'yyyy-MM-dd HH:mm:ss',
            ],
            'created_at' => [
                'type' => 'date',
                'format' => 'yyyy-MM-dd HH:mm:ss',
            ],
            'albums' => [
                'type' => 'nested',
                'properties' => [
                    'id' => [
                        'type' => 'keyword',
                    ],
                    'name' => [
                        'type' => 'text',
                    ],
                    'location_id' => [
                        'type' => 'keyword',
                    ],
                    'location_type' => [
                        'type' => 'keyword',
                    ],
                ],
            ],
            'labels' => [
                'type' => 'nested',
                'properties' => [
                    'id' => [
                        'type' => 'keyword',
                    ],
                    'name' => [
                        'type' => 'text',
                    ],
                    'location_id' => [
                        'type' => 'keyword',
                    ],
                    'location_type' => [
                        'type' => 'keyword',
                    ],
                ],
            ],
        ]);
    }
}

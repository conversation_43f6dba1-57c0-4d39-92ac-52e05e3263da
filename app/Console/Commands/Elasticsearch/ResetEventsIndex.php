<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Models\Events\Event;
use App\Services\Elasticsearch\ElasticsearchService;
use App\Services\Elasticsearch\EventIndexService;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ResetEventsIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:reset:events';

    /** @var string */
    protected $description = 'Reset Elasticsearch events index from local database.';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Resetting Elasticsearch events index...', 'title');
        if (! config('services.elasticsearch.indexes.events')) {
            $this->log('ELASTICSEARCH_INDEX_EVENTS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        $this->call(CreateEventsIndex::class, ['--force-recreate' => true]);
        app(ElasticsearchService::class)->clearIndex(config('services.elasticsearch.indexes.events'));
        $this->output->progressStart(Event::count());
        Event::with(['contentLocations', 'media', 'radioStations', 'thematic'])
            ->where('active', true)
            ->orderBy('id')
            ->chunk(1000, function (Collection $chunk) {
                $chunk->each(static fn (Event $event) => app(EventIndexService::class)->updateOrCreate($event));
                $this->output->progressAdvance($chunk->count());
            });
        $this->output->progressFinish();
        $this->log('Elasticsearch events index reset.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }
}

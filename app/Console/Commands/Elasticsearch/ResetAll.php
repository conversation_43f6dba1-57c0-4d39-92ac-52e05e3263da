<?php

namespace App\Console\Commands\Elasticsearch;

use Illuminate\Console\Command;

class ResetAll extends Command
{
    protected $signature = 'es:index:reset:all';

    protected $description = 'Reset all Elasticsearch index';

    public function handle(): void
    {
        $this->call(ResetSongsIndex::class);
        $this->call(ResetPodcastsIndex::class);
        $this->call(ResetPlaylistsIndex::class);
        $this->call(ResetNewsArticlesIndex::class);
        $this->call(ResetEventsIndex::class);
        $this->call(ResetPerformersIndex::class);
    }
}

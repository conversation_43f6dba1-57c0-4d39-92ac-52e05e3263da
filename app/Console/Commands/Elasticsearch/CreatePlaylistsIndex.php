<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;

class CreatePlaylistsIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:create:playlists {--force-recreate}';

    /** @var string */
    protected $description = 'Create index Playlists on the Elasticsearch.
                             {--force-recreate : Whether the potential pre-existing index should be reset.}';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $forceRecreate = $this->option('force-recreate');
        $this->log(($forceRecreate ? 'Recreating' : 'Creating') .
            ' Elasticsearch playlists index...', 'title');
        $indexName = config('services.elasticsearch.indexes.playlists');
        if (! $indexName) {
            $this->log('ELASTICSEARCH_INDEX_PLAYLISTS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        if ($forceRecreate) {
            app(ElasticsearchService::class)->deleteIndex($indexName);
            $this->log('Elasticsearch playlists index deleted.', 'comment');
        }
        $this->createIndex($indexName);
        $this->log('Elasticsearch playlists index ' . ($forceRecreate ? 'recreated' : 'created') . '.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }

    protected function createIndex(string $indexName): void
    {
        if (app(ElasticsearchService::class)->indexExists($indexName)) {
            $this->log('Aborting as the ES index ' . $indexName . ' already exists.', 'warning');

            return;
        }
        app(ElasticsearchService::class)->createIndex($indexName, [
            'id' => [
                'type' => 'integer',
                'index' => true,
            ],
            'audio_source_class' => [
                'type' => 'text',
                'index' => false,
            ],
            'cover_thumb' => [
                'type' => 'text',
                'index' => false,
            ],
            'title' => [
                'type' => 'text',
            ],
            'tags' => [
                'type' => 'text',
                'analyzer' => 'tags_analyzer',
            ],
            'user_id' => [
                'type' => 'integer',
            ],
            'thematic_id' => [
                'type' => 'integer',
                'index' => false,
            ],
            'thematic_title' => [
                'type' => 'text',
                'index' => false,
            ],
            'radio_stations' => [
                'type' => 'object',
            ],
            'songs_performers' => [
                'type' => 'text',
            ],
            'songs_titles' => [
                'type' => 'text',
            ],
            'songs_cover_urls' => [
                'type' => 'text',
                'index' => false,
            ],
            'songs_count' => [
                'type' => 'integer',
                'index' => false,
            ],
            'location_id' => [
                'type' => 'keyword',
                'index' => false,
            ],
            'location_type' => [
                'type' => 'keyword',
                'index' => false,
            ],
            'published_at' => [
                'type' => 'date',
                'format' => 'yyyy-MM-dd HH:mm:ss',
            ],
            'unpublished_at' => [
                'type' => 'date',
                'format' => 'yyyy-MM-dd HH:mm:ss',
            ],
            'active' => [
                'type' => 'boolean',
            ],
        ]);
    }
}

<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Models\Performers\Performer;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ResetPerformersIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:reset:performers';

    /** @var string */
    protected $description = 'Reset Elasticsearch performers index from Winmedia database.';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Resetting Elasticsearch performers index...', 'title');
        if (! config('services.elasticsearch.indexes.performers')) {
            $this->log('ELASTICSEARCH_INDEX_PERFORMERS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        $this->call(CreatePerformersIndex::class, ['--force-recreate' => true]);
        app(ElasticsearchService::class)->clearIndex(config('services.elasticsearch.indexes.performers'));
        $this->output->progressStart(Performer::count());
        Performer::with(['albums.contentLocations.location', 'labels.contentLocations.location', 'songs.albumRelationship'])->chunk(1000, function (Collection $chunk) {
            $data = $chunk->reduce(function (array $data, Performer $performer) {
                $albums = [];
                foreach ($performer->albums as $album) {
                    $location = $album->location();

                    $albums[] = [
                        'id' => $album->id,
                        'name' => $album->name,
                        'location_type' => $location ? $location::class : null,
                        'location_id' => $location?->id,
                        'updated_at' => $album->updated_at?->toDateTimeString(),
                        'created_at' => $album->created_at?->toDateTimeString(),
                        'published_at' => $album->published_at?->toDateTimeString(),
                    ];
                }

                $labels = [];
                foreach ($performer->labels as $label) {
                    $location = $label->location();

                    $labels[] = [
                        'id' => $label->id,
                        'name' => $label->name,
                        'location_type' => $location ? $location::class : null,
                        'location_id' => $location?->id,
                        'updated_at' => $label->updated_at?->toDateTimeString(),
                        'created_at' => $label->created_at?->toDateTimeString(),
                    ];
                }

                $data[] = [
                    'id' => $performer->id,
                    'thumb' => $performer->cover_thumb_or_album,
                    'name' => $performer->name,
                    'music_styles' => $performer->music_styles,
                    'albums' => $albums,
                    'labels' => $labels,
                    'updated_at' => $performer->updated_at?->toDateTimeString(),
                    'created_at' => $performer->created_at?->toDateTimeString(),
                ];

                return $data;
            }, []);
            app(ElasticsearchService::class)->bulkIndex(config('services.elasticsearch.indexes.performers'), $data);
            $this->output->progressAdvance(count($data));
        });
        $this->output->progressFinish();
        $this->log('Elasticsearch performers index reset.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }
}

<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Models\Audio\Podcast;
use App\Models\Users\User;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ResetPodcastsIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:reset:podcasts';

    /** @var string */
    protected $description = 'Reset Elasticsearch podcasts index from Winmedia database.';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Resetting Elasticsearch podcasts index...', 'title');
        if (! config('services.elasticsearch.indexes.podcasts')) {
            $this->log('ELASTICSEARCH_INDEX_PODCASTS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        $this->call(CreatePodcastsIndex::class, ['--force-recreate' => true]);
        app(ElasticsearchService::class)->clearIndex(config('services.elasticsearch.indexes.podcasts'));
        $this->output->progressStart(Podcast::where('active', true)->count());

        Podcast::with(['contentLocations.location', 'media', 'program', 'thematic'])
            ->where('active', true)
            ->orderBy('id')
            ->chunk(1000, function (Collection $chunk) {
                $data = $chunk->reduce(function (array $data, Podcast $podcast) {
                    if ($podcast->audio_stream['mp3']) {
                        $location = $podcast->location();

                        $data[] = [
                            'id' => $podcast->id,
                            'audio_source_class' => $podcast::class,
                            'cover_thumb' => $podcast->getFirstMediaUrl('cover', 'medium'),
                            'title' => $podcast->title,
                            'tags' => $podcast->tags,
                            'description' => $podcast->description,
                            'songs_performers' => $podcast->songs()->pluck('performer')->implode(', '),
                            'songs_titles' => $podcast->songs()->pluck('title')->implode(', '),
                            'human_readable_duration' => $podcast->human_readable_duration,
                            'thematic_id' => $podcast->thematic_id,
                            'thematic_title' => $podcast->thematic?->title,
                            'program_id' => $podcast->program_id,
                            'program_title' => $podcast->program->title,
                            'program_tags' => $podcast->program->tags,
                            'location_type' => $location ? $location::class : null,
                            'location_id' => $location?->id,
                            'published_at' => $podcast->published_at,
                            'authors' => $podcast->authors()->get()
                                ->map(static fn (User $user) => ['id' => $user->id, 'username' => $user->username]),
                        ];
                    }

                    return $data;
                }, []);
                app(ElasticsearchService::class)->bulkIndex(config('services.elasticsearch.indexes.podcasts'), $data);
                $this->output->progressAdvance(count($data));
            });

        $this->output->progressFinish();
        $this->log('Elasticsearch podcasts index reset.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }
}

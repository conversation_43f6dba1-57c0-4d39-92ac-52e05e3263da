<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Models\Audio\Song;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;

class ResetSongsIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:reset:songs';

    /** @var string */
    protected $description = 'Reset Elasticsearch songs index from Winmedia database.';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Resetting Elasticsearch songs index...', 'title');
        if (! config('services.elasticsearch.indexes.songs')) {
            $this->log('ELASTICSEARCH_INDEX_SONGS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        $this->call(CreateSongsIndex::class, ['--force-recreate' => true]);
        app(ElasticsearchService::class)->clearIndex(config('services.elasticsearch.indexes.songs'));
        $this->output->progressStart(Song::allTypes()->count());
        Song::with(['performerRelationship', 'albumRelationship', 'labelRelationship'])->allTypes()->orderBy('id')->chunk(1000, function (Collection $chunk) {
            $data = $chunk->reduce(function (array $data, Song $song) {
                $data[] = [
                    'id' => $song->id,
                    'imedia' => $song->imedia,
                    'audio_source_class' => $song::class,
                    'cover_thumb' => $song->cover_thumb,
                    'title' => $song->title,
                    'performer_id' => $song->performerRelationship?->id,
                    'performer_name' => $song->performerRelationship?->name ?: $song->performer,
                    'version' => $song->version,
                    'album_id' => $song->albumRelationship?->id,
                    'album_name' => $song->album !== '0' ? $song->album : '',
                    'year' => $song->yearFourDigit,
                    'genre' => $song->genre !== '0' ? $song->genre : '',
                    'place_label_id' => $song->labelRelationship?->place()?->id,
                    'label_name' => $song->labelRelationship?->name ?: $song->publisher,
                    'comment' => $song->comment,
                    'human_readable_duration' => $song->human_readable_duration,
                    'created_at' => $song->created_at,
                    'hidden' => $song->imedia !== null && $song->category_plateforme !== Song::CATEGORY_PLATEFORME_MUSICS,
                ];

                return $data;
            }, []);
            app(ElasticsearchService::class)->bulkIndex(config('services.elasticsearch.indexes.songs'), $data);
            $this->output->progressAdvance(count($data));
        });
        Song::query()->musicType()->update(['elasticsearch_action' => Song::ES_ACTION_UP_TO_DATE]);
        Song::query()->sonoreType()->update(['elasticsearch_action' => Song::ES_ACTION_NOT_INDEXED]);
        $this->output->progressFinish();
        $this->log('Elasticsearch songs index reset.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }
}

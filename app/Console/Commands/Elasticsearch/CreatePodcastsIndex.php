<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;

class CreatePodcastsIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:create:podcasts {--force-recreate}';

    /** @var string */
    protected $description = 'Create index Podcasts on the Elasticsearch.
                             {--force-recreate : Whether the potential pre-existing index should be reset.}';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $forceRecreate = $this->option('force-recreate');
        $this->log(($forceRecreate ? 'Recreating' : 'Creating') .
            ' Elasticsearch podcasts index...', 'title');
        $indexName = config('services.elasticsearch.indexes.podcasts');
        if (! $indexName) {
            $this->log('ELASTICSEARCH_INDEX_PODCASTS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        if ($forceRecreate) {
            app(ElasticsearchService::class)->deleteIndex($indexName);
            $this->log('Elasticsearch podcasts index deleted.', 'comment');
        }
        $this->createIndex($indexName);
        $this->log('Elasticsearch podcasts index ' . ($forceRecreate ? 'recreated' : 'created') . '.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }

    protected function createIndex(string $indexName): void
    {
        if (app(ElasticsearchService::class)->indexExists($indexName)) {
            $this->log('Aborting as the ES index ' . $indexName . ' already exists.', 'warning');

            return;
        }
        app(ElasticsearchService::class)->createIndex($indexName, [
            'id' => [
                'type' => 'integer',
                'index' => true,
            ],
            'audio_source_class' => [
                'type' => 'text',
                'index' => false,
            ],
            'cover_thumb' => [
                'type' => 'text',
                'index' => false,
            ],
            'title' => [
                'type' => 'text',
            ],
            'tags' => [
                'type' => 'text',
                'analyzer' => 'tags_analyzer',
            ],
            'description' => [
                'type' => 'text',
                'analyzer' => 'french',
            ],
            'songs_performers' => [
                'type' => 'text',
            ],
            'songs_titles' => [
                'type' => 'text',
            ],
            'human_readable_duration' => [
                'type' => 'text',
                'index' => false,
            ],
            'thematic_id' => [
                'type' => 'integer',
                'index' => false,
            ],
            'thematic_title' => [
                'type' => 'text',
                'index' => false,
            ],
            'radio_stations' => [
                'type' => 'object',
            ],
            'program_id' => [
                'type' => 'integer',
                'index' => false,
            ],
            'program_title' => [
                'type' => 'text',
            ],
            'program_tags' => [
                'type' => 'text',
                'analyzer' => 'tags_analyzer',
            ],
            'location_type' => [
                'type' => 'keyword',
            ],
            'location_id' => [
                'type' => 'keyword',
            ],
            'published_at' => [
                'type' => 'date',
            ],
            'authors' => [
                'type' => 'object',
            ],
        ]);
    }
}

<?php

namespace App\Console\Commands\Elasticsearch;

use App\Console\Commands\CommandAbstract;
use App\Services\Elasticsearch\ElasticsearchService;
use Illuminate\Console\Command;

class CreateSongsIndex extends CommandAbstract
{
    /** @var string */
    protected $signature = 'es:index:create:songs {--force-recreate}';

    /** @var string */
    protected $description = 'Create index Songs on the Elasticsearch.
                             {--force-recreate : Whether the potential pre-existing index should be reset.}';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $forceRecreate = $this->option('force-recreate');
        $this->log(($forceRecreate ? 'Recreating' : 'Creating') .
            ' Elasticsearch songs index...', 'title');
        $indexName = config('services.elasticsearch.indexes.songs');
        if (! $indexName) {
            $this->log('ELASTICSEARCH_INDEX_SONGS is missing in your .env file.', 'error');

            return Command::INVALID;
        }
        $this->lock();
        if ($forceRecreate) {
            app(ElasticsearchService::class)->deleteIndex($indexName);
            $this->log('Elasticsearch songs index deleted.', 'comment');
        }
        $this->createIndex($indexName);
        $this->log('Elasticsearch songs index ' . ($forceRecreate ? 'recreated' : 'created') . '.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }

    protected function createIndex(string $indexName): void
    {
        if (app(ElasticsearchService::class)->indexExists($indexName)) {
            $this->log('Aborting as the ES index ' . $indexName . ' already exists.', 'warning');

            return;
        }
        app(ElasticsearchService::class)->createIndex($indexName, [
            'id' => [
                'type' => 'long',
                'index' => true,
            ],
            'imedia' => [
                'type' => 'long',
                'index' => true,
                'null_value' => 0,
            ],
            'audio_source_class' => [
                'type' => 'text',
                'index' => false,
            ],
            'cover_thumb' => [
                'type' => 'text',
                'index' => false,
            ],
            'title' => [
                'type' => 'text',
            ],
            'performer_id' => [
                'type' => 'integer',
                'index' => true,
            ],
            'performer_name' => [
                'type' => 'text',
                'fields' => [
                    'keyword' => [
                        'type' => 'keyword',
                    ],
                ],
            ],
            'version' => [
                'type' => 'text',
                'analyzer' => 'french',
            ],
            'album_id' => [
                'type' => 'integer',
                'index' => true,
            ],
            'album_name' => [
                'type' => 'text',
            ],
            'year' => [
                'type' => 'text',
                'index' => false,
            ],
            'genre' => [
                'type' => 'text',
            ],
            'place_label_id' => [
                'type' => 'integer',
                'index' => true,
            ],
            'label_name' => [
                'type' => 'text',
            ],
            'comment' => [
                'type' => 'text',
                'analyzer' => 'french',
            ],
            'human_readable_duration' => [
                'type' => 'text',
                'index' => false,
            ],
            'created_at' => [
                'type' => 'date',
                'index' => false,
                'format' => 'strict_date_optional_time_nanos',
            ],
            'hidden' => [
                'type' => 'boolean',
            ],
        ]);
    }
}

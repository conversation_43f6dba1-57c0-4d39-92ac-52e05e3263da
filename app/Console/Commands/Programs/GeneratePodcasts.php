<?php

namespace App\Console\Commands\Programs;

use App\Console\Commands\CommandAbstract;
use App\Models\Audio\Podcast;
use App\Models\Holidays\Holidays;
use App\Models\Map\ContentLocation;
use App\Models\Radio\Program;
use App\Models\Radio\ProgramRecurrence;
use Carbon\CarbonInterface;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Date;

class GeneratePodcasts extends CommandAbstract
{
    /** @var string */
    protected $signature = 'programs:podcasts:generate {days_to_generate?} {program_id?}';

    /** @var string */
    protected $description = 'Generate program podcasts from today to x days.
                            {days_to_generate : Number of days to generate from today (Optional: default 7 days)}
                            {program_id : ID of program to generate (Optional: default all programs)}';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $daysToGenerate = $this->argument('days_to_generate') ? intval($this->argument('days_to_generate')) : 7;
        $logText = $this->argument('program_id') ? ('program "' . $this->argument('program_id') . '"') : 'all programs';

        $this->log('Generating podcasts for ' . $logText . ' from today to day+' . $daysToGenerate . '...', 'title');
        $this->lock();
        $this->output->progressStart($daysToGenerate + 1);
        foreach (range(0, $daysToGenerate) as $day) {
            $this->generateOnDay(Date::today()->addDays($day));
            $this->output->progressAdvance();
        }
        $this->output->progressFinish();
        $this->log('Generated podcasts for ' . $logText . ' from today to day+' . $daysToGenerate . '.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }

    protected function generateOnDay(CarbonInterface $day): void
    {
        $weekOfMonth = $day->weekOfMonth;
        $dayNextWeek = $day->addDays(7);
        if ($dayNextWeek->weekOfMonth === 1) {
            $weekOfMonth = 5;
        }

        $programRecurrencesQuery = ProgramRecurrence::with(['program.media', 'subPrograms', 'radioStations'])
            ->where(fn (Builder $query) => $query
                ->whereJsonLength('months', 0)
                ->orWhereJsonContains('months', $day->month))
            ->where(fn (Builder $query) => $query
                ->whereJsonLength('month_days', 0)
                ->orWhereJsonContains('month_days', $day->day))
            ->where(fn (Builder $query) => $query
                ->whereJsonLength('week_days', 0)
                ->orWhereJsonContains('week_days', $day->dayOfWeek)
                ->orWhereJsonContains('week_days', ($day->dayOfWeek + 7)))
            ->where(fn (Builder $query) => $query
                ->whereJsonLength('month_week', 0)
                ->orWhereJsonContains('month_week', $day->weekOfMonth)
                ->orWhereJsonContains('month_week', $weekOfMonth));

        if ($this->argument('program_id')) {
            $programRecurrences = $programRecurrencesQuery->where('program_id', '=', intval($this->argument('program_id')))->get();
        } else {
            $programRecurrences = $programRecurrencesQuery->get();
        }

        /** @var \App\Models\Radio\ProgramRecurrence $programRecurrence */
        foreach ($programRecurrences as $programRecurrence) {
            if (
                ! $this->dayIsOnValidWeekOfYear($programRecurrence->broadcast_week, $day)
                || $this->dayIsPositionedOnHolidays(
                    $programRecurrence->holidays_break,
                    $programRecurrence->program_id,
                    $day
                )
            ) {
                continue;
            }
            $program = $programRecurrence->program;
            $this->generatePodcast(
                $program,
                $day->timezone($programRecurrence->local_time_tz)
                    ->hours($programRecurrence->local_time->hour)
                    ->minutes($programRecurrence->local_time->minute)
                    ->timezone('UTC'),
                $programRecurrence->radioStations->pluck('id')->toArray()
            );
            if ($programRecurrence->subPrograms->isNotEmpty()) {
                foreach ($programRecurrence->subPrograms as $subProgram) {
                    if (
                        $this->dayIsPositionedOnHolidays(
                            $programRecurrence->holidays_break,
                            $subProgram->id,
                            $day
                        )
                    ) {
                        continue;
                    }
                    $time = Date::createFromFormat('H:i:s', $subProgram->pivot->local_time);
                    $this->generatePodcast(
                        $subProgram,
                        $day->timezone($subProgram->pivot->local_time_tz)
                            ->hours($time->hour)
                            ->minutes($time->minute)
                            ->timezone('UTC'),
                        $programRecurrence->radioStations->pluck('id')->toArray()
                    );
                }
            }
        }
    }

    protected function dayIsOnValidWeekOfYear(string $broadcastWeek, CarbonInterface $day): bool
    {
        return match ($broadcastWeek) {
            '2' => $day->weekOfYear % 2 === 0,
            '3' => $day->weekOfYear % 2 !== 0,
            default => true,
        };
    }

    protected function dayIsPositionedOnHolidays(bool $holidaysBreak, int $programId, CarbonInterface $day): bool
    {
        if ($holidaysBreak) {
            // ProgramRecurrence has the setting holidaysBreak active (general setting)
            return Holidays::where('active', 1)
                ->whereDate('started_at', '<=', $day->format('Y-m-d'))
                ->whereDate('ended_at', '>=', $day->format('Y-m-d'))
                ->whereRaw('started_at < ended_at')
                ->where(fn (Builder $query) => $query
                    ->whereDoesntHave('programs')
                    ->orWhereHas('programs', fn (Builder $query) => $query
                        ->where('id', $programId)))
                ->exists();
        }

        // ProgramRecurrence hasn't the setting holidaysBreak active (only specific program holidays)
        return Holidays::where('active', 1)
            ->whereDate('started_at', '<=', $day->format('Y-m-d'))
            ->whereDate('ended_at', '>=', $day->format('Y-m-d'))
            ->whereRaw('started_at < ended_at')
            ->whereHas('programs', fn (Builder $query) => $query
                ->where('id', $programId))
            ->exists();
    }

    protected function generatePodcast(Program $program, CarbonInterface $publishedAt, array $radioStationIds): void
    {
        if (Podcast::where('published_at', $publishedAt)->where('program_id', $program->id)->doesntExist()) {
            $podcast = Podcast::create([
                'program_id' => $program->id,
                'thematic_id' => $program->thematic_id,
                'title' => $program->title,
                'tags' => $program->tags,
                'description' => $program->description,
                'type' => Podcast::TYPE_REPLAY,
                'duration' => $program->duration,
                'published_at' => $publishedAt,
                'active' => true,
            ]);
            $authorsIdsWithPivot = collect($program->authors()->pluck('id')->toArray())->mapWithKeys(fn (
                int $authorId,
                int $index
            ) => [$authorId => ['index' => $index]])->toArray();
            $podcast->authors()->sync($authorsIdsWithPivot);
            $podcast->radioStations()->sync($radioStationIds);
            if ($program->getFirstMedia('cover')) {
                $program->getFirstMedia('cover')->copy($podcast, 'cover');
            }
            $program->load('contentLocations');
            if ($programLocation = $program->location()) {
                $contentLocation = new ContentLocation();
                $contentLocation->content()->associate($podcast);
                $contentLocation->location()->associate($programLocation);
                $contentLocation->save();
            }
        }
    }
}

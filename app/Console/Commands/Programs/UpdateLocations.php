<?php

namespace App\Console\Commands\Programs;

use App\Console\Commands\CommandAbstract;
use App\Models\Map\ContentLocation;
use App\Models\Radio\Program;
use App\Services\Elasticsearch\PodcastIndexService;
use Illuminate\Console\Command;

class UpdateLocations extends CommandAbstract
{
    /** @var string */
    protected $signature = 'programs:locations:update';

    /** @var string */
    protected $description = 'Update locations defined in programs to podcasts who haven\'t any.';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Update locations defined in programs to podcasts who haven\'t any.', 'title');
        $this->lock();

        $programs = Program::whereHas('contentLocations')
            ->with(['contentLocations.location', 'podcasts' => ['contentLocations', 'thematic', 'program']])
            ->get();

        $this->output->progressStart(count($programs));

        /** @var \App\Models\Radio\Program $program */
        foreach ($programs as $program) {
            $programLocation = $program->location();
            if (! $programLocation) {
                continue;
            }

            /** @var \App\Models\Audio\Podcast $podcast */
            foreach ($program->podcasts as $podcast) {
                if ($podcast->contentLocations->isEmpty()) {
                    ContentLocation::create([
                        'content_type' => get_class($podcast),
                        'content_id' => $podcast->id,
                        'location_type' => get_class($programLocation),
                        'location_id' => $programLocation->id,
                    ]);

                    app(PodcastIndexService::class)->updateOrCreate($podcast);
                }
            }
            $this->output->progressAdvance();
        }

        $this->output->progressFinish();

        $this->log('Update locations defined in programs to podcasts who haven\'t any.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }
}

<?php

namespace App\Console\Commands\Programs;

use App\Console\Commands\CommandAbstract;
use App\Models\Audio\Podcast;
use App\Models\Radio\Program;
use App\Models\Radio\RadioStation;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\PodcastIndexService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Storage;
use Owenoj\LaravelGetId3\GetId3;

class BulkImportPodcasts extends CommandAbstract
{
    /** @var string */
    protected $signature = 'programs:bulk:import:podcasts {program_id} {published_at} {stations_ids?} {--replay} {--order_title_asc} {--order_title_desc}';

    /** @var string */
    protected $description = 'Import multiples podcasts in "storage/app/public/winmedia/imports" for a program.
                            {program_id : Program ID to add podcasts to}
                            {published_at : Date of publication for podcasts "Y-m-d H:i:s" or modes "from_file_created", "from_file_modified", "from_file_name_yyyymmdd" ("yyyymmdd" or "yyyy mm dd")}
                            {stations_ids : List of ID winmedia stations "2,3,5" (Optional: all stations)}
                            {--replay : Set podcasts as Replay (Optional: default Original)}
                            {--order_title_asc : Order by title ASC (title_1 set as more recent than title_2)}
                            {--order_title_desc : Order by title DESC (title_2 set as more recent than title_1)}';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Import multiples podcasts for a program...', 'title');

        $program = Program::find($this->argument('program_id'));
        if (! $program) {
            $this->log('Unknow program', 'error');

            return Command::INVALID;
        }

        $publishedAt = null;
        $fromFile = null;
        if ($this->argument('published_at') === 'from_file_created') {
            $fromFile = 'created_at';
        } elseif ($this->argument('published_at') === 'from_file_modified') {
            $fromFile = 'modified_at';
        } elseif ($this->argument('published_at') === 'from_file_name_yyyymmdd') {
            $fromFile = 'name_yyyymmdd';
        } elseif (Date::hasFormat($this->argument('published_at'), 'Y-m-d H:i:s')) {
            $publishedAt = Date::parse($this->argument('published_at'), 'Europe/Paris')->timezone('UTC');
        } else {
            $this->log('Date format invalid', 'error');

            return Command::INVALID;
        }

        if ($this->argument('stations_ids')) {
            $stations = RadioStation::whereIn('winmedia_id', explode(',', $this->argument('stations_ids')))->get();
            $radioStationIds = $stations->pluck('id');
        }

        if ($this->confirm('Start with program "' . $program->title . '" (' . $program->id . '), '
                . ($this->argument('stations_ids') ? '" and stations "' . $this->argument('stations_ids') . '" ?' : '" and all stations ?'))
        ) {
            $this->lock();

            $files = Storage::disk('public')->files('winmedia/imports');

            if ($this->option('order_title_asc')) {
                rsort($files);
            } elseif ($this->option('order_title_desc')) {
                sort($files);
            }

            $this->output->progressStart(count($files));

            foreach ($files as $file) {
                $this->log($file, 'info');

                $filename = pathinfo($file)['filename'];

                if ($fromFile === 'created_at') {
                    $filePath = Storage::disk('public')->path($file);
                    $fileCreatedAt = filectime($filePath);
                    $publishedAt = Date::createFromTimestamp($fileCreatedAt);
                } elseif ($fromFile === 'modified_at') {
                    $filePath = Storage::disk('public')->path($file);
                    $fileModifiedAt = filemtime($filePath);
                    $publishedAt = Date::createFromTimestamp($fileModifiedAt);
                } elseif ($fromFile === 'name_yyyymmdd') {
                    if (preg_match('/(\d{4})\s*(\d{2})\s*(\d{2})/', $filename, $matches)) {
                        $publishedAt = Date::createFromFormat('Y-m-d', $matches[1] . '-' . $matches[2] . '-' . $matches[3])
                            ->setTimezone('Europe/Paris')
                            ->setTime(0, 0)
                            ->setTimezone('UTC');
                        $filename = trim(preg_replace('/\d{4}\s*\d{2}\s*\d{2}/', '', $filename));
                    } else {
                        $publishedAt = Date::now();
                    }
                }

                $audioFileID3 = GetId3::fromDiskAndPath('public', $file);

                $podcast = Podcast::create([
                    'program_id' => $program->id,
                    'thematic_id' => $program->thematic_id,
                    'title' => $filename,
                    'tags' => $program->tags,
                    'description' => $program->description,
                    'type' => $this->option('replay') ? Podcast::TYPE_REPLAY : Podcast::TYPE_ORIGINAL,
                    'published_at' => $publishedAt,
                    'duration' => (int) $audioFileID3->getPlaytimeSeconds() ?: $program->duration,
                    'active' => true,
                ]);

                Config::set('media-library.max_file_size', 1024 * 1024 * 600); // 600 Mo
                $podcast->addMedia(Storage::disk('public')->path($file))->toMediaCollection('audio');

                $authorsIdsWithPivot = collect($program->authors()->pluck('id')->toArray())->mapWithKeys(fn (
                    int $authorId,
                    int $index
                ) => [$authorId => ['index' => $index]])->toArray();
                $podcast->authors()->sync($authorsIdsWithPivot);

                if (isset($radioStationIds)) {
                    $podcast->radioStations()->sync($radioStationIds);
                }

                if ($program->getFirstMedia('cover')) {
                    $program->getFirstMedia('cover')->copy($podcast, 'cover');
                }

                app(PodcastIndexService::class)->updateOrCreate($podcast);
                app(CacheService::class)->clearBrowsePodcastsCache();

                if ($this->option('order_title_asc') || $this->option('order_title_desc')) {
                    $publishedAt = $publishedAt->addMinute();
                }

                $this->output->progressAdvance();
            }
            $this->output->progressFinish();
            $this->log('Import multiples podcasts for a program.', 'success');
            $this->unlock();

            return Command::SUCCESS;
        }

        return Command::INVALID;
    }
}

<?php

namespace App\Console\Commands\Programs;

use App\Console\Commands\CommandAbstract;
use App\Models\Radio\Program;
use App\Services\Elasticsearch\PodcastIndexService;
use Illuminate\Console\Command;

class UpdateAuthors extends CommandAbstract
{
    /** @var string */
    protected $signature = 'programs:authors:update';

    /** @var string */
    protected $description = 'Update authors defined in programs to podcasts who haven\'t any.';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Update authors defined in programs to podcasts who haven\'t any.', 'title');
        $this->lock();

        $programs = Program::whereHas('authors')
            ->with(['authors', 'podcasts' => ['contentLocations', 'thematic', 'program']])
            ->get();

        $this->output->progressStart(count($programs));

        /** @var \App\Models\Radio\Program $program */
        foreach ($programs as $program) {
            /** @var \App\Models\Audio\Podcast $podcast */
            foreach ($program->podcasts as $podcast) {
                if (count($podcast->authors()->get()) === 0) {
                    $authorsIdsWithPivot = collect($program->authors()->pluck('id')->toArray())->mapWithKeys(fn (
                        int $authorId,
                        int $index
                    ) => [$authorId => ['index' => $index]])->toArray();
                    $podcast->authors()->sync($authorsIdsWithPivot);
                    app(PodcastIndexService::class)->updateOrCreate($podcast);
                }
            }
            $this->output->progressAdvance();
        }

        $this->output->progressFinish();

        $this->log('Update authors defined in programs to podcasts who haven\'t any.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }
}

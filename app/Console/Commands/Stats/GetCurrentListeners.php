<?php

namespace App\Console\Commands\Stats;

use App\Console\Commands\CommandAbstract;
use App\Models\Radio\RadioStation;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class GetCurrentListeners extends CommandAbstract
{
    /** @var string */
    protected $signature = 'stats:current:listeners';

    /** @var string */
    protected $description = 'Get currents listeners on SUN.';

    public function handle(): int
    {
        $this->log('Get currents listerners...', 'title');

        $mountPoints = $this->getMountsPointsStations();

        $currentListeners = $this->getCurrentListeners($mountPoints);

        Cache::forever('current-listeners', $currentListeners);

        $this->log('Get currents listerners (' . $currentListeners . ')', 'success');

        return Command::SUCCESS;
    }

    protected function getMountsPointsStations(): array
    {
        $stations = RadioStation::where('active', 1)->get();

        $mountPoints = [];

        foreach ($stations as $station) {
            array_push($mountPoints, parse_url($station->stream_url_ld, PHP_URL_PATH));
            if ($station->stream_url_ld != $station->stream_url_hd) {
                array_push($mountPoints, parse_url($station->stream_url_hd, PHP_URL_PATH));
            }
        }

        return $mountPoints;
    }

    protected function getCurrentListeners(array $mountPoints): int
    {
        try {
            $response = Http::get('http://admin:' . config('api.icecast_server_admin_password') . '@diffusion.lafrap.fr/admin/stats');
        } catch (Exception $e) {
            return 0;
        }

        if ($response->status() !== 200) {
            return 0;
        }

        $xml = simplexml_load_string($response->body());
        $stats = json_decode(json_encode($xml), true);

        $currentListeners = 0;

        foreach ($stats['source'] as $source) {
            if (in_array($source['@attributes']['mount'], $mountPoints)) {
                $currentListeners = $currentListeners + $source['listeners'];
            }
        }

        return $currentListeners;
    }
}

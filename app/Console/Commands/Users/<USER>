<?php

namespace App\Console\Commands\Users;

use App\Console\Commands\CommandAbstract;
use App\Models\Users\User;
use App\Services\Users\UsersService;
use Illuminate\Console\Command;

class ResetAnonymousAvatars extends CommandAbstract
{
    /** @var string */
    protected $signature = 'user:reset:anonymous:avatars';

    /** @var string */
    protected $description = 'Replace old anonymous avatars with new randoms avatars';

    public function handle(): int
    {
        $this->log('Started replace users avatars', 'title');

        $users = User::all()->filter(function ($user) {
            return $user->getFirstMedia('profile_picture')->file_name === 'anonymous-user.png';
        });

        $this->output->progressStart(count($users));

        foreach ($users as $user) {
            app(UsersService::class)->setDefaultAvatar($user);
            $this->output->progressAdvance();
        }

        $this->log('Finished replace users avatars.', 'success');

        return Command::SUCCESS;
    }
}

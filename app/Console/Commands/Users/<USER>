<?php

namespace App\Console\Commands\Users;

use App\Console\Commands\CommandAbstract;
use App\Models\Users\UserJourney;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Date;

class ClearSessionUserJourneys extends CommandAbstract
{
    /** @var string */
    protected $signature = 'user:journeys:clear';

    /** @var string */
    protected $description = 'Flush expired session user journeys.';

    public function handle(): int
    {
        $this->log('Started flushing expired session user journeys...', 'title');
        $expiredAt = Date::now()->subMinutes(config('session.lifetime') + 5);
        UserJourney::whereNotNull('session_id')->where('updated_at', '<', $expiredAt)->delete();
        $this->log('Finished flushing expired session user journeys.', 'success');

        return Command::SUCCESS;
    }
}

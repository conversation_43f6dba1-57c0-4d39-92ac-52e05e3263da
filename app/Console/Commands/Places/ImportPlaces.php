<?php

namespace App\Console\Commands\Places;

use App\Console\Commands\CommandAbstract;
use App\Models\Map\Place;
use App\Models\Map\PlaceType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use MatanYadaev\EloquentSpatial\Objects\Point as EloquentSpatialPoint;

class ImportPlaces extends CommandAbstract
{
    /** @var string */
    protected $signature = 'places:import {table?} {sources?} {types?} {--update}';

    /** @var string */
    protected $description = 'Import places from table.
                            {table : Name of table  (Optional: lieux_equipements)}
                            {sources : List of sources to import "openstreetmap,data.culture.gouv.fr,sun" (Optional: all)}
                            {types : List of types to import "bar,library,attraction" (Optional: all)}
                            {--update : Update existing places (Optional: skip if existing)';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Import places from table...', 'title');

        $table = $this->argument('table') ?? 'lieux_equipements';
        $sources = null;
        $types = null;

        /**
         * import column status :
         * 0 : to import
         * 1 : import ok
         * 2 : update existing
         * 3 : skip existing
         * 5 : exported from Laravel
         */
        if (! Schema::hasTable($table)) {
            $this->log('Unknow table', 'error');

            return Command::INVALID;
        }

        if ($this->argument('sources')) {
            $sources = array_values(array_unique(explode(',', $this->argument('sources'))));
        }
        if ($this->argument('types')) {
            $types = array_values(array_unique(explode(',', $this->argument('types'))));
        }

        if ($this->option('update')) {
            $this->log('Option --update will replace all existing places with new data from import', 'warning');
        }

        if ($this->confirm('Start with table "' . $table . '" '
            . (! $sources ? 'on all sources ' : ('on sources "' . $this->argument('sources') . '" '))
            . (! $types ? 'and all types ?' : ('and types "' . $this->argument('types') . '" ?'))
        )
        ) {
            $this->lock();

            $inputDataPlaces = DB::table($table)
                ->where('import', '=', 0)
                ->when($sources, function ($query, $sources) {
                    return $query->whereIn('source', $sources);
                })
                ->when($types, function ($query, $types) {
                    return $query->whereIn('type', $types);
                })
                ->get();

            $this->output->progressStart(count($inputDataPlaces));

            foreach ($inputDataPlaces as $inputDataPlace) {
                $this->log($inputDataPlace->name, 'info');

                $existingPlace = null;
                $sourceId = null;
                $sourceName = null;

                switch ($inputDataPlace->source) {
                    case 'openstreetmap':
                        $sourceId = $inputDataPlace->id_openstreetmap;
                        $sourceName = 'openstreetmap';
                        $existingPlace = Place::where([
                            ['source_name', $inputDataPlace->source],
                            ['source_id', $inputDataPlace->id_openstreetmap],
                        ])->first();
                        break;
                    case 'data.culture.gouv.fr':
                        $sourceId = $inputDataPlace->identifiant_deps;
                        $sourceName = 'data.culture.gouv.fr';
                        $existingPlace = Place::where([
                            ['source_name', $inputDataPlace->source],
                            ['source_id', $inputDataPlace->identifiant_deps],
                        ])->first();
                        break;
                    case 'sun':
                        if ($inputDataPlace->id_openstreetmap) {
                            $sourceId = $inputDataPlace->id_openstreetmap;
                            $sourceName = 'openstreetmap';
                            $existingPlace = Place::where([
                                ['source_name', 'openstreetmap'],
                                ['source_id', $inputDataPlace->id_openstreetmap],
                            ])->first();
                        }

                        break;
                }

                if (! $existingPlace) {
                    $place = Place::create([
                        'name' => $inputDataPlace->name,
                        'type' => PlaceType::getTypeFromMapping($inputDataPlace->type),
                        'addr_street1' => $inputDataPlace->addr_street,
                        'addr_street2' => null,
                        'addr_city' => $inputDataPlace->addr_city,
                        'addr_zip' => $inputDataPlace->addr_zip != 0 ? $inputDataPlace->addr_zip : null,
                        'enabled' => true,
                        'informations' => $inputDataPlace->information,
                        'source_name' => $sourceName,
                        'source_id' => $sourceId,
                    ]);

                    $place->points()->create([
                        'coord' => new EloquentSpatialPoint((float) $inputDataPlace->latitude, (float) $inputDataPlace->longitude),
                    ]);

                    DB::table($table)
                        ->where('id', $inputDataPlace->id)
                        ->update([
                            'import' => 1,
                        ]);
                } else {
                    if ($this->option('update')) {
                        $existingPlace->update([
                            'name' => $inputDataPlace->name,
                            'type' => PlaceType::getTypeFromMapping($inputDataPlace->type),
                            'addr_street1' => $inputDataPlace->addr_street,
                            'addr_street2' => '',
                            'addr_city' => $inputDataPlace->addr_city,
                            'addr_zip' => $inputDataPlace->addr_zip != 0 ? $inputDataPlace->addr_zip : null,
                            'enabled' => true,
                            'informations' => $inputDataPlace->information,
                            'source_name' => $sourceName,
                            'source_id' => $sourceId,
                        ]);
                        $existingPoint = $existingPlace->currentPoint;
                        if ($existingPoint) {
                            $existingPoint->coord = new EloquentSpatialPoint((float) $inputDataPlace->latitude, (float) $inputDataPlace->longitude);
                            $existingPoint->save();
                        } else {
                            $existingPlace->points()->create([
                                'coord' => new EloquentSpatialPoint((float) $inputDataPlace->latitude, (float) $inputDataPlace->longitude),
                            ]);
                        }
                    }
                    DB::table($table)
                        ->where('id', $inputDataPlace->id)
                        ->update([
                            'import' => $this->option('update') ? 2 : 3,
                        ]);
                }

                $this->output->progressAdvance();
            }

            $this->output->progressFinish();
            $this->log('Import places from table.', 'success');
            $this->unlock();

            return Command::SUCCESS;
        }

        return Command::INVALID;
    }
}

<?php

namespace App\Console\Commands\Places;

use App\Console\Commands\CommandAbstract;
use App\Models\Map\Place;
use App\Models\Map\PlaceType;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ExportPlaces extends CommandAbstract
{
    /** @var string */
    protected $signature = 'places:export {table?} {sources?}';

    /** @var string */
    protected $description = 'Export places into table.
                            {table : Name of table  (Optional: lieux_equipements)}
                            {sources : List of sources to import "openstreetmap,data.culture.gouv.fr,sun" (Optional: default sun only)}';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Export places into table with import value at "5"...', 'title');

        $table = $this->argument('table') ?? 'lieux_equipements';
        $sources = array_values(array_unique(explode(',', $this->argument('sources') ?? 'sun')));

        /**
         * import column status :
         * 0 : to import
         * 1 : import ok
         * 2 : update existing
         * 3 : skip existing
         * 5 : exported from Laravel
         */
        if (! Schema::hasTable($table)) {
            $this->log('Unknow table', 'error');

            return Command::INVALID;
        }

        if ($this->confirm('Start with table "' . $table . '" ' . 'on sources "' . implode(',', $sources) . '" ?')) {
            $this->lock();

            $exportDataPlaces = Place::with('currentPoint')
                ->where('type', '!=', PlaceType::RadioStudio)
                ->when($sources, function ($query, $sources) {
                    if (in_array('sun', $sources)) {
                        $query->where(function ($q) use ($sources) {
                            $q->whereIn('source_name', $sources)
                                ->orWhereNull('source_name')
                                ->orWhere('source_name', '');
                        });
                    } else {
                        $query->whereIn('source_name', $sources);
                    }

                    return $query;
                })
                ->get();

            $this->output->progressStart(count($exportDataPlaces));

            foreach ($exportDataPlaces as $exportDataPlace) {
                DB::table($table)
                    ->insert([
                        'name' => $exportDataPlace->name,
                        'type' => $exportDataPlace->type,
                        'addr_street' => $exportDataPlace->addr_street1,
                        'addr_city' => $exportDataPlace->addr_city,
                        'addr_zip' => $exportDataPlace->addr_zip,
                        'latitude' => $exportDataPlace->currentPoint->coord->latitude,
                        'longitude' => $exportDataPlace->currentPoint->coord->longitude,
                        'information' => $exportDataPlace->informations,
                        'source' => empty($exportDataPlace->source_name) ? 'sun' : $exportDataPlace->source_name,
                        'identifiant_deps' => $exportDataPlace->source_name === 'data.culture.gouv.fr' ? $exportDataPlace->source_id : null,
                        'id_openstreetmap' => $exportDataPlace->source_name === 'openstreetmap' ? $exportDataPlace->source_id : null,
                        'coordinates' => $exportDataPlace->currentPoint->coord->toWkt(),
                        'import' => 5,
                    ]);
                $this->output->progressAdvance();
            }

            $this->output->progressFinish();
            $this->log('Export places into table.', 'success');
            $this->unlock();

            return Command::SUCCESS;
        }

        return Command::INVALID;
    }
}

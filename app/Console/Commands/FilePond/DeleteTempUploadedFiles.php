<?php

namespace App\Console\Commands\FilePond;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Storage;

class DeleteTempUploadedFiles extends Command
{
    protected $signature = 'filepond:delete:temp:uploaded:files';

    protected $description = 'Delete temporary uploaded files older than 7 days.';

    public function handle(): void
    {
        foreach (Storage::directories('tmp') as $directory) {
            $directoryLastModified = Carbon::createFromTimestamp(Storage::lastModified($directory));

            if (now()->diffInDays($directoryLastModified) > 7) {
                Storage::deleteDirectory($directory);
            }
        }
    }
}

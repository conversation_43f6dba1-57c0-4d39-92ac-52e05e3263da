<?php

namespace App\Console\Commands\Announcements;

use App\Console\Commands\CommandAbstract;
use App\Models\Announcements\Announcement;
use Illuminate\Console\Command;

class ClearAnnouncements extends CommandAbstract
{
    /** @var string */
    protected $signature = 'announcements:clear';

    /** @var string */
    protected $description = 'Clear and softDelete passed announcements.';

    /** @throws \ErrorException */
    public function handle(): int
    {
        $this->log('Clear and softDelete passed announcements.', 'title');
        $this->lock();

        // Delete announcements with unpulished_at is not null and in the past
        Announcement::whereNotNull('unpublished_at')
            ->where('unpublished_at', '<', now())
            ->delete();

        $this->log('Clear and softDelete passed announcements.', 'success');
        $this->unlock();

        return Command::SUCCESS;
    }
}

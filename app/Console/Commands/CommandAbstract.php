<?php

namespace App\Console\Commands;

use ErrorException;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use ReflectionClass;

abstract class CommandAbstract extends Command
{
    /** @throws \ErrorException */
    public function lock(): void
    {
        $commandLockKey = $this->getCommandLockKey();
        if (Cache::get($commandLockKey)) {
            throw new ErrorException('Command is currently running.');
        }
        Cache::put($commandLockKey, true, 43200); // Locked for 12h if not unlocked.
    }

    protected function getCommandLockKey(): string
    {
        $reflection = new ReflectionClass($this);

        return Str::snake($reflection->getShortName());
    }

    public function unlock(): void
    {
        Cache::forget($this->getCommandLockKey());
    }

    protected function log(string $message, string $level): void
    {
        if (config('app.debug')) {
            Log::debug($message);
        }
        $this->output->{$level}($message);
    }
}

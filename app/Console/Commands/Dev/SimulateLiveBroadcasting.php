<?php

namespace App\Console\Commands\Dev;

use App\Console\Commands\CommandAbstract;
use App\Models\Audio\Song;
use App\Models\Audio\SongHistory;
use App\Models\Users\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Http;

class SimulateLiveBroadcasting extends CommandAbstract
{
    /** @var string */
    protected $signature = 'live:broadcasting:simulate';

    /** @var string */
    protected $description = 'Simulate live broadcasting for development purpose.';

    /** @throws \Exception */
    public function handle(): int
    {
        $this->log('Simulating live broadcasting...', 'title');
        $radiostations = radioStations();
        foreach ($radiostations as $radioStation) {
            $song = Song::winmediaType()->inRandomOrder()->first();
            $user = $song->isMusic ? (random_int(1, 10) >= 7 ? User::inRandomOrder()->first() : null) : null;
            $now = Date::now();
            Http::withHeaders(['Authorization' => 'Basic ' . base64_encode(config('api.winmedia_api_key'))])
                ->post(route('api.liveBroadcasting.store'), [
                    'winmedia_radio_station_id' => $radioStation->winmedia_id,
                    'winmedia_song_id' => $song->imedia,
                    'dedication_user_id' => $user?->id,
                    'real_duration' => random_int(120000, 300000), // In milliseconds => between 2 and 5 minutes
                    'started_at' => $now->toDateTimeString(),
                ]);
            SongHistory::create([
                'imedia' => $song->imedia,
                'dateheure_diffusion' => $now,
            ]);
        }
        $this->log(
            'Finished live broadcasting simulation for ' . $radiostations->count() . ' radio stations.',
            'success'
        );

        return Command::SUCCESS;
    }
}

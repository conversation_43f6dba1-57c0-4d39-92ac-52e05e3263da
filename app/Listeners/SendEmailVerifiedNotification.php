<?php

namespace App\Listeners;

use App\Notifications\EmailVerified;
use Illuminate\Auth\Events\Verified;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendEmailVerifiedNotification implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @return void
     */
    public function handle(Verified $event)
    {
        /** @var \App\Models\Users\User $user */
        $user = $event->user;
        if (method_exists($user, 'notify')) {
            $user->notify(new EmailVerified());
        }
    }
}

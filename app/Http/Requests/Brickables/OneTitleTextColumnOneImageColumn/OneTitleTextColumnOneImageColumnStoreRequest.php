<?php

namespace App\Http\Requests\Brickables\OneTitleTextColumnOneImageColumn;

use App\Models\Brickables\OneTitleTextColumnOneImageColumnBrick;
use Illuminate\Foundation\Http\FormRequest;

class OneTitleTextColumnOneImageColumnStoreRequest extends FormRequest
{
    /** @throws \Okipa\MediaLibraryExt\Exceptions\CollectionNotFound */
    public function rules(): array
    {
        return [
            'title_left' => ['required', 'string'],
            'text_left' => ['required', 'string'],
            'url' => ['nullable', 'url', 'max:255'],
            'url_target_blank' => ['required', 'boolean'],
            'image_right' => array_merge(
                ['required'],
                app(OneTitleTextColumnOneImageColumnBrick::class)->getMediaValidationRules('images')
            ),
            'invert_order' => ['required', 'boolean'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'invert_order' => (bool) $this->invert_order,
            'url_target_blank' => (bool) $this->url_target_blank,
        ]);
    }
}

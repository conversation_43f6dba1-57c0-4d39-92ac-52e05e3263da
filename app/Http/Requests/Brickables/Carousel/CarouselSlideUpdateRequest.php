<?php

namespace App\Http\Requests\Brickables\Carousel;

use App\Models\Brickables\CarouselBrickSlide;
use Illuminate\Foundation\Http\FormRequest;

class CarouselSlideUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'image' => app(CarouselBrickSlide::class)->getMediaValidationRules('images'),
            'label' => ['nullable', 'string', 'max:255'],
            'caption' => ['nullable', 'string', 'max:255'],
            'url' => ['nullable', 'url', 'max:255'],
            'url_target_blank' => ['required', 'boolean'],
            'active' => ['required', 'boolean'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'active' => (bool) $this->active,
            'url_target_blank' => (bool) $this->url_target_blank,
        ]);
    }
}

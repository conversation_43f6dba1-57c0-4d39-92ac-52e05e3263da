<?php

namespace App\Http\Requests\Thematics;

use App\Models\Audio\Thematic;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ThematicStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'illustration' => array_merge(
                ['required'],
                app(Thematic::class)->getMediaValidationRules('illustrations')
            ),
            'title' => ['required', 'string', 'max:255', Rule::unique(Thematic::class)],
        ];
    }
}

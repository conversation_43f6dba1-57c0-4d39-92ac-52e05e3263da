<?php

namespace App\Http\Requests\Cookies;

use App\Models\Cookies\CookieCategory;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CookieCategoryStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'unique_key' => ['required', 'snakecase', 'max:255', Rule::unique(CookieCategory::class)],
            'title' => ['required', 'string', 'max:255', Rule::unique(CookieCategory::class)],
            'description' => ['nullable', 'string', 'max:4294967295'],
        ];
    }
}

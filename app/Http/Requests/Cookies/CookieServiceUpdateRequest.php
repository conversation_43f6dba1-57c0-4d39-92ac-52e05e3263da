<?php

namespace App\Http\Requests\Cookies;

use App\Models\Cookies\CookieCategory;
use App\Models\Cookies\CookieService;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CookieServiceUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'category_ids' => ['required', 'array', Rule::in(CookieCategory::pluck('id'))],
            'unique_key' => [
                'required',
                'snakecase',
                'max:255',
                Rule::unique(CookieService::class)->ignore($this->cookieService),
            ],
            'title' => [
                'required',
                'string',
                'max:255',
                Rule::unique(CookieService::class)->ignore($this->cookieService),
            ],
            'description' => ['nullable', 'string', 'max:4294967295'],
            'cookies' => ['nullable', 'json'],
            'required' => ['required', 'boolean'],
            'enabled_by_default' => ['required', 'boolean'],
            'active' => ['required', 'boolean'],
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'required' => (bool) $this->required,
            'enabled_by_default' => (bool) $this->enabled_by_default,
            'active' => (bool) $this->active,
        ]);
    }
}

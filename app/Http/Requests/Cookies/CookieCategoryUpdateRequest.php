<?php

namespace App\Http\Requests\Cookies;

use App\Models\Cookies\CookieCategory;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CookieCategoryUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'unique_key' => [
                'required',
                'snakecase',
                'max:255',
                Rule::unique(CookieCategory::class)->ignore($this->cookieCategory),
            ],
            'title' => [
                'required',
                'string',
                'max:255',
                Rule::unique(CookieCategory::class)->ignore($this->cookieCategory),
            ],
            'description' => ['nullable', 'string', 'max:4294967295'],
        ];
    }
}

<?php

namespace App\Http\Requests\Holidays;

use App\Models\Radio\Program;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Date;
use Illuminate\Validation\Rule;

class HolidaysStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'label' => ['required', 'string', 'max:255'],
            'started_at' => ['required', 'date'],
            'ended_at' => ['required', 'date', 'after:started_at'],
            'programs' => ['present', 'array'],
            'programs.*' => ['required', 'integer', Rule::exists(Program::class, 'id')],
            'active' => ['required', 'boolean'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'started_at' => Date::parse($this->started_at)->timezone('Europe/Paris')->format('Y-m-d'),
            'ended_at' => Date::parse($this->ended_at)->timezone('Europe/Paris')->format('Y-m-d'),
            'programs' => json_decode($this->programs ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'active' => (bool) $this->active,
        ]);
    }
}

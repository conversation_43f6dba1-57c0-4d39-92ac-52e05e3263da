<?php

namespace App\Http\Requests\Traits;

use App\Models\Map\Place;
use App\Models\Map\PlaceType;
use Illuminate\Validation\Rule;
use InvalidArgumentException;

trait HasLocationInfo
{
    protected function locationRules(bool $locationRequired = false, string $namePrefix = 'place'): array
    {
        $namePrefix = \trim($namePrefix);
        if (empty($namePrefix)) {
            throw new InvalidArgumentException('The prefix used in form field names is required.');
        }

        $rules = [
            "$namePrefix.create_new_place" => ['boolean'],
            "$namePrefix.place_id" => [
                "exclude_if:$namePrefix.create_new_place,true",
                $locationRequired ? 'required' : 'nullable',
                'integer',
                Rule::exists(Place::class, 'id'),
            ],
            "$namePrefix.name" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'required',
                'string',
                'max:255',
            ],
            "$namePrefix.type" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'required',
                Rule::enum(PlaceType::class),
            ],
            "$namePrefix.addr_street1" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'nullable',
                'string',
                'max:255',
            ],
            "$namePrefix.addr_street2" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'nullable',
                'string',
                'max:255',
            ],
            "$namePrefix.addr_city" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'nullable',
                'string',
                'max:255',
            ],
            "$namePrefix.addr_zip" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'nullable',
                'string',
                'max:255',
            ],
            "$namePrefix.informations" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'nullable',
                'json',
            ],
            "$namePrefix.latitude" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'required',
                'numeric',
                'between:-90,90',
            ],
            "$namePrefix.longitude" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'required',
                'numeric',
                'between:-180,180',
            ],
            "$namePrefix.source_name" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'nullable',
                'string',
                'max:255',
            ],
            "$namePrefix.source_id" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'nullable',
                'string',
                'max:255',
            ],
            "$namePrefix.enabled" => [
                "exclude_if:$namePrefix.create_new_place,false",
                'boolean',
            ],
        ];

        return $rules;
    }

    protected function prepareLocationValidation(string $namePrefix = 'place'): void
    {
        $data = $this->request->all($namePrefix);
        $data['create_new_place'] = isset($data['create_new_place']);

        if ($data['create_new_place']) {
            $data['latitude'] = $data['lat'] ?? null;
            $data['longitude'] = $data['lng'] ?? null;
            $data['source_name'] = $data['sourceName'] ?? null;
            $data['source_id'] = $data['sourceId'] ?? null;
            $data['enabled'] = isset($data['enabled']);

            unset($data['lat'], $data['lng'], $data['sourceName'], $data['sourceId']);
        }

        $this->merge([$namePrefix => $data]);
    }

    /** @return array<string, string> */
    protected function locationAttributes(string $namePrefix = 'place'): array
    {
        return [
            "$namePrefix.place_id" => 'Lieu',
            "$namePrefix.name" => 'Nom',
            "$namePrefix.type" => 'Type',
            "$namePrefix.addr_street1" => 'Voie',
            "$namePrefix.addr_street2" => 'Complément',
            "$namePrefix.addr_city" => 'Ville',
            "$namePrefix.addr_zip" => 'Code postal',
            "$namePrefix.informations" => 'Informations',
            "$namePrefix.latitude" => 'Latitude',
            "$namePrefix.longitude" => 'Longitude',
        ];
    }
}

<?php

namespace App\Http\Requests\Traits;

use App\Models\PageContents\PageContent;

trait HasSeoMeta
{
    protected function seoMetaRules(): array
    {
        return [
            'meta_title' => ['required', 'string', 'max:255'],
            'meta_description' => ['nullable', 'string', 'max:255'],
            'meta_image' => array_merge(['nullable'], app(PageContent::class)->getMediaValidationRules('seo')),
            'remove_meta_image' => ['required', 'boolean'],
        ];
    }

    protected function prepareSeoMetaRules(): void
    {
        $this->merge(['remove_meta_image' => (bool) $this->remove_meta_image]);
    }
}

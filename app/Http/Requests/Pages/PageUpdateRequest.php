<?php

namespace App\Http\Requests\Pages;

use App\Http\Requests\Traits\HasSeoMeta;
use App\Models\Pages\Page;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PageUpdateRequest extends FormRequest
{
    use HasSeoMeta;

    public function rules(): array
    {
        return array_merge([
            'slug' => [
                'required',
                'string',
                'slug',
                'max:255',
                Rule::unique(Page::class)->ignore($this->page),
            ],
            'nav_title' => ['required', 'string', 'max:255'],
            'active' => ['required', 'boolean'],
        ], $this->seoMetaRules());
    }

    protected function prepareForValidation(): void
    {
        $this->merge(['active' => (bool) $this->active]);
        $this->prepareSeoMetaRules();
    }
}

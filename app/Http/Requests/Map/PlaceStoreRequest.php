<?php

namespace App\Http\Requests\Map;

use App\Models\Map\PlaceType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PlaceStoreRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'type' => ['required', Rule::enum(PlaceType::class), 'max:255'],
            'addr_street1' => ['nullable', 'string', 'max:255'],
            'addr_street2' => ['nullable', 'string', 'max:255'],
            'addr_city' => ['nullable', 'string', 'max:255'],
            'addr_zip' => ['nullable', 'string', 'max:255'],
            'latitude' => ['required', 'numeric', 'between:-90,90'],
            'longitude' => ['required', 'numeric', 'between:-180,180'],
            'source_name' => ['nullable', 'string', 'max:255'],
            'source_id' => ['nullable', 'string', 'max:255'],
            'informations' => ['nullable', 'json'],
            'description' => ['nullable', 'string', 'max:65535'],
            'url' => ['nullable', 'string', 'max:255'],
            'enabled' => ['boolean'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'latitude' => $this->location ? $this->location['lat'] : null,
            'longitude' => $this->location ? $this->location['lng'] : null,
            'source_name' => $this->location ? $this->location['sourceName'] : null,
            'source_id' => $this->location ? $this->location['sourceId'] : null,
            'enabled' => (bool) $this->enabled,
        ]);
    }
}

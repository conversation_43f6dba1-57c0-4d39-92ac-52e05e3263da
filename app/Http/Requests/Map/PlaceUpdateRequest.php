<?php

namespace App\Http\Requests\Map;

use App\Models\Map\Place;
use App\Models\Map\PlaceType;
use Illuminate\Validation\Rule;

class PlaceUpdateRequest extends PlaceStoreRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $rules = parent::rules();
        $rules['point_id'] = ['required', 'integer'];
        $rules['merge_places_activation'] = ['required', 'boolean'];
        $rules['merge_places_ids'] = ['present', 'array'];
        $rules['merge_places_ids.*'] = [
            'required',
            Rule::exists(Place::class, 'id')->where(function ($query) {
                $query->where('type', '!=', PlaceType::RadioStudio);
            }),
        ];

        return $rules;
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'latitude' => $this->location ? $this->location['lat'] : null,
            'longitude' => $this->location ? $this->location['lng'] : null,
            'source_name' => $this->location ? $this->location['sourceName'] : null,
            'source_id' => $this->location ? $this->location['sourceId'] : null,
            'enabled' => (bool) $this->enabled,
            'merge_places_activation' => (bool) $this->merge_places_activation,
            'merge_places_ids' => json_decode($this->merge_places_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
        ]);
    }
}

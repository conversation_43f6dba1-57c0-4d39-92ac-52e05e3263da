<?php

namespace App\Http\Requests\Users;

use App\Models\Teams\Team;
use App\Models\Users\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Date;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class UserUpdateRequest extends FormRequest
{
    /** @throws \Okipa\MediaLibraryExt\Exceptions\CollectionNotFound */
    public function rules(): array
    {
        return [
            'profile_picture' => app(User::class)->getMediaValidationRules('profile_picture'),
            'remove_profile_picture' => ['required', 'boolean'],
            'username' => ['required', 'string', 'max:255'],
            'birth_date' => ['nullable', 'date', 'before:today'],
            'email' => [
                'required',
                'string',
                'email:rfc,dns,spoof',
                'max:255',
                Rule::unique(User::class)->ignore($this->user),
            ],
            'address' => ['nullable', 'string', 'max:255'],
            'city' => ['nullable', 'string', 'max:255'],
            'team_id' => ['required', 'int', Rule::exists(Team::class, 'id')],
            'new_password' => ['nullable', Password::defaults(), 'confirmed'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'remove_profile_picture' => (bool) $this->remove_profile_picture,
            'birth_date' => $this->birth_date ? Date::parse($this->birth_date)->timezone('Europe/Paris')->format('Y-m-d') : null,
        ]);
    }
}

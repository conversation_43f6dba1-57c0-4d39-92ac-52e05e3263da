<?php

namespace App\Http\Requests\Users;

use App\Models\Teams\Team;
use App\Models\Users\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Date;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class UserStoreRequest extends FormRequest
{
    /** @throws \Okipa\MediaLibraryExt\Exceptions\CollectionNotFound */
    public function rules(): array
    {
        return [
            'profile_picture' => app(User::class)->getMediaValidationRules('profile_picture'),
            'username' => ['required', 'string', 'max:255'],
            'birth_date' => ['nullable', 'date', 'before:today'],
            'email' => ['required', 'string', 'max:255', 'email:rfc,dns,spoof', Rule::unique(User::class)],
            'address' => ['nullable', 'string', 'max:255'],
            'city' => ['nullable', 'string', 'max:255'],
            'team_id' => ['required', 'int', Rule::exists(Team::class, 'id')],
            'password' => ['nullable', Password::defaults(), 'confirmed'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'birth_date' => $this->birth_date ? Date::parse($this->birth_date)->timezone('Europe/Paris')->format('Y-m-d') : null,
        ]);
    }
}

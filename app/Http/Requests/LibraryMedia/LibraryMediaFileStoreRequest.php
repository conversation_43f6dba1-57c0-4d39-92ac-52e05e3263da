<?php

namespace App\Http\Requests\LibraryMedia;

use App\Models\LibraryMedia\LibraryMediaCategory;
use App\Models\LibraryMedia\LibraryMediaFile;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LibraryMediaFileStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'category_id' => ['required', 'integer', Rule::exists(LibraryMediaCategory::class, 'id')],
            'name' => ['required', 'string', 'max:255'],
            'media' => array_merge(['required'], app(LibraryMediaFile::class)->getMediaValidationRules('media')),
        ];
    }
}

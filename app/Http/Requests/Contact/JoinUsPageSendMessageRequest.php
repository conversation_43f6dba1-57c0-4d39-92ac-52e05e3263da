<?php

namespace App\Http\Requests\Contact;

use App\Http\Livewire\Front\JoinUs\Form;
use App\Models\Logs\LogJoinUsFormMessage;
use App\Rules\PhoneInternational;
use App\Rules\ValidMultipleFilePondUpload;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class JoinUsPageSendMessageRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'application_type' => ['required', 'string', Rule::in(Form::APPLICATION_TYPE_OPTIONS)],
            'first_name' => ['required', 'string', 'max:255'],
            'last_name' => ['required', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'birth_year' => ['required', 'date_format:Y'],
            'email' => ['required', 'string', 'email:rfc,dns,spoof'],
            'phone_number' => ['nullable', 'string', 'max:255', new PhoneInternational()],
            'mission' => ['required', 'string', Rule::in(Form::MISSION_OPTIONS)],
            'studio' => ['required', 'string', Rule::in(Form::STUDIO_OPTIONS)],
            'message' => ['required', 'string', 'max:65535'],
            'experiences' => ['present', 'array'],
            'experiences.*' => ['string', Rule::in(Form::EXPERIENCE_OPTIONS)],
            'listening_types' => ['present', 'array'],
            'listening_types.*' => ['string', Rule::in(Form::LISTENING_TYPE_OPTIONS)],
            'listening_sun' => ['required', 'string', Rule::in(['non', 'oui'])],
            'favorite_music_types' => ['required', 'array'],
            'favorite_music_types.*' => ['string', Rule::in(Form::MUSIC_TYPE_OPTIONS)],
            'attachments' => [
                'present',
                'array',
                new ValidMultipleFilepondUpload(
                    app(LogJoinUsFormMessage::class)->getMediaCollection('attachments')->acceptsMimeTypes,
                    1024 * 1024 * 100
                ),
            ],

        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'experiences' => $this->experiences ?: [],
            'listening_types' => $this->listening_types ?: [],
            'favorite_music_types' => $this->favorite_music_types ?: [],
        ]);
    }
}

<?php

namespace App\Http\Requests\Songs;

use App\Models\Audio\Song;
use App\Rules\ValidUniquePerformerTitleVersion;
use Illuminate\Foundation\Http\FormRequest;

class SongUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'id' => [
                'required',
                new ValidUniquePerformerTitleVersion($this->input('performer'), $this->input('title'), $this->input('version')),
            ],
            'cover_source' => array_merge(
                ['nullable'],
                app(Song::class)->getMediaValidationRules('cover_source')
            ),
            'performer' => ['required', 'string', 'max:255'],
            'title' => ['required', 'string', 'max:255'],
            'version' => ['string', 'max:255'],
            'album' => ['required', 'string', 'max:255'],
            'year' => ['required', 'string', 'max:255'],
            'genre' => ['string', 'max:255'],
            'publisher' => ['string', 'max:255'],
            'comment' => ['string'],
            '_proprietes' => ['string', 'max:255'],
            '_proprietaire' => ['string', 'max:255'],
            'disc' => ['integer', 'min:1', 'max:10'],
            'track' => ['integer', 'min:1', 'max:100'],
            'performer_edit' => ['nullable'],
            'album_edit' => ['nullable'],
            '_release_date' => ['string', 'max:255'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'version' => $this->version ?? '',
            'genre' => $this->genre ?? '',
            'publisher' => $this->publisher ?? '',
            'comment' => $this->comment ?? '',
            '_proprietes' => $this->_proprietes ?? '',
            '_proprietaire' => $this->_proprietaire ?? '',
            'disc' => $this->disc ?? '',
            'track' => $this->track ?? '',
            '_release_date' => $this->_release_date ?? '',
        ]);
    }
}

<?php

namespace App\Http\Requests\Programs;

use App\Models\Radio\Program;
use App\Models\Radio\ProgramRecurrence;
use App\Models\Radio\RadioStation;
use App\Rules\InJson;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ProgramRecurrenceUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'label' => ['required', 'string', 'max:255'],
            'radio_station_ids' => ['required', 'array'],
            'radio_station_ids.*' => ['required', Rule::exists(RadioStation::class, 'id')],
            'sub_programs' => ['present', 'array'],
            'sub_programs.*.id' => ['required', 'integer', Rule::exists(Program::class, 'id')],
            'sub_programs.*.local_time' => ['required', 'date'],
            'months' => ['required', 'string', new InJson(range(1, 12))],
            'month_days' => ['required', 'string', new InJson(range(1, 31))],
            'week_days' => ['required', 'string', new InJson(range(1, 7))],
            'local_time' => ['required', 'date'],
            'month_week' => ['required', 'string', new InJson(range(1, 5))],
            'broadcast_week' => ['required', Rule::in(array_keys(ProgramRecurrence::BROADCAST_WEEK))],
            'holidays_break' => ['required', 'boolean'],
        ];
    }

    public function prepareForValidation(): void
    {
        $this->merge([
            'radio_station_ids' => json_decode($this->radio_station_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'sub_programs' => $this->sub_programs ?: [],
            'months' => $this->months ?: '[]',
            'month_days' => $this->month_days ?: '[]',
            'week_days' => $this->week_days ?: '[]',
            'month_week' => $this->month_week ?: '[]',
            'holidays_break' => (bool) $this->holidays_break,
        ]);
    }
}

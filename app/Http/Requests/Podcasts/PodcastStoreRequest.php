<?php

namespace App\Http\Requests\Podcasts;

use App\Http\Requests\Traits\HasLocationInfo;
use App\Http\Requests\Traits\HasSeoMeta;
use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Audio\Thematic;
use App\Models\Radio\Program;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use App\Rules\TagsLength;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class PodcastStoreRequest extends FormRequest
{
    use HasLocationInfo, HasSeoMeta;

    public function rules(): array
    {
        return array_merge(
            [
                // Media
                'cover' => array_merge(
                    ['required'],
                    app(Podcast::class)->getMediaValidationRules('cover')
                ),
                'audio' => array_merge(
                    ['required_if:type,' . Podcast::TYPE_ORIGINAL],
                    [
                        app(Podcast::class)->getMediaMimesValidationRules('audio'),
                        app(Podcast::class)->getMediaMimeTypesValidationRules('audio'),
                        'max:' . 1024 * 1024 * 600, // 600 Mo
                    ]
                ),
                // Config
                'program_id' => ['required', 'integer', Rule::exists(Program::class, 'id')],
                'thematic_id' => ['required', 'integer', Rule::exists(Thematic::class, 'id')],
                'type' => ['required', 'string', Rule::in(array_keys(Podcast::TYPES))],
                // Information
                'title' => ['required', 'string', 'max:255'],
                'tags' => ['required', 'array', 'min:1', new TagsLength(65535)],
                'description' => ['required', 'string', 'max:65535'],
                'radio_station_ids' => ['required', 'array'],
                'radio_station_ids.*' => ['required', Rule::exists(RadioStation::class, 'id')],
                'song_ids' => ['present', 'array'],
                'song_ids.*' => ['required', 'integer', Rule::exists(Song::class, 'id')],
                'authors_ids' => ['present', 'array'],
                'authors_ids.*' => ['required', Rule::exists(User::class, 'id')],
                'published_at' => ['required', 'date'],
                'active' => ['required', 'boolean'],
            ],
            $this->locationRules()
        );
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'tags' => collect(explode(',', $this->tags))->filter()->map(function (string $tag) {
                return trim(Str::replace('"', '', $tag));
            })->toArray(),
            'radio_station_ids' => json_decode($this->radio_station_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'song_ids' => Arr::pluck(json_decode($this->song_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR), 'value'),
            'authors_ids' => json_decode($this->authors_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'active' => (bool) $this->active,
        ]);
        $this->prepareLocationValidation();
    }

    public function attributes(): array
    {
        return $this->locationAttributes();
    }
}

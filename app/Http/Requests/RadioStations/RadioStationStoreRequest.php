<?php

namespace App\Http\Requests\RadioStations;

use App\Http\Requests\Traits\HasLocationInfo;
use App\Models\Radio\RadioStation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Intervention\Validation\Rules\Hexadecimalcolor;

class RadioStationStoreRequest extends FormRequest
{
    use HasLocationInfo;

    public function rules(): array
    {
        return array_merge(
            [
                'cover' => array_merge(
                    ['required'],
                    app(RadioStation::class)->getMediaValidationRules('cover')
                ),
                'winmedia_id' => ['required', 'integer', 'min:1', Rule::unique(RadioStation::class)],
                'name' => ['required', 'string', 'max:255'],
                'label' => ['nullable', 'string', 'max:255'],
                'stream_url_ld' => ['required', 'url', 'max:255'],
                'stream_url_hd' => ['required', 'url', 'max:255'],
                'color' => ['required', 'string', 'color' => new Hexadecimalcolor([6])],
                'active' => ['required', 'boolean'],
                'map_events_week_interval' => ['nullable', 'integer', 'min:1', 'max:100'],
                'map_news_week_interval' => ['nullable', 'integer', 'min:1', 'max:100'],
                'map_playlists_week_interval' => ['nullable', 'integer', 'min:1', 'max:100'],
                'map_podcasts_week_interval' => ['nullable', 'integer', 'min:1', 'max:100'],
                'map_performers_week_interval' => ['nullable', 'integer', 'min:1', 'max:100'],
            ],
            $this->locationRules()
        );
    }

    protected function prepareForValidation(): void
    {
        $this->merge(['active' => (bool) $this->active]);
        $this->prepareLocationValidation();
    }

    public function attributes(): array
    {
        return $this->locationAttributes();
    }
}

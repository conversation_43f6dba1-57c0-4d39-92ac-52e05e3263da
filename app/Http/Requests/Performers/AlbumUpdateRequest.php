<?php

namespace App\Http\Requests\Performers;

use App\Http\Requests\Traits\HasLocationInfo;
use App\Models\Performers\AlbumDetail;
use Illuminate\Foundation\Http\FormRequest;

class AlbumUpdateRequest extends FormRequest
{
    use HasLocationInfo;

    public function rules(): array
    {
        return array_merge(
            [
                'name' => ['required', 'string'],
                'detail_album_source' => array_merge(
                    ['nullable'],
                    app(AlbumDetail::class)->getMediaValidationRules('album_source')
                ),
                'detail' => ['present', 'array'],
                'published_at' => ['required', 'date'],
            ],
            $this->locationRules(),
        );
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'detail' => [
                'localisation' => $this->detail_localisation ?? null,
            ],
        ]);
        $this->prepareLocationValidation();
    }

    public function attributes(): array
    {
        return $this->locationAttributes();
    }
}

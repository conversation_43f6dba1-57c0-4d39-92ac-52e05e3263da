<?php

namespace App\Http\Requests\Performers;

use App\Http\Requests\Traits\HasLocationInfo;
use Illuminate\Foundation\Http\FormRequest;

class LabelUpdateRequest extends FormRequest
{
    use HasLocationInfo;

    public function rules(): array
    {
        return array_merge(
            [
                'name' => ['required', 'string'],
            ],
            $this->locationRules(),
        );
    }

    protected function prepareForValidation(): void
    {
        $this->prepareLocationValidation();
    }

    public function attributes(): array
    {
        return $this->locationAttributes();
    }
}

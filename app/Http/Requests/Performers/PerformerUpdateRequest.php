<?php

namespace App\Http\Requests\Performers;

use App\Models\Performers\Member;
use App\Models\Performers\PerformerDetail;
use Date;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class PerformerUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string'],
            'detail_artist_picture_source' => array_merge(
                ['nullable'],
                app(PerformerDetail::class)->getMediaValidationRules('artist_picture_source')
            ),
            'detail' => ['present', 'array'],
            'detail.biography' => ['nullable', 'string'],
            'detail.alias_name' => ['nullable', 'string'],
            'detail.begin_city' => ['nullable', 'string'],
            'detail.current_city' => ['nullable', 'string'],
            'detail.begin_date' => ['nullable', 'date', 'before_or_equal:today'],
            'detail.end_date' => ['nullable', 'date', 'before_or_equal:today'],
            'detail.genres' => ['nullable', 'string'],
            'detail.url_web' => ['nullable', 'string'],
            'detail.url_wiki' => ['nullable', 'string'],
            'detail.url_bandcamp' => ['nullable', 'string'],
            'detail.url_discogs' => ['nullable', 'string'],
            'detail.url_instagram' => ['nullable', 'string'],
            'detail.url_facebook' => ['nullable', 'string'],
            'detail.url_youtube' => ['nullable', 'string'],
            'members_ids' => ['present', 'array'],
            'members_ids.*' => ['required', Rule::exists(Member::class, 'id')],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'detail' => [
                'biography' => $this->detail_biography ?? null,
                'alias_name' => $this->detail_alias_name ?? null,
                'begin_city' => $this->detail_begin_city ?? null,
                'current_city' => $this->detail_current_city ?? null,
                'begin_date' => $this->detail_begin_date ? Date::parse($this->detail_begin_date)->timezone('Europe/Paris')->format('Y-m-d') : null,
                'end_date' => $this->detail_end_date ? Date::parse($this->detail_end_date)->timezone('Europe/Paris')->format('Y-m-d') : null,
                'genres' => $this->detail_genres ?? null,
                'url_web' => $this->detail_url_web ?? null,
                'url_wiki' => $this->detail_url_wiki ?? null,
                'url_bandcamp' => $this->detail_url_bandcamp ?? null,
                'url_discogs' => $this->detail_url_discogs ?? null,
                'url_instagram' => $this->detail_url_instagram ?? null,
                'url_facebook' => $this->detail_url_facebook ?? null,
                'url_youtube' => $this->detail_url_youtube ?? null,
            ],
            'members_ids' => json_decode($this->members_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
        ]);
    }
}

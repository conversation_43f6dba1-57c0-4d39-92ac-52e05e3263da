<?php

namespace App\Http\Requests\Performers;

use App\Models\Performers\Performer;
use Date;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class MemberUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'first_name' => ['required', 'string'],
            'last_name' => ['required', 'string'],
            'nick_name' => ['nullable', 'string'],
            'roles' => ['nullable', 'string'],
            'begin_date' => ['nullable', 'date', 'before_or_equal:today'],
            'end_date' => ['nullable', 'date', 'before_or_equal:today'],
            'performers_ids' => ['nullable', 'array'],
            'performers_ids.*' => ['required', Rule::exists(Performer::class, 'id')],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'performers_ids' => json_decode($this->performers_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'begin_date' => $this->begin_date ? Date::parse($this->begin_date)->timezone('Europe/Paris')->format('Y-m-d') : null,
            'end_date' => $this->end_date ? Date::parse($this->end_date)->timezone('Europe/Paris')->format('Y-m-d') : null,
        ]);
    }
}

<?php

namespace App\Http\Requests\Events;

use App\Http\Requests\Traits\HasLocationInfo;
use App\Models\Audio\Thematic;
use App\Models\Events\Event;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use App\Rules\TagsLength;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class EventUpdateRequest extends FormRequest
{
    use HasLocationInfo;

    public function rules(): array
    {
        return array_merge(
            [
                'cover' => array_merge(
                    ['nullable'],
                    app(Event::class)->getMediaValidationRules('cover')
                ),
                'audio' => array_merge(
                    ['nullable'],
                    [
                        app(Event::class)->getMediaMimesValidationRules('audio'),
                        app(Event::class)->getMediaMimeTypesValidationRules('audio'),
                        'max:' . 1024 * 1024 * 600,
                    ]
                ),
                'remove_audio_file' => ['required', 'boolean'],
                'thematic_id' => ['nullable', 'integer', Rule::exists(Thematic::class, 'id')],
                'user_id' => ['nullable', 'int', Rule::exists(User::class, 'id')],
                'radio_station_ids' => ['required', 'array'],
                'radio_station_ids.*' => ['required', Rule::exists(RadioStation::class, 'id')],
                'title' => ['required', 'string', 'max:255'],
                'description' => ['required', 'string', 'max:4294967295'],
                'audio_caption' => ['nullable', 'string', 'max:255'],
                'tags' => ['required', 'array', 'min:1', new TagsLength(65535)],
                'started_at' => ['required', 'date'],
                'ended_at' => ['required', 'date', 'after_or_equal:started_at'],
                'active' => ['required', 'boolean'],
            ],
            $this->locationRules(locationRequired: true)
        );
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'tags' => collect(explode(',', $this->tags))->filter()->map(function (string $tag) {
                return trim(Str::replace('"', '', $tag));
            })->toArray(),
            'radio_station_ids' => json_decode($this->radio_station_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'remove_audio_file' => (bool) $this->remove_audio_file,
            'active' => (bool) $this->active,
        ]);

        $this->prepareLocationValidation();
    }

    public function attributes(): array
    {
        return $this->locationAttributes();
    }
}

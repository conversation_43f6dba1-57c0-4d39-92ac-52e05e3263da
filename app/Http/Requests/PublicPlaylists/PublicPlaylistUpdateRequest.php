<?php

namespace App\Http\Requests\PublicPlaylists;

use App\Http\Requests\Traits\HasLocationInfo;
use App\Models\Audio\Playlist;
use App\Models\Audio\Song;
use App\Models\Audio\Thematic;
use App\Models\Radio\RadioStation;
use App\Rules\TagsLength;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class PublicPlaylistUpdateRequest extends FormRequest
{
    use HasLocationInfo;

    public function rules(): array
    {
        return array_merge(
            [
                'cover' => [
                    'nullable',
                    ...app(Playlist::class)->getMediaValidationRules('cover'),
                ],
                'remove_cover' => ['required', 'boolean'],
                'title' => ['required', 'string', 'max:255'],
                'radio_station_ids' => ['required', 'array'],
                'radio_station_ids.*' => ['required', Rule::exists(RadioStation::class, 'id')],
                'thematic_id' => ['required', 'integer', Rule::exists(Thematic::class, 'id')],
                'tags' => ['required', 'array', 'min:1', new TagsLength(65535)],
                'rail_displayed' => ['required', 'boolean'],
                'description' => ['nullable', 'string', 'max:65535'],
                'published_at' => ['required', 'date'],
                'unpublished_at' => ['nullable', 'date'],
                'active' => ['required', 'boolean'],
                'song_ids' => ['required', 'array'],
                'song_ids.*' => ['required', 'integer', Rule::exists(Song::class, 'id')],
            ],
            $this->locationRules()
        );
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'remove_cover' => (bool) $this->remove_cover,
            'radio_station_ids' => json_decode($this->radio_station_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'tags' => collect(explode(',', $this->tags))->filter()->map(function (string $tag) {
                return trim(Str::replace('"', '', $tag));
            })->toArray(),
            'rail_displayed' => (bool) $this->rail_displayed,
            'song_ids' => Arr::pluck(json_decode($this->song_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR), 'value'),
            'active' => (bool) $this->active,
        ]);
        $this->prepareLocationValidation();
    }
}

<?php

namespace App\Http\Requests\Settings;

use App\Models\Settings\Settings;
use App\Rules\PhoneInternational;
use Illuminate\Foundation\Http\FormRequest;

class SettingsUpdateRequest extends FormRequest
{
    /**
     * @throws \Okipa\MediaLibraryExt\Exceptions\CollectionNotFound
     * @throws \Exception
     */
    public function rules(): array
    {
        return [
            'logo_squared' => app(Settings::class)->getMediaValidationRules('logo_squared'),
            'program_schedule' => app(Settings::class)->getMediaValidationRules('program_schedule'),
            'email' => ['required', 'string', 'max:255', 'email:rfc,dns,spoof'],
            'phone_number' => ['required', 'string', 'max:255', new PhoneInternational()],
            'address' => ['required', 'string', 'max:255'],
            'zip_code' => ['required', 'string', 'max:255'],
            'city' => ['required', 'string', 'max:255'],
            'studios_contact' => ['nullable', 'string', 'max:65535'],
            'facebook_url' => ['nullable', 'string', 'max:255', 'url'],
            'twitter_url' => ['nullable', 'string', 'max:255', 'url'],
            'instagram_url' => ['nullable', 'string', 'max:255', 'url'],
            'youtube_url' => ['nullable', 'string', 'max:255', 'url'],
            'bluesky_url' => ['nullable', 'string', 'max:255', 'url'],
            'mastodon_url' => ['nullable', 'string', 'max:255', 'url'],
            'newsletter_url' => ['nullable', 'string', 'max:510', 'url'],
            'play_store_app_url' => ['nullable', 'string', 'max:255', 'url'],
            'app_store_app_url' => ['nullable', 'string', 'max:255', 'url'],
            'matomo_url' => ['nullable', 'url', 'max:255'],
            'matomo_id_site' => ['nullable', 'string', 'max:255'],
            'ga4_tracking_id' => ['nullable', 'string', 'max:255'],
            'login_modal_on_init' => ['required', 'boolean'],
            'map_events_week_interval' => ['required', 'integer', 'min:1', 'max:100'],
            'map_news_week_interval' => ['required', 'integer', 'min:1', 'max:100'],
            'map_playlists_week_interval' => ['required', 'integer', 'min:1', 'max:100'],
            'map_podcasts_week_interval' => ['required', 'integer', 'min:1', 'max:100'],
            'map_performers_week_interval' => ['required', 'integer', 'min:1', 'max:100'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'login_modal_on_init' => (bool) $this->login_modal_on_init,
        ]);
    }
}

<?php

namespace App\Http\Requests\Announcements;

use App\Models\Announcements\Announcement;
use App\Models\Map\Point;
use App\Models\Radio\RadioStation;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AnnouncementUpdateRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'title' => ['nullable', 'string', 'max:255', 'required_if:announceable_type,' . Point::class],
            'subtitle' => ['nullable', 'string', 'max:65535'],
            'description' => ['nullable', 'string', 'max:65535'],
            'radio_station_ids' => ['required', 'array'],
            'radio_station_ids.*' => ['required', Rule::exists(RadioStation::class, 'id')],
            'announceable_type' => ['required', Rule::in(array_keys(Announcement::ANNOUNCEABLES))],
            'announceable_id' => ['required', Rule::exists($this->announceable_type, 'id')],
            'published_at' => ['required', 'date'],
            'unpublished_at' => ['nullable', 'date'],
            'active' => ['required', 'boolean'],
            'url' => ['required_if:announceable_type,' . Point::class, 'nullable', 'url'],
        ];
    }

    /** @throws \JsonException */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'radio_station_ids' => json_decode($this->radio_station_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'active' => (bool) $this->active,
        ]);
    }
}

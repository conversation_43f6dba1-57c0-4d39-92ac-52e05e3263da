<?php

namespace App\Http\Requests\Api;

use App\Models\Audio\Podcast;
use Illuminate\Auth\Access\AuthorizationException;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class PodcastUpdateRequest extends FormRequest
{
    public function authorize(): bool
    {
        return File::exists(Storage::path('public/winmedia/podcasts/' . $this->get('podcast_id') . '.mp3'));
    }

    public function rules(): array
    {
        return ['podcast_id' => ['required', 'integer', Rule::exists(Podcast::class, 'id')]];
    }

    protected function failedAuthorization(): AuthorizationException
    {
        throw new AuthorizationException('Podcast file not found.');
    }
}

<?php

namespace App\Http\Requests\Api;

use App\Models\Map\PlaceType;
use App\Rules\UniqueCoordinates;
use App\Rules\UniqueNormalizedPlaceName;
use App\Rules\UniqueOsmId;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class PlaceStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'max:255',
                new UniqueNormalizedPlaceName(),
            ],
            'type' => [
                'required',
                Rule::enum(PlaceType::class),
                'max:255',
            ],
            'addr_street1' => ['nullable', 'string', 'max:255'],
            'addr_city' => ['nullable', 'string', 'max:255'],
            'addr_zip' => ['nullable', 'string', 'max:255'],
            'latitude' => [
                'required',
                'numeric',
                'between:-90,90',
                new UniqueCoordinates(),
            ],
            'longitude' => ['required', 'numeric', 'between:-180,180'],
            'osm_id' => [
                'required',
                'string',
                'max:255',
                new UniqueOsmId(),
            ],
            'source_name' => ['nullable', 'string', 'max:255'],
            'source_id' => ['nullable', 'string', 'max:255'],
            'informations' => ['nullable', 'json'],
            'description' => ['nullable', 'string', 'max:65535'],
            'url' => ['nullable', 'string', 'max:255'],
            'enabled' => ['boolean'],
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => 'name',
            'type' => 'type',
            'addr_street1' => 'addr_street',
            'addr_city' => 'addr_city',
            'addr_zip' => 'addr_zip',
            'latitude' => 'latitude',
            'longitude' => 'longitude',
            'osm_id' => 'osm_id',
            'informations' => 'informations',
            'description' => 'description',
            'url' => 'url',
            'enabled' => 'enabled',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'type' => $this->type ? $this->type : PlaceType::Other->value,
            'addr_street1' => $this->addr_street ?? null,
            'source_name' => $this->osm_id ? 'openstreetmap' : null,
            'source_id' => $this->osm_id ? $this->osm_id : null,
            'enabled' => true,
        ]);
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'error' => collect($validator->errors()->all())->implode(' '),
            'place' => null,
        ], 422));
    }
}

<?php

namespace App\Http\Requests\Api;

use App\Models\Audio\Thematic;
use App\Models\Events\Event;
use App\Models\Map\Place;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use App\Rules\TagsLength;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Validation\Rule;

class EventStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'cover' => array_merge(
                ['required'],
                app(Event::class)->getMediaValidationRules('cover')
            ),
            'thematic_id' => ['required', 'integer', Rule::exists(Thematic::class, 'id')],
            'user_id' => ['nullable', 'int', Rule::exists(User::class, 'id')],
            'radio_station_ids' => ['required', 'array'],
            'radio_station_ids.*' => ['required', Rule::exists(RadioStation::class, 'id')],
            'title' => ['required', 'string', 'max:255'],
            'description' => ['required', 'string', 'max:4294967295'],
            'tags' => ['required', 'array', 'min:1', new TagsLength(65535)],
            'started_at' => ['required', 'date'],
            'ended_at' => ['required', 'date', 'after_or_equal:started_at'],
            'localisation' => ['required', 'integer', Rule::exists(Place::class, 'id')],
            'active' => ['required', 'boolean'],
        ];
    }

    public function attributes(): array
    {
        return [
            'cover' => 'image',
            'thematic_id' => 'thematic',
            'user_id' => 'author',
            'radio_station_ids' => 'stations',
            'radio_station_ids.*' => 'stations.*',
            'title' => 'title',
            'description' => 'description',
            'tags' => 'tags',
            'started_at' => 'started_at',
            'ended_at' => 'ended_at',
            'active' => 'activation',
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'cover' => $this->image,
            'thematic_id' => $this->thematic,
            'user_id' => $this->author,
            'radio_station_ids' => json_decode($this->stations ?: '[]', true, 512),
            'tags' => json_decode($this->tags ?: '[]', true, 512),
            'active' => (bool) $this->activation,
        ]);
    }

    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException(response()->json([
            'success' => false,
            'error' => collect($validator->errors()->all())->implode(' '),
            'event' => null,
        ], 422));
    }
}

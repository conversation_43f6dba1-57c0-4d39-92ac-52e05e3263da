<?php

namespace App\Http\Requests\Api;

use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LiveBroadcastStoreRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'winmedia_radio_station_id' => [
                'required',
                'int',
                Rule::exists(RadioStation::class, 'winmedia_id'),
            ],
            'winmedia_song_id' => [
                'nullable',
                'int',
                Rule::exists(Song::class, 'imedia'),
            ],
            'song_id' => [
                'nullable',
                'int',
                Rule::exists(Song::class, 'id'),
            ],
            'podcast_id' => [
                'nullable',
                'int',
                Rule::exists(Podcast::class, 'id'),
            ],
            'dedication_user_id' => [
                'nullable',
                'int',
                Rule::exists(User::class, 'id'),
            ],
            'real_duration' => ['required', 'int', 'min:0'],
            'started_at' => ['required', 'string', 'date_format:Y-m-d H:i:s'],
        ];
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'song_id' => $this->winmedia_song_id
                ? Song::winmediaType()->where('imedia', $this->winmedia_song_id)->first()?->id
                : null,
        ]);
    }
}

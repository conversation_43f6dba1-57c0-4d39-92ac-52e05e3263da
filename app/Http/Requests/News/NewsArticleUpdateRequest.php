<?php

namespace App\Http\Requests\News;

use App\Http\Requests\Traits\HasLocationInfo;
use App\Http\Requests\Traits\HasSeoMeta;
use App\Models\Audio\Thematic;
use App\Models\News\NewsArticle;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use App\Rules\TagsLength;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class NewsArticleUpdateRequest extends FormRequest
{
    use HasLocationInfo, HasSeoMeta;

    public function rules(): array
    {
        return array_merge(
            [
                'illustration' => app(NewsArticle::class)->getMediaValidationRules('illustrations'),
                'audio' => [
                    app(NewsArticle::class)->getMediaMimesValidationRules('audio'),
                    app(NewsArticle::class)->getMediaMimeTypesValidationRules('audio'),
                    'max:' . 1024 * 1024 * 600,
                ],
                'remove_audio_file' => ['required', 'boolean'],
                'slug' => [
                    'required',
                    'string',
                    'slug',
                    'max:255',
                    Rule::unique(NewsArticle::class, 'slug')->ignore($this->article),
                ],
                'title' => [
                    'required',
                    'string',
                    'max:255',
                    Rule::unique(NewsArticle::class, 'title')->ignore($this->article),
                ],
                'tags' => ['required', 'array', 'min:1', new TagsLength(65535)],
                'description' => ['nullable', 'string', 'max:4294967295'],
                'audio_caption' => ['nullable', 'string', 'max:255'],
                'thematic_id' => ['nullable', 'int', Rule::exists(Thematic::class, 'id')],
                'user_id' => ['nullable', 'int', Rule::exists(User::class, 'id')],
                'radio_station_ids' => ['required', 'array'],
                'radio_station_ids.*' => ['required', Rule::exists(RadioStation::class, 'id')],
                'published_at' => ['required', 'date'],
                'active' => ['required', 'boolean'],
            ],
            $this->seoMetaRules(),
            $this->locationRules(),
        );
    }

    protected function prepareForValidation(): void
    {
        $this->merge([
            'tags' => collect(explode(',', $this->tags))->filter()->map(function (string $tag) {
                return trim(Str::replace('"', '', $tag));
            })->toArray(),
            'radio_station_ids' => json_decode($this->radio_station_ids ?: '[]', true, 512, JSON_THROW_ON_ERROR),
            'remove_audio_file' => (bool) $this->remove_audio_file,
            'active' => (bool) $this->active,
        ]);
        $this->prepareSeoMetaRules();
        $this->prepareLocationValidation();
    }

    public function attributes(): array
    {
        return $this->locationAttributes();
    }
}

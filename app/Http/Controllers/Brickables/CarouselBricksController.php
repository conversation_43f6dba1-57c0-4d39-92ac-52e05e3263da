<?php

namespace App\Http\Controllers\Brickables;

use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Okipa\LaravelBrickables\Models\Brick;

class CarouselBricksController extends BricksController
{
    /**
     * @throws \ErrorException
     * @throws \Exception
     */
    public function edit(Brick $brick, Request $request): View
    {
        $view = parent::edit($brick, $request);
        $view->with(compact('request'));

        return $view;
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    protected function sendBrickCreatedResponse(Request $request, Brick $brick): RedirectResponse
    {
        return redirect()->route('brick.edit', [
            'brick' => $brick,
            'admin_panel_url' => $request->admin_panel_url,
        ])->with('success', __('The entry :model > :brickable has been created.', [
            'brickable' => $brick->brickable->getLabel(),
            'model' => $brick->model->getReadableClassName(),
        ]));
    }
}

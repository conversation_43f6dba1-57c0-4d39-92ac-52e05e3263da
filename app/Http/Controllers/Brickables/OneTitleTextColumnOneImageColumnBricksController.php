<?php

namespace App\Http\Controllers\Brickables;

use Illuminate\Http\Request;
use Okipa\LaravelBrickables\Models\Brick;

class OneTitleTextColumnOneImageColumnBricksController extends BricksController
{
    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    protected function stored(Request $request, Brick $brick): void
    {
        /** @var \App\Models\Brickables\OneTitleTextColumnOneImageColumnBrick $brick */
        $brick->addMediaFromRequest('image_right')->toMediaCollection('images');
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    protected function updated(Request $request, Brick $brick): void
    {
        if ($request->file('image_right')) {
            /** @var \App\Models\Brickables\OneTitleTextColumnOneImageColumnBrick $brick */
            $brick->addMediaFromRequest('image_right')->toMediaCollection('images');
        }
    }
}

<?php

namespace App\Http\Controllers\Brickables;

use App\Http\Controllers\Controller;
use App\Http\Requests\Brickables\Carousel\CarouselSlideStoreRequest;
use App\Http\Requests\Brickables\Carousel\CarouselSlideUpdateRequest;
use App\Models\Brickables\CarouselBrick;
use App\Models\Brickables\CarouselBrickSlide;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class CarouselBrickSlidesController extends Controller
{
    public function create(CarouselBrick $brick): View
    {
        $slide = null;
        SEOTools::setTitle(__('breadcrumbs.parent.create', [
            'parent' => $brick->model->getReadableClassName() . ' > ' . __('Content bricks') . ' > ' . __('Carousel'),
            'entity' => __('Slides'),
        ]));

        return view('vendor.laravel-brickables.brickables.carousel.slides.edit', compact('brick', 'slide'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function store(CarouselSlideStoreRequest $request, CarouselBrick $brick): RedirectResponse
    {
        $slide = CarouselBrickSlide::create(array_merge(
            $request->validated(),
            ['brick_id' => $brick->id]
        ));
        $slide->addMediaFromRequest('image')->toMediaCollection('images');

        return redirect()->route('brick.edit', [
            'brick' => $brick,
            'admin_panel_url' => $request->admin_panel_url,
        ])->with('toast_success', __('crud.parent.created', [
            'parent' => $brick->model->getReadableClassName() . ' > ' . __('Content bricks') . ' > ' . __('Carousel'),
            'entity' => __('Slides'),
            'name' => $slide->label,
        ]));
    }

    public function edit(CarouselBrickSlide $slide): View
    {
        $brick = $slide->brick;
        SEOTools::setTitle(__('breadcrumbs.parent.edit', [
            'parent' => $brick->model->getReadableClassName() . ' > ' . __('Content bricks') . ' > ' . __('Carousel'),
            'entity' => __('Slides'),
            'detail' => $slide->label,
        ]));

        return view('vendor.laravel-brickables.brickables.carousel.slides.edit', compact('brick', 'slide'));
    }

    public function update(CarouselSlideUpdateRequest $request, CarouselBrickSlide $slide): RedirectResponse
    {
        $slide->update($request->validated());
        if ($request->file('image')) {
            $slide->addMediaFromRequest('image')->toMediaCollection('images');
        }

        return back()->with('toast_success', __('crud.parent.updated', [
            'parent' => $slide->brick->model->getReadableClassName() . ' > ' . __('Content bricks') . ' > '
                . __('Carousel'),
            'entity' => __('Slides'),
            'name' => $slide->label,
        ]));
    }

    public function destroy(CarouselBrickSlide $slide): RedirectResponse
    {
        $slide->delete();
        $orderedIds = CarouselBrickSlide::where('brick_id', $slide->brick->id)
            ->ordered()
            ->pluck('id');
        CarouselBrickSlide::setNewOrder($orderedIds);

        return back()->with('toast_success', __('crud.parent.destroyed', [
            'parent' => $slide->brick->model->getReadableClassName() . ' > ' . __('Carousel'),
            'entity' => __('Slides'),
            'name' => $slide->label,
        ]));
    }
}

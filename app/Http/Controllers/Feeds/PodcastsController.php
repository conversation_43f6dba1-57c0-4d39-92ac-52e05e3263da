<?php

namespace App\Http\Controllers\Feeds;

use App\Http\Controllers\Controller;
use App\Models\Audio\Podcast;
use App\Models\Radio\Program;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;

class PodcastsController extends Controller
{
    public function index(?Program $program = null): Response
    {
        $tags = 'SUN,radio,lesonunique,Son,Unique,Nantes,Cholet,Saint-Nazaire,Roche-sur-Yon,Angers';
        $programCover = null;
        if ($program) {
            $tags .= ',' . $program->thematic->title;
            $tags .= ',' . $program->tags;
            $programCover = $this->encodeFullUrl($program->getFirstMediaUrl('cover', 'itunes'));
        }
        $podcasts = Podcast::with(['media', 'program', 'thematic'])
            ->where('published_at', '<=', now())
            ->where('active', true)
            ->when(
                $program,
                fn (Builder $podcastsQuery) => $podcastsQuery->where('program_id', $program->id)
            )
            ->orderBy('published_at', 'desc')
            ->get()
            ->filter(fn (Podcast $podcast) => $podcast->audio_stream['mp3'] ? true : false) /** @phpstan-ignore-line */
            ->slice(0, 50)
            ->map(function (Podcast $podcast) {
                $cover = $podcast->getFirstMedia('cover');
                $isWinmediaFile = File::exists(Storage::path('public/winmedia/podcasts/' . $podcast->id . '.mp3'))
                    && $podcast->winmedia_audio_source_uploaded;

                return [
                    'title' => $podcast->title,
                    'description' => strip_tags(html_entity_decode($podcast->description, ENT_QUOTES)),
                    'cover_url' => $cover ? $this->encodeFullUrl($cover->getFullUrl()) : null,
                    'published_at' => $podcast->published_at->tz('Europe/Paris')->format('D, d M Y H:i:s O'),
                    'duration' => $podcast->duration,
                    'audio' => [
                        'url' => $this->encodeFullUrl($podcast->audio_stream['mp3']),
                        'type' => $isWinmediaFile
                            ? Storage::disk('public')->mimeType('winmedia/podcasts/' . $podcast->id . '.mp3')
                            : $podcast->getFirstMedia('audio')->mime_type,
                        'length' => $isWinmediaFile
                            ? Storage::disk('public')->size('winmedia/podcasts/' . $podcast->id . '.mp3')
                            : $podcast->getFirstMedia('audio')->size,
                    ],
                    'link' => route('app.podcast.show', $podcast),
                ];
            });

        return response()->view('feeds.podcasts', compact('podcasts', 'program', 'tags', 'programCover'))->header('Content-Type', 'application/xml');
    }

    public function encodeFullUrl($url): string
    {
        $parsedUrl = parse_url($url);

        if (! array_key_exists('scheme', $parsedUrl) || ! array_key_exists('host', $parsedUrl)) {
            return $url;
        } else {
            return $parsedUrl['scheme'] . '://' . $parsedUrl['host'] . str_replace('%2F', '/', urlencode($parsedUrl['path']));
        }
    }
}

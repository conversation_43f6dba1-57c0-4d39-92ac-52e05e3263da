<?php

namespace App\Http\Controllers\Traits;

use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use Illuminate\Foundation\Http\FormRequest;
use LogicException;
use MatanYadaev\EloquentSpatial\Objects\Point as EloquentSpatialPoint;

trait ContentLocationTrait
{
    private function storeLocationFromRequest(FormRequest $request, $content, string $namePrefix = 'place'): void
    {
        $data = $request->safe()->only($namePrefix)[$namePrefix]
            ?? throw new LogicException("No location data under the '$namePrefix' key.");

        $place = null;

        if (! $data['create_new_place']) {
            if (! \array_key_exists('place_id', $data)) {
                throw new LogicException("Key 'place_id' is missing.");
            }
            if ($data['place_id']) {
                $place = Place::findOrFail($data['place_id']);
            }
        } else {
            $place = Place::create($data);
            $place->points()->create([
                'coord' => new EloquentSpatialPoint(
                    $data['latitude'],
                    $data['longitude'],
                ),
            ]);
        }

        if ($place) {
            $contentLocation = new ContentLocation();
            $contentLocation->content()->associate($content);
            $contentLocation->location()->associate($place);
            $contentLocation->save();
        }
    }

    private function updateLocationFromRequest(FormRequest $request, $content, string $namePrefix = 'place'): void
    {
        $data = $request->safe()->only($namePrefix)[$namePrefix]
            ?? throw new LogicException("No location data under the '$namePrefix' key.");

        $newPlace = null;
        $currentPlace = $content->place();

        if (! $data['create_new_place']) {
            if (! \array_key_exists('place_id', $data)) {
                throw new LogicException("Key 'place_id' is missing.");
            }
            if ($data['place_id']) {
                if ($data['place_id'] === $currentPlace?->id) {
                    return;
                }

                $newPlace = Place::findOrFail($data['place_id']);
            }
        } else {
            $newPlace = Place::create($data);
            $newPlace->points()->create([
                'coord' => new EloquentSpatialPoint(
                    $data['latitude'],
                    $data['longitude'],
                ),
            ]);
        }

        if ($currentPlace) {
            $content->contentLocations
                ->first(function (ContentLocation $item) use ($currentPlace): bool {
                    return $item->location_type === Place::class
                        && $item->location_id === $currentPlace->id;
                })
                ->delete();
        }

        if ($newPlace) {
            $contentLocation = new ContentLocation();
            $contentLocation->content()->associate($content);
            $contentLocation->location()->associate($newPlace);
            $contentLocation->save();
        }

        $content->load('contentLocations.location');
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\PodcastUpdateRequest;
use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\PodcastIndexService;
use Illuminate\Http\JsonResponse;
use Owenoj\LaravelGetId3\GetId3;

class PodcastsController extends Controller
{
    /** @throws \Exception */
    public function update(PodcastUpdateRequest $request): JsonResponse
    {
        $podcast = Podcast::findOrFail($request->get('podcast_id'));
        $playlistIds = $request->get('playlist')
            ? json_decode($request->get('playlist'), false, 512, JSON_THROW_ON_ERROR)
            : [];
        $songIdsWithPivot = collect($playlistIds)
            ->filter(fn (int $imedia) => Song::where('imedia', $imedia)->exists())
            ->mapWithKeys(function (int $imedia, int $index) {
                $songId = Song::where('imedia', $imedia)->pluck('id')->first();

                return [$songId => ['index' => $index]];
            })
            ->toArray();
        $podcast->songs()->sync($songIdsWithPivot);
        $audioFileID3 = GetId3::fromDiskAndPath(
            'local',
            '/public/winmedia/podcasts/' . $request->get('podcast_id') . '.mp3'
        );
        $podcast->update([
            'winmedia_audio_source_uploaded' => true,
            'duration' => (int) $audioFileID3->getPlaytimeSeconds() ?: $podcast->program()->get()[0]->duration,
        ]);

        if ($podcast->active) {
            app(PodcastIndexService::class)->updateOrCreate($podcast);
        } else {
            app(PodcastIndexService::class)->delete($podcast);
        }
        app(CacheService::class)->clearBrowsePodcastsCache();

        return response()->json(['success' => true]);
    }
}

<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Audio\Thematic;
use App\Models\Events\Event;
use App\Models\Map\Place;
use App\Models\Map\PlaceType;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Normalizer;

class AgendaController extends Controller
{
    /** @throws \Exception */
    public function thematics(): JsonResponse
    {
        $thematics = Thematic::select('id', 'title')
            ->get()
            ->map(fn (Thematic $thematic) => [
                'id' => $thematic->id,
                'title' => $thematic->title,
            ]);

        return response()->json($thematics);
    }

    /** @throws \Exception */
    public function stations(): JsonResponse
    {
        $stations = RadioStation::with(['contentLocations', 'media'])
            ->where('active', true)
            ->ordered()
            ->get()
            ->map(fn (RadioStation $station) => [
                'id' => $station->id,
                'name' => $station->name,
                'label' => $station->label,
                'color' => $station->color,
                'position' => [
                    'latitude' => $station->point()?->coord?->latitude,
                    'longitude' => $station->point()?->coord?->longitude,
                ],
            ]);

        return response()->json($stations);
    }

    public function users(): JsonResponse
    {
        $users = User::with('team')
            ->select('id', 'username')
            ->whereHas('team', function ($query) {
                $query->whereIn('unique_key', ['volunteer', 'editor', 'admin', 'employee']);
            })
            ->orderBy('username')
            ->get()
            ->map(fn (User $user) => [
                'id' => $user->id,
                'username' => $user->username,
            ]);

        return response()->json($users);
    }

    /** @throws \Exception */
    public function places(Request $request): JsonResponse
    {
        $searchName = $request->get('name');

        if (empty($searchName)) {
            return response()->json([]);
        }

        $normalizedSearchName = Normalizer::normalize($searchName, Normalizer::FORM_C);

        $places = Place::with('currentPoint')
            ->when(config('database.default') === 'pgsql', function ($query) use ($normalizedSearchName) {
                return $query->whereRaw('LOWER(UNACCENT(name)) LIKE LOWER(UNACCENT(?))', ["%{$normalizedSearchName}%"]);
            }, function ($query) use ($normalizedSearchName) {
                return $query->whereRaw('LOWER(name) LIKE LOWER(?)', ["%{$normalizedSearchName}%"]);
            })
            ->orderBy('name')
            ->limit(100)
            ->get()
            ->map(fn (Place $place) => [
                'id' => $place->id,
                'name' => $place->name,
                'type' => $place->type,
                'addr_street' => $place->addr_street1,
                'addr_city' => $place->addr_city,
                'addr_zip' => $place->addr_zip,
                'position' => [
                    'latitude' => $place->currentPoint?->coord?->latitude,
                    'longitude' => $place->currentPoint?->coord?->longitude,
                ],
                'source_id' => $place->source_id,
                'source_name' => $place->source_name,
            ]);

        return response()->json($places);
    }

    /** @throws \Exception */
    public function placeTypes(): JsonResponse
    {
        $placeTypes = collect(PlaceType::cases())
            ->map(fn (PlaceType $placeType) => [
                'id' => $placeType->value,
                'name' => PlaceType::list()[$placeType->value],
            ]);

        return response()->json($placeTypes);
    }

    /** @throws \Exception */
    public function events(Request $request): JsonResponse
    {
        $page = $request->get('page', 1);

        $events = Event::with(['contentLocations', 'media', 'radioStations'])
            ->where('active', true)
            ->orderBy('created_at', 'DESC')
            ->offset(($page - 1) * 100)
            ->limit(100)
            ->get()
            ->map(fn (Event $event) => [
                'id' => $event->id,
                'title' => $event->title,
                'description' => $event->description,
                'tags' => $event->tags,
                'thematic' => $event->thematic_id,
                'author' => $event->user_id,
                'started_at' => $event->started_at,
                'ended_at' => $event->ended_at,
                'created_at' => $event->created_at,
                'stations' => $event->radioStations->pluck('id'),
                'localisation' => [
                    'id' => $event->location()->id,
                    'name' => $event->locationName(),
                    'latitude' => $event->point()->getLatitude(),
                    'longitude' => $event->point()->getLongitude(),
                ],
                'cover_source' => $event->getFirstMediaUrl('cover'),
                'cover_card' => $event->getFirstMediaUrl('cover', 'agenda_card'),
            ]);

        return response()->json($events);
    }
}

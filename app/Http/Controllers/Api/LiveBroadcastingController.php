<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\LiveBroadcastStoreRequest;
use App\Models\Radio\LiveBroadcasting;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Date;

class LiveBroadcastingController extends Controller
{
    /** @throws \Exception */
    public function store(LiveBroadcastStoreRequest $request): JsonResponse
    {
        LiveBroadcasting::create($request->validated())->toArray();
        LiveBroadcasting::where('started_at', '<', Date::now()->subMonth())->delete();
        lastRadioStationsLiveBroadcasts(true);

        return response()->json(['success' => true]);
    }
}

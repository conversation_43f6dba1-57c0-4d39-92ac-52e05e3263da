<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Audio\Podcast;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Date;

class ProgramsController extends Controller
{
    /** @throws \Exception */
    public function index(): JsonResponse
    {
        $podcasts = Podcast::with(['program', 'radioStations'])
            ->where('type', Podcast::TYPE_REPLAY)
            ->whereBetween('published_at', [Date::now()->startOfDay(), Date::now()->addWeek()->endOfDay()])
            ->orderBy('published_at')
            ->get()
            ->map(fn (Podcast $podcast) => [
                'id' => $podcast->id,
                'title' => $podcast->title,
                'duration' => $podcast->duration,
                'description' => strip_tags(html_entity_decode($podcast->description, ENT_QUOTES)),
                'program_title' => $podcast->program->title,
                'published_at' => $podcast->published_at->toW3CString(),
                'radio_stations' => $podcast->radioStations->pluck('winmedia_id')->toArray(),
                'start_podcast_title' => $podcast->program->start_podcast_title,
                'start_podcast_artist' => $podcast->program->start_podcast_artist,
                'start_podcast_version' => $podcast->program->start_podcast_version,
                'end_podcast_title' => $podcast->program->end_podcast_title,
                'end_podcast_artist' => $podcast->program->end_podcast_artist,
                'end_podcast_version' => $podcast->program->end_podcast_version,
                'cover' => $podcast->getFirstMediaUrl('cover'),
                'cover_thumb' => $podcast->getFirstMediaUrl('cover', 'player_mobile'),
                'theorical_timing_live_display' => (bool) $podcast->program->theorical_timing_live_display,
            ]);

        return response()->json(['success' => true, 'data' => $podcasts]);
    }
}

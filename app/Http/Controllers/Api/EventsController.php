<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\EventStoreRequest;
use App\Models\Events\Event;
use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\EventIndexService;
use Illuminate\Http\JsonResponse;

class EventsController extends Controller
{
    public function store(EventStoreRequest $request): JsonResponse
    {
        $request->validated();

        $event = Event::create($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
        ])->toArray());

        $event->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));

        $event->addMediaFromRequest('image')->toMediaCollection('cover');

        $place = Place::find($request->safe()->offsetGet('localisation'));

        if ($place) {
            $contentLocation = new ContentLocation();
            $contentLocation->content()->associate($event);
            $contentLocation->location()->associate($place);
            $contentLocation->save();
            $event->load('contentLocations.location');
        }

        $event->active
            ? app(EventIndexService::class)->updateOrCreate($event)
            : app(EventIndexService::class)->delete($event);
        app(CacheService::class)->clearBrowseEventsCache();

        return response()->json([
            'success' => true,
            'error' => null,
            'event' => $event->id,
        ]);
    }
}

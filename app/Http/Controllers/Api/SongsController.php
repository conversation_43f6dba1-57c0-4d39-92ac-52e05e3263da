<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\Elasticsearch\CustomSearchService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SongsController extends Controller
{
    public function search(Request $request): JsonResponse
    {
        $songs = app(CustomSearchService::class)->searchSongs(
            $request->get('q', '') ?? '',
            14,
            0,
            null,
            true
        )['hits']['hits'];

        return response()->json([
            'success' => true,
            'data' => array_map(static fn (array $song) => $song['_source'], $songs),
        ]);
    }
}

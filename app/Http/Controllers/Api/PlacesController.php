<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\PlaceStoreRequest;
use App\Models\Map\Place;
use Illuminate\Http\JsonResponse;
use MatanYadaev\EloquentSpatial\Objects\Point as EloquentSpatialPoint;

class PlacesController extends Controller
{
    public function store(PlaceStoreRequest $request): JsonResponse
    {
        $validated = $request->validated();

        $place = Place::create($validated);

        $place->points()->create([
            'coord' => new EloquentSpatialPoint($validated['latitude'], $validated['longitude']),
        ]);

        return response()->json([
            'success' => true,
            'error' => null,
            'place' => $place->id,
        ]);
    }
}

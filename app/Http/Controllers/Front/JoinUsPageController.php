<?php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Models\PageContents\PageContent;
use Illuminate\Contracts\View\View;

class JoinUsPageController extends Controller
{
    /** @throws \Exception */
    public function show(): view
    {
        /** @var \App\Models\PageContents\PageContent $pageContent */
        $pageContent = PageContent::where('unique_key', 'join_us_page_content')->sole();
        $pageContent->displaySeoMeta();
        $css = mix('/css/templates/front/web/contact/page/show.css');
        $js = mix('/js/templates/front/web/contact/page/show.js');

        return view('templates.front.join-us.page.show', compact('pageContent', 'css', 'js'));
    }
}

<?php

namespace App\Http\Controllers\Front;

use App\Http\Controllers\Controller;
use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\News\NewsArticle;
use App\Models\Performers\Album;
use App\Models\Radio\Program;
use App\Services\Map\MapConfiguration;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;

/**
 * Voir le middleware App\Http\Middleware\PrepareIframeContext qui intervient
 * dans le processus du partage par iframe (spécialement pour la carte).
 */
class IframeController extends Controller
{
    public function showIframe(Request $request)
    {
        $shareType = $request->shareType;
        $selectedItem = null;
        $footerInfos = null;
        $songs = null;
        $themeMode = $this->getMode();
        $data = [];

        switch ($shareType) {
            case 'album':
                $footerInfos['href'] = route('app.album.show', $request->itemId);
                $footerInfos['txtLink'] = 'Ecoutez cet album en vous connectant sur la plateforme mySUN';
                $footerInfos['txt'] = 'SUN, radio régionale, musicale et innovante';

                $selectedItem = Album::findOrFail($request->itemId);
                $songs = $selectedItem->songs;
                break;

            case 'emission':
                $footerInfos['href'] = route('app.program.show', $request->itemId);
                $footerInfos['txtLink'] = 'Découvrez nos podcasts et replays sur la plateforme mySUN';
                $footerInfos['txt'] = 'SUN, radio régionale, musicale et innovante';

                $selectedItem = Program::findOrFail($request->itemId);

                $podcasts = $selectedItem
                    ->podcasts()
                    ->with(['media', 'program'])
                    ->where('published_at', '<=', Date::now())
                    ->where('active', true)
                    ->hasAudio()
                    ->orderBy('published_at', 'DESC')
                    ->get();
                $selectedItem->episodes = $podcasts; /** @phpstan-ignore-line */
                //                $cover_thumb = $selectedItem->setCoversPatchworkHeaderBackgroundData();
                break;

            case 'playlist':
                $footerInfos['href'] = route('app.playlist.show', $request->itemId);
                $footerInfos['txtLink'] = 'Ecoutez cette playlist en vous connectant sur la plateforme mySUN';
                $footerInfos['txt'] = 'SUN, radio régionale, musicale et innovante';

                $selectedItem = Playlist::with(['media', 'thematic', 'songs'])
                    ->findOrFail($request->itemId);
                $songs = $selectedItem->songs;

                //                $coverThumb = $selectedItem->getFirstMediaUrl('cover', 'medium');
                //                if (empty($coverThumb)) {
                //                    dd($songs->first()->cover_thumb);
                //                } else {
                //                    dd($coverThumb);
                //                }
                break;

            case 'podcast':
                $footerInfos['href'] = route('app.podcast.show', $request->itemId);
                $footerInfos['txtLink'] = 'Découvrez nos podcasts et replays sur la plateforme mySUN';
                $footerInfos['txt'] = 'SUN, radio régionale, musicale et innovante';

                $selectedItem = Podcast::with(['media', 'program'])
                    ->findOrFail($request->itemId);
                break;

            case 'song':
                $footerInfos['href'] = route('app.browse.index');
                $footerInfos['txtLink'] = 'Ecoutez ce morceau en vous connectant sur la plateforme mySUN';
                $footerInfos['txt'] = 'SUN, radio régionale, musicale et innovante';

                $selectedItem = Song::with(['performerRelationship', 'albumRelationship'])
                    ->findOrFail($request->itemId)->append('audioStream');
                break;

            case 'article':
                $footerInfos['href'] = route('app.news.show', $request->itemId);
                $footerInfos['txtLink'] = 'Découvrez nos actualités sur la plateforme mySUN';
                $footerInfos['txt'] = 'SUN, radio régionale, musicale et innovante';

                $selectedItem = NewsArticle::findOrFail($request->itemId);
                break;

            case 'map':
                $data['authenticated'] = false;
                $data['config'] = MapConfiguration::fromArray($request->query->all());
                break;

            default:
                abort(404);
        }

        $data += compact('themeMode', 'footerInfos', 'shareType', 'selectedItem', 'songs');

        return view("iframes/$shareType", $data);
    }

    private function getMode()
    {
        if (Auth::user()) {
            return Auth::user()->settings->dark_mode_enum->value;
        }

        return '';
    }
}

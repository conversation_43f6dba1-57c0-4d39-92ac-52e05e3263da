<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Holidays\HolidaysStoreRequest;
use App\Http\Requests\Holidays\HolidaysUpdateRequest;
use App\Models\Holidays\Holidays;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class HolidaysController extends Controller
{
    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Vacances']));

        return view('templates.admin.holidays.index');
    }

    public function create(): View
    {
        $holidays = null;
        SEOTools::setTitle(__('breadcrumbs.parent.create', [
            'parent' => 'Vacances',
            'entity' => 'Holidays',
        ]));

        return view('templates.admin.holidays.edit', compact('holidays'));
    }

    /** @throws \Exception */
    public function store(HolidaysStoreRequest $request): RedirectResponse
    {
        $holidays = Holidays::create($request->validated());
        $holidays->programs()->sync($request->programs);

        return redirect()->route('holidays.index')
            ->with('toast_success', __('crud.parent.created', [
                'parent' => 'Vacances',
                'entity' => 'Holidays',
                'name' => $holidays->label,
            ]));
    }

    public function edit(Holidays $holidays): View
    {
        SEOTools::setTitle(__('breadcrumbs.parent.edit', [
            'parent' => 'Vacances',
            'entity' => 'Holidays',
            'name' => $holidays->label,
        ]));

        return view('templates.admin.holidays.edit', compact('holidays'));
    }

    public function update(HolidaysUpdateRequest $request, Holidays $holidays): RedirectResponse
    {
        $holidays->update($request->validated());
        $holidays->programs()->sync($request->programs);

        return redirect()->route('holidays.edit', $holidays)
            ->with('toast_success', __('crud.parent.updated', [
                'parent' => 'Vacances',
                'entity' => 'Holidays',
                'name' => $holidays->label,
            ]));
    }

    /** @throws \Exception */
    public function destroy(Holidays $holidays): RedirectResponse
    {
        $holidays->delete();

        return back()->with('toast_success', __('crud.parent.destroyed', [
            'parent' => 'Vacances',
            'entity' => 'Holidays',
            'name' => $holidays->label,
        ]));
    }
}

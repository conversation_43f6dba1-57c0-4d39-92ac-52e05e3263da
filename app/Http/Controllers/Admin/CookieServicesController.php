<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Cookies\CookieServicesIndexRequest;
use App\Http\Requests\Cookies\CookieServiceStoreRequest;
use App\Http\Requests\Cookies\CookieServiceUpdateRequest;
use App\Models\Cookies\CookieService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Arr;

class CookieServicesController extends Controller
{
    /** @throws \ErrorException */
    public function index(CookieServicesIndexRequest $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.parent.index', [
            'parent' => 'Cookies',
            'entity' => 'Services',
        ]));

        return view('templates.admin.cookies.services.index', compact('request'));
    }

    public function create(): View
    {
        $cookieService = null;
        SEOTools::setTitle(__('breadcrumbs.parent.create', [
            'parent' => 'Cookies',
            'entity' => 'Services',
        ]));

        return view('templates.admin.cookies.services.edit', compact('cookieService'));
    }

    /** @throws \Exception */
    public function store(CookieServiceStoreRequest $request): RedirectResponse
    {
        $cookies = Arr::first($request->safe()->only('cookies'));
        $cookieService = CookieService::create($cookies
            ? $request->safe()->merge(['cookies' => json_decode($cookies, true, 512, JSON_THROW_ON_ERROR)])->toArray()
            : $request->validated());
        $cookieService->categories()->sync($request->category_ids);
        cookieCategories(true);

        return redirect()->route('cookie.services.index')
            ->with('toast_success', __('crud.parent.created', [
                'parent' => 'Cookies',
                'entity' => 'Services',
                'name' => $cookieService->title,
            ]));
    }

    public function edit(CookieService $cookieService): View
    {
        SEOTools::setTitle(__('breadcrumbs.parent.edit', [
            'parent' => 'Cookies',
            'entity' => 'Services',
            'detail' => $cookieService->title,
        ]));

        return view('templates.admin.cookies.services.edit', compact('cookieService'));
    }

    /** @throws \Exception */
    public function update(CookieServiceUpdateRequest $request, CookieService $cookieService): RedirectResponse
    {
        $cookies = Arr::first($request->safe()->only('cookies'));
        $cookieService->update($cookies
            ? $request->safe()->merge(['cookies' => json_decode($cookies, true, 512, JSON_THROW_ON_ERROR)])->toArray()
            : $request->validated());
        $cookieService->categories()->sync($request->category_ids);
        cookieCategories(true);

        return back()->with('toast_success', __('crud.parent.updated', [
            'parent' => 'Cookies',
            'entity' => 'Services',
            'name' => $cookieService->title,
        ]));
    }

    /** @throws \Exception */
    public function destroy(CookieService $cookieService): RedirectResponse
    {
        $cookieService->delete();
        cookieCategories(true);

        return back()->with('toast_success', __('crud.parent.destroyed', [
            'parent' => 'Cookies',
            'entity' => 'Services',
            'name' => $cookieService->title,
        ]));
    }
}

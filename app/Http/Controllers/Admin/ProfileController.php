<?php

namespace App\Http\Controllers\Admin;

use App\Providers\RouteServiceProvider;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\ValidationException;

class ProfileController
{
    public function show(): View
    {
        $user = auth()->user();
        SEOTools::setTitle('Profil');

        return view('templates.admin.users.profile', compact('user'));
    }

    /** @throws \Exception */
    public function deleteAccount(Request $request): RedirectResponse
    {
        if (! Hash::check($request->password, Auth::user()->password)) {
            throw ValidationException::withMessages([
                'password' => [__('This password does not match our records.')],
            ])->errorBag('delete_account');
        }
        $user = Auth::user();
        Auth::logout();
        $user->delete();

        return redirect(RouteServiceProvider::HOME)->with('success', __('Your account has been deleted.'));
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

class JoinUsFormLogsController extends Controller
{
    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Logs messages formulaire Nous rejoindre']));

        return view('templates.admin.join-us-form-logs.index');
    }

    public function downloadAttachment(Media $attachment): BinaryFileResponse
    {
        return response()->download(
            $attachment->getPath(),
            $attachment->file_name,
            ['Content-Type' => $attachment->mime_type]
        );
    }
}

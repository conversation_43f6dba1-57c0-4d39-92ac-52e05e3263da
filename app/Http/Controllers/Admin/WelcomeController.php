<?php

namespace App\Http\Controllers\Admin;

use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Auth\Events\Verified;
use Illuminate\Contracts\View\View;
use Illuminate\Foundation\Auth\User;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\Password;
use Symfony\Component\HttpFoundation\Response;

class WelcomeController extends \Spatie\WelcomeNotification\WelcomeController
{
    public function showWelcomeForm(Request $request, User $user): View
    {
        SEOTools::setTitle(__('Welcome'));

        return parent::showWelcomeForm($request, $user);
    }

    public function savePassword(Request $request, User $user): Response
    {
        $response = parent::savePassword($request, $user);
        if (! $request->user()->hasVerifiedEmail() && $request->user()->markEmailAsVerified()) {
            event(new Verified($request->user()));
        }

        return $response;
    }

    public function sendPasswordSavedResponse(): Response
    {
        alert()->html('Succès', __('Your new password has been saved.'), 'success');

        if (auth()->user()?->team_id === 5) {
            return redirect()->route('app.index');
        } else {
            return redirect()->route('admin.index');
        }
    }

    protected function rules(): array
    {
        return ['password' => ['required', Password::defaults()]];
    }
}

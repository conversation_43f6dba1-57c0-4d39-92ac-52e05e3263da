<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Users\UserStoreRequest;
use App\Http\Requests\Users\UserUpdateRequest;
use App\Models\Users\User;
use App\Services\Users\UsersService;
use Artesaos\SEOTools\Facades\SEOTools;
use Hash;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;

class UsersController extends Controller
{
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Utilisateurs']));

        return view('templates.admin.users.index');
    }

    public function create(): View
    {
        $user = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Utilisateurs']));

        return view('templates.admin.users.edit', compact('user'));
    }

    public function store(UserStoreRequest $request): RedirectResponse
    {
        $password = Hash::make(Arr::first($request->safe()->only('password')) ?: Str::random(8));
        $user = User::create($request->safe()->merge(compact('password'))->toArray());
        app(UsersService::class)->saveProfilePictureFromRequest($request, $user, false);
        $additionalMessage = '';
        if (! $request->password) {
            $user->sendWelcomeNotification(Date::now()->addDays(14));
            $additionalMessage = ' ' . __('A password creation link has been sent.');
        }

        return redirect()->route('users.index')->with('toast_success', __('crud.orphan.created', [
            'entity' => 'Utilisateurs',
            'name' => $user->username,
        ]) . $additionalMessage);
    }

    public function edit(User $user): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', ['entity' => 'Utilisateurs', 'detail' => $user->username]));

        return view('templates.admin.users.edit', compact('user'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function update(User $user, UserUpdateRequest $request): RedirectResponse
    {
        $newPassword = Arr::first($request->safe()->only('new_password'));
        $user->update($newPassword
            ? $request->safe()->merge(['password' => Hash::make($newPassword)])->toArray()
            : $request->safe()->except('new_password'));
        app(UsersService::class)->saveProfilePictureFromRequest($request, $user, true);

        return back()->with('toast_success', __('crud.orphan.updated', [
            'entity' => 'Utilisateurs',
            'name' => $user->username,
        ]));
    }

    /** @throws \Exception */
    public function destroy(User $user): RedirectResponse
    {
        $user->delete();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Utilisateurs',
            'name' => $user->username,
        ]));
    }

    /** @throws \Exception */
    public function sendWelcome(User $user): RedirectResponse
    {
        $user->sendWelcomeNotification(Date::now()->addDays(14));

        return back()->with('toast_success', __('A password creation link has been sent.'));
    }
}

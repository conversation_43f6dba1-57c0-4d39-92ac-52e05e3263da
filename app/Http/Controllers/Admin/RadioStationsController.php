<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\ContentLocationTrait;
use App\Http\Requests\RadioStations\RadioStationStoreRequest;
use App\Http\Requests\RadioStations\RadioStationUpdateRequest;
use App\Models\Radio\RadioStation;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class RadioStationsController extends Controller
{
    use ContentLocationTrait;

    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', [
            'entity' => 'Stations de radio',
        ]));

        return view('templates.admin.radio-stations.index');
    }

    public function create(): View
    {
        $radioStation = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', [
            'entity' => 'Stations de radio',
        ]));

        return view('templates.admin.radio-stations.edit', compact('radioStation'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function store(RadioStationStoreRequest $request): RedirectResponse
    {
        /** @var \App\Models\Radio\RadioStation $radioStation */
        $radioStation = RadioStation::create($request->validated());
        $radioStation->addMediaFromRequest('cover')->toMediaCollection('cover');
        $this->storeLocationFromRequest($request, $radioStation);
        radioStations(true);
        lastRadioStationsLiveBroadcasts(true);

        return redirect()->route('radio-stations.index')
            ->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Stations de radio',
                'name' => $radioStation->name,
            ]));
    }

    public function edit(RadioStation $radioStation): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Stations de radio',
            'detail' => $radioStation->name,
        ]));

        return view('templates.admin.radio-stations.edit', compact('radioStation'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function update(RadioStationUpdateRequest $request, RadioStation $radioStation): RedirectResponse
    {
        $radioStation->update($request->validated());
        if ($request->file('cover')) {
            $radioStation->addMediaFromRequest('cover')->toMediaCollection('cover');
        }
        $this->updateLocationFromRequest($request, $radioStation);
        radioStations(true);
        lastRadioStationsLiveBroadcasts(true);

        return redirect()->route('radio-station.edit', $radioStation)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Stations de radio',
                'name' => $radioStation->name,
            ]));
    }

    /** @throws \Exception */
    public function destroy(RadioStation $radioStation): RedirectResponse
    {
        $radioStation->delete();
        radioStations(true);
        lastRadioStationsLiveBroadcasts(true);

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Stations de radio',
            'name' => $radioStation->name,
        ]));
    }
}

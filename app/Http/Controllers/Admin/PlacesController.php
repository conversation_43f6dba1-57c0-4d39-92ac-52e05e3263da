<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Map\PlaceStoreRequest;
use App\Http\Requests\Map\PlaceUpdateRequest;
use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Map\Point;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use MatanYadaev\EloquentSpatial\Objects\Point as EloquentSpatialPoint;

class PlacesController extends Controller
{
    public function index(Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', [
            'entity' => 'Lieux',
        ]));

        return view('templates.admin.map.places.index');
    }

    public function create(): View
    {
        $place = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Lieux']));

        return view('templates.admin.map.places.edit', compact('place'));
    }

    public function store(PlaceStoreRequest $request): RedirectResponse
    {
        $validated = $request->validated();
        $place = Place::create($validated);

        $place->points()->create([
            'coord' => new EloquentSpatialPoint($validated['latitude'], $validated['longitude']),
        ]);

        if ($request->file('place_picture')) {
            $place->addMediaFromRequest('place_picture')->toMediaCollection('place_picture');
        }

        return redirect()
            ->route('map.places.index')
            ->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Lieux',
                'name' => $place->name,
            ]));
    }

    public function edit(Place $place, Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Lieux',
            'detail' => $place->name,
        ]));

        $place->load('currentPoint');

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query'])
            ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return view('templates.admin.map.places.edit', compact('place', 'previousParams'));
    }

    public function update(PlaceUpdateRequest $request, Place $place): RedirectResponse
    {
        $validated = $request->validated();

        $place->update($validated);

        $pointActive = Point::find($validated['point_id']);
        $pointActive->coord = new EloquentSpatialPoint($validated['latitude'], $validated['longitude']);
        $pointActive->save();

        if ($request->file('place_picture')) {
            $place->addMediaFromRequest('place_picture')->toMediaCollection('place_picture');
        }

        // Si l'option de fusion de lieux est activée et que des lieux ont été sélectionnés
        // on met à jour les contenus liés à ces lieux pour les associer au lieu en cours d'édition
        // puis on supprime les lieux fusionnés.
        if ($validated['merge_places_activation'] && count($validated['merge_places_ids']) > 0) {
            $mergePlacesIds = array_values(array_diff($validated['merge_places_ids'], [$place->id]));
            if (count($mergePlacesIds) > 0) {
                ContentLocation::query()
                    ->where('location_type', Place::class)
                    ->whereIn('location_id', $validated['merge_places_ids'])
                    ->update([
                        'location_type' => Place::class,
                        'location_id' => $place->id,
                    ]);

                Place::destroy($validated['merge_places_ids']);
            }
        }

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query'])
            ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()
            ->to(route('map.places.index') . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Lieux',
                'name' => $place->name,
            ]));
    }

    public function destroy(Place $place): RedirectResponse
    {
        // Avant de supprimer le lieu, on associe les contenus qui lui sont
        // liés à des points créés à partir de ses coordonnées. (Un point
        // unique pour chaque contenu car un point ne doit pas être relié à
        // plusieurs autres entités de la base.)
        $contentLocations = ContentLocation::query()
            ->where('location_type', Place::class)
            ->where('location_id', $place->id)
            ->get();

        foreach ($contentLocations as $contentLocation) {
            $point = new Point();
            $point->coord = $place->currentPoint->coord;
            $point->former_location_name = $place->name;
            $point->save();

            $contentLocation->update([
                'location_type' => Point::class,
                'location_id' => $point->id,
            ]);
        }

        $place->delete();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Lieux',
            'name' => $place->name,
        ]));
    }
}

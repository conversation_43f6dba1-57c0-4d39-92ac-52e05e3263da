<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\LibraryMedia\LibraryMediaCategoryStoreRequest;
use App\Http\Requests\LibraryMedia\LibraryMediaCategoryUpdateRequest;
use App\Models\LibraryMedia\LibraryMediaCategory;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class LibraryMediaCategoriesController extends Controller
{
    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.parent.index', [
            'parent' => 'Bibliothèque médias',
            'entity' => 'Categories',
        ]));

        return view('templates.admin.library-media.categories.index');
    }

    public function create(): View
    {
        $category = null;
        SEOTools::setTitle(__('breadcrumbs.parent.create', [
            'parent' => 'Bibliothèque médias',
            'entity' => 'Categories',
        ]));

        return view('templates.admin.library-media.categories.edit', compact('category'));
    }

    public function store(LibraryMediaCategoryStoreRequest $request): RedirectResponse
    {
        $category = LibraryMediaCategory::create($request->validated());

        return redirect()->route('libraryMedia.categories.index')
            ->with('toast_success', __('crud.parent.created', [
                'parent' => 'Bibliothèque médias',
                'entity' => 'Categories',
                'name' => $category->title,
            ]));
    }

    public function edit(LibraryMediaCategory $category): View
    {
        SEOTools::setTitle(__('breadcrumbs.parent.edit', [
            'parent' => 'Bibliothèque médias',
            'entity' => 'Categories',
            'detail' => $category->title,
        ]));

        return view('templates.admin.library-media.categories.edit', compact('category'));
    }

    public function update(
        LibraryMediaCategoryUpdateRequest $request,
        LibraryMediaCategory $category
    ): RedirectResponse {
        $category->update($request->validated());

        return back()->with('toast_success', __('crud.parent.updated', [
            'parent' => 'Bibliothèque médias',
            'entity' => 'Categories',
            'name' => $category->title,
        ]));
    }

    public function destroy(LibraryMediaCategory $category): RedirectResponse
    {
        $category->delete();

        return back()->with('toast_success', __('crud.parent.destroyed', [
            'parent' => 'Bibliothèque médias',
            'entity' => 'Categories',
            'name' => $category->title,
        ]));
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Performers\MemberStoreRequest;
use App\Http\Requests\Performers\MemberUpdateRequest;
use App\Models\Performers\Member;
use App\Models\Performers\Performer;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class MembersController extends Controller
{
    /** @throws \ErrorException */
    public function index(?Performer $performer = null): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Membres']));

        return view('templates.admin.performers.members.index', compact('performer'));
    }

    /** @throws \Exception */
    public function create(?Performer $performer = null): View
    {
        $member = null;

        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Membre']));

        return view('templates.admin.performers.members.edit', compact('member', 'performer'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function store(MemberStoreRequest $request, ?Performer $performer = null): RedirectResponse
    {
        /** @var Member $member */
        $member = Member::create($request->safe()->toArray());

        if ($performer) {
            $memberIds = $performer->members()->orderByPivot('index')->pluck('members.id');

            $memberIdsWithPivot = collect($memberIds)->mapWithKeys(fn (
                int $memberId,
                int $index
            ) => [$memberId => ['index' => $index]])->toArray();

            $nextIndex = count($memberIdsWithPivot);
            $performer->members()->attach([$member->id => [
                'index' => $nextIndex,
                'roles' => $request->safe()->offsetGet('roles'),
                'begin_date' => $request->safe()->offsetGet('begin_date'),
                'end_date' => $request->safe()->offsetGet('end_date'),
            ]]);

            return redirect()->route('performer.edit', $performer)->with('toast_success', __('crud.parent.created', [
                'parent' => 'Interprète',
                'entity' => $performer->name . ' > Membre',
                'detail' => $member->first_name,
            ]));
        } else {
            // ToDo: RECUPERER DERNIER INDEX DE L'ARTISTE POUR METTRE index + 1
            //$member->performers()->sync($request->safe()->offsetGet('performers_ids'));

            return redirect()->route('members.index')->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Membre',
                'name' => $member->first_name,
            ]));
        }
    }

    /** @throws \Exception */
    public function edit(Request $request, Member $member, ?Performer $performer = null): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Membre',
            'detail' => $member->first_name,
        ]));

        $pivotData = null;
        if ($performer) {
            $pivotData = $member->performers()
                ->wherePivot('performer_id', $performer->id)
                ->first()
                ->getRelationValue('pivot');
        }

        return view('templates.admin.performers.members.edit', compact('member', 'performer', 'pivotData'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function update(MemberUpdateRequest $request, Member $member, ?Performer $performer = null): RedirectResponse
    {
        $member->update($request->safe()->toArray());

        if ($performer) {
            $member->performers()->updateExistingPivot($performer->id, [
                'roles' => $request->safe()->offsetGet('roles'),
                'begin_date' => $request->safe()->offsetGet('begin_date'),
                'end_date' => $request->safe()->offsetGet('end_date'),
            ]);

            return redirect()->route('performer.edit', $performer)->with('toast_success', __('crud.parent.updated', [
                'parent' => 'Interprète',
                'entity' => $performer->name . ' > Membre',
                'detail' => $member->first_name,
            ]));
        } else {
            // ToDo: RECUPERER DERNIER INDEX DE L'ARTISTE POUR METTRE index + 1
            //$member->performers()->sync($request->safe()->offsetGet('performers_ids'));

            return redirect()->to(route('member.edit', ['member' => $member->id]))
                ->with('toast_success', __('crud.orphan.updated', [
                    'entity' => 'Membre',
                    'name' => $member->first_name,
                ]));
        }
    }

    /** @throws \Exception */
    public function destroy(Member $member): RedirectResponse
    {
        $member->delete();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Membre',
            'name' => $member->first_name,
        ]));
    }
}

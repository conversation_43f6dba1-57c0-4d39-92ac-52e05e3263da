<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\ContentLocationTrait;
use App\Http\Requests\Programs\ProgramStoreRequest;
use App\Http\Requests\Programs\ProgramUpdateRequest;
use App\Models\Audio\Podcast;
use App\Models\Radio\Program;
use App\Services\Elasticsearch\PodcastIndexService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Date;

class ProgramsController extends Controller
{
    use ContentLocationTrait;

    /** @throws \ErrorException */
    public function index(Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Émissions']));

        return view('templates.admin.programs.index');
    }

    public function create(?Program $mainProgram = null): View
    {
        $program = null;
        $duration = null;
        if ($mainProgram) {
            SEOTools::setTitle(__('breadcrumbs.parent.create', [
                'parent' => 'Émissions',
                'entity' => $mainProgram->title,
            ]));
        } else {
            SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Émissions']));
        }

        return view('templates.admin.programs.edit', compact('program', 'duration', 'mainProgram'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function store(ProgramStoreRequest $request, ?Program $mainProgram = null): RedirectResponse
    {
        /** @var \App\Models\Radio\Program $program */
        $program = Program::create($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
            'main_program_id' => $mainProgram?->id,
        ])->toArray());
        $authorsIdsWithPivot = collect($request->safe()->offsetGet('authors_ids'))->mapWithKeys(fn (
            int $authorId,
            int $index
        ) => [$authorId => ['index' => $index]])->toArray();
        $program->authors()->sync($authorsIdsWithPivot);
        $program->addMediaFromRequest('cover')->toMediaCollection('cover');
        $this->storeLocationFromRequest($request, $program);
        programs(true);
        if ($mainProgram) {
            return redirect()->route('program.edit', $mainProgram)->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Sous-émissions',
                'name' => $program->title,
            ]));
        }

        return redirect()->route('programs.index')->with('toast_success', __('crud.orphan.created', [
            'entity' => 'Émissions',
            'name' => $program->title,
        ]));
    }

    /** @throws \ErrorException */
    public function edit(Program $program, Request $request): View
    {
        $mainProgram = null;
        $duration = Date::now()->startOfDay()->seconds($program->duration);
        if ($program->mainProgram) {
            $mainProgram = $program->mainProgram;
            SEOTools::setTitle(__('breadcrumbs.parent.edit', [
                'parent' => 'Émissions',
                'entity' => $mainProgram->title . ' > Sous-émission',
                'detail' => $program->title,
            ]));
        } else {
            SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
                'entity' => 'Émissions',
                'detail' => $program->title,
            ]));
        }

        return view(
            'templates.admin.programs.edit',
            compact(
                'program',
                'mainProgram',
                'duration',
            )
        );
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function update(ProgramUpdateRequest $request, Program $program): RedirectResponse
    {
        $program->update($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
        ])->toArray());
        $authorsIdsWithPivot = collect($request->safe()->offsetGet('authors_ids'))->mapWithKeys(fn (
            int $authorId,
            int $index
        ) => [$authorId => ['index' => $index]])->toArray();
        $program->authors()->sync($authorsIdsWithPivot);
        if ($request->file('cover')) {
            $program->addMediaFromRequest('cover')->toMediaCollection('cover');
        }
        $this->updateLocationFromRequest($request, $program);
        $program->podcasts()->with(['media', 'thematic', 'program', 'contentLocations'])->each(function (Podcast $podcast) {
            if ($podcast->audio_stream['mp3']) {
                app(PodcastIndexService::class)->updateOrCreate($podcast);
            }
        });
        programs(true);

        return redirect()->to(route('program.edit', $program))
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => $program->mainProgram ? 'Sous-émissions' : 'Émissions',
                'name' => $program->title,
            ]));
    }

    /** @throws \Exception */
    public function destroy(Program $program): RedirectResponse
    {
        $program->delete();
        programs(true);

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => $program->mainProgram ? 'Sous-émissions' : 'Émissions',
            'name' => $program->title,
        ]));
    }
}

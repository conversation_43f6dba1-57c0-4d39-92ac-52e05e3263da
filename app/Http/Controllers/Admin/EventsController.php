<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\ContentLocationTrait;
use App\Http\Requests\Events\EventStoreRequest;
use App\Http\Requests\Events\EventUpdateRequest;
use App\Models\Events\Event;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\EventIndexService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;

class EventsController extends Controller
{
    use ContentLocationTrait;

    /** @throws \ErrorException */
    public function index(Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Évènements']));

        return view('templates.admin.events.index');
    }

    public function create(): View
    {
        $event = null;

        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Évènement']));

        return view('templates.admin.events.edit', compact('event'));
    }

    /**
     * @throws \JsonException
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function store(EventStoreRequest $request): RedirectResponse
    {
        /** @var \App\Models\Events\Event $event */
        $event = Event::create($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
        ])->toArray());

        $event->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));

        $event->addMediaFromRequest('cover')->toMediaCollection('cover');
        if ($request->has('audio')) {
            Config::set('media-library.max_file_size', 1024 * 1024 * 600);
            $event->addMediaFromRequest('audio')->toMediaCollection('audio');
        }

        $this->storeLocationFromRequest($request, $event);

        if ($event->active) {
            app(EventIndexService::class)->updateOrCreate($event);
            app(CacheService::class)->clearBrowseEventsCache();
        }

        return redirect()
            ->route('events.index')
            ->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Évènement',
                'name' => $event->title,
            ]));
    }

    public function edit(Event $event, Request $request): View
    {
        $event->load('radioStations');

        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Évènement',
            'detail' => $event->title,
        ]));

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return view('templates.admin.events.edit', compact('event', 'previousParams'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function update(EventUpdateRequest $request, Event $event): RedirectResponse
    {
        $event->update($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
        ])->toArray());
        $event->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));
        if ($request->file('cover')) {
            $event->addMediaFromRequest('cover')->toMediaCollection('cover');
        }
        if ($request->file('audio')) {
            Config::set('media-library.max_file_size', 1024 * 1024 * 600);
            $event->addMediaFromRequest('audio')->toMediaCollection('audio');
        } elseif ($request->remove_audio_file) {
            $event->clearMediaCollection('audio');
        }

        $this->updateLocationFromRequest($request, $event);

        $event->active
            ? app(EventIndexService::class)->updateOrCreate($event)
            : app(EventIndexService::class)->delete($event);
        app(CacheService::class)->clearBrowseEventsCache();

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query'])
            ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()
            ->to(route('event.edit', $event) . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Évènement',
                'name' => $event->title,
            ]));
    }

    public function destroy(Event $event): RedirectResponse
    {
        $event->delete();
        app(EventIndexService::class)->delete($event);
        app(CacheService::class)->clearBrowseEventsCache();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Évènement',
            'name' => $event->title,
        ]));
    }
}

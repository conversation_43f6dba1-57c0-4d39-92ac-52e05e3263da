<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Announcements\AnnouncementStoreRequest;
use App\Http\Requests\Announcements\AnnouncementUpdateRequest;
use App\Models\Announcements\Announcement;
use App\Services\Cache\CacheService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class AnnouncementsController extends Controller
{
    /** @throws \ErrorException */
    public function index(Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', [
            'entity' => 'Annonces',
        ]));

        return view('templates.admin.announcements.index');
    }

    public function create(): View
    {
        $announcement = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Annonces']));

        return view('templates.admin.announcements.edit', compact('announcement'));
    }

    public function store(AnnouncementStoreRequest $request): RedirectResponse
    {
        $announcement = Announcement::create($request->validated());

        $announcement->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));

        if ($request->file('cover')) {
            $announcement->addMediaFromRequest('cover')->toMediaCollection('cover');
        }

        app(CacheService::class)->clearBrowseAnnouncementsCache();

        return redirect()->route('announcements.index')
            ->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Annonces',
                'name' => $announcement->title,
            ]));
    }

    public function edit(Announcement $announcement, Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Annonces',
            'detail' => $announcement->title,
        ]));

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return view('templates.admin.announcements.edit', compact('announcement', 'previousParams'));
    }

    public function update(AnnouncementUpdateRequest $request, Announcement $announcement): RedirectResponse
    {
        $announcement->update($request->validated());
        $announcement->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));

        if ($request->file('cover')) {
            $announcement->addMediaFromRequest('cover')->toMediaCollection('cover');
        }

        app(CacheService::class)->clearBrowseAnnouncementsCache();

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to(route('announcement.edit', $announcement) . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Annonces',
                'name' => $announcement->title,
            ]));
    }

    /** @throws \Exception */
    public function destroy(Announcement $announcement): RedirectResponse
    {
        // Announcement use softDelete
        $announcement->delete();

        app(CacheService::class)->clearBrowseAnnouncementsCache();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Annonces',
            'name' => $announcement->title,
        ]));
    }

    /** @throws \Exception */
    public function forceDestroy(Request $request, $id): RedirectResponse
    {
        // Announcement use softDelete : use forceDelete() to really delete it
        $announcement = Announcement::withTrashed()->findOrFail($id);
        $announcement->forceDelete();

        app(CacheService::class)->clearBrowseAnnouncementsCache();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Annonces',
            'name' => $announcement->title,
        ]));
    }

    /** @throws \Exception */
    public function restore(Request $request, $id): RedirectResponse
    {
        // Announcement use softDelete : use restore() to restore it
        $announcement = Announcement::withTrashed()->findOrFail($id);
        $announcement->restore();

        app(CacheService::class)->clearBrowseAnnouncementsCache();

        return back()->with('toast_success', __('crud.orphan.restored', [
            'entity' => 'Annonces',
            'name' => $announcement->title,
        ]));
    }
}

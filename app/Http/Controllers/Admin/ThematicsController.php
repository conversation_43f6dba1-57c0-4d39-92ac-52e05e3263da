<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Thematics\ThematicStoreRequest;
use App\Http\Requests\Thematics\ThematicUpdateRequest;
use App\Models\Audio\Thematic;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class ThematicsController extends Controller
{
    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', [
            'entity' => 'Thématiques',
        ]));

        return view('templates.admin.thematics.index');
    }

    public function create(): View
    {
        $thematic = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', [
            'entity' => 'Thématiques',
        ]));

        return view('templates.admin.thematics.edit', compact('thematic'));
    }

    public function store(ThematicStoreRequest $request): RedirectResponse
    {
        $thematic = Thematic::create($request->validated());
        $thematic->addMediaFromRequest('illustration')->toMediaCollection('illustrations');
        thematics(true);

        return redirect()->route('thematics.index')
            ->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Thématiques',
                'name' => $thematic->title,
            ]));
    }

    public function edit(Thematic $thematic): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Thématiques',
            'detail' => $thematic->title,
        ]));

        return view('templates.admin.thematics.edit', compact('thematic'));
    }

    public function update(ThematicUpdateRequest $request, Thematic $thematic): RedirectResponse
    {
        $thematic->update($request->validated());
        if ($request->file('illustration')) {
            $thematic->addMediaFromRequest('illustration')->toMediaCollection('illustrations');
        }
        thematics(true);

        return back()->with('toast_success', __('crud.orphan.updated', [
            'entity' => 'Thématiques',
            'name' => $thematic->title,
        ]));
    }

    /** @throws \Exception */
    public function destroy(Thematic $thematic): RedirectResponse
    {
        $thematic->delete();
        thematics(true);

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Thématiques',
            'name' => $thematic->title,
        ]));
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Radio\Program;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\DB;

class SubProgramAssociationController extends Controller
{
    /** @throws \ErrorException */
    public function index(Program $mainProgram): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Ajout sous-émission', 'detail' => $mainProgram->title]));

        return view('templates.admin.programs.subprogram-association.index', compact('mainProgram'));
    }

    /** @throws \Exception */
    public function link(Program $mainProgram, Program $program): RedirectResponse
    {
        $previousSubPrograms = Program::where('main_program_id', $program->id)->get();
        $previousRecurrences = $program->recurrences()->get();

        foreach ($previousRecurrences as $previousRecurrence) {
            $previousRecurrence->delete();
        }

        foreach ($previousSubPrograms as $previousSubProgram) {
            DB::table('program_recurrences_sub_programs')->where('sub_program_id', $previousSubProgram->id)->delete();

            $previousSubProgram->main_program_id = null;
            $previousSubProgram->save();
        }

        $program->main_program_id = $mainProgram->id;
        $program->save();

        return redirect()->route('program.edit', $mainProgram)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Émissions',
                'name' => $mainProgram->title,
            ]));
    }

    /** @throws \Exception */
    public function unlink(Program $mainProgram, Program $program): RedirectResponse
    {
        DB::table('program_recurrences_sub_programs')->where('sub_program_id', $program->id)->delete();

        $program->main_program_id = null;
        $program->save();

        return redirect()->route('program.edit', $mainProgram)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Émissions',
                'name' => $mainProgram->title,
            ]));
    }
}

<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Cookies\CookieCategoryStoreRequest;
use App\Http\Requests\Cookies\CookieCategoryUpdateRequest;
use App\Models\Cookies\CookieCategory;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class CookieCategoriesController extends Controller
{
    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.parent.index', [
            'parent' => 'Cookies',
            'entity' => 'Categories',
        ]));

        return view('templates.admin.cookies.categories.index');
    }

    public function create(): View
    {
        $cookieCategory = null;
        SEOTools::setTitle(__('breadcrumbs.parent.create', [
            'parent' => 'Cookies',
            'entity' => 'Categories',
        ]));

        return view('templates.admin.cookies.categories.edit', compact('cookieCategory'));
    }

    /** @throws \Exception */
    public function store(CookieCategoryStoreRequest $request): RedirectResponse
    {
        $cookieCategory = CookieCategory::create($request->validated());
        cookieCategories(true);

        return redirect()->route('cookie.categories.index')
            ->with('toast_success', __('crud.parent.created', [
                'parent' => 'Cookies',
                'entity' => 'Categories',
                'name' => $cookieCategory->title,
            ]));
    }

    public function edit(CookieCategory $cookieCategory): View
    {
        SEOTools::setTitle(__('breadcrumbs.parent.edit', [
            'parent' => 'Cookies',
            'entity' => 'Categories',
            'detail' => $cookieCategory->title,
        ]));

        return view('templates.admin.cookies.categories.edit', compact('cookieCategory'));
    }

    /** @throws \Exception */
    public function update(CookieCategoryUpdateRequest $request, CookieCategory $cookieCategory): RedirectResponse
    {
        $cookieCategory->update($request->validated());
        cookieCategories(true);

        return back()->with('toast_success', __('crud.parent.updated', [
            'parent' => 'Cookies',
            'entity' => 'Categories',
            'name' => $cookieCategory->title,
        ]));
    }

    /** @throws \Exception */
    public function destroy(CookieCategory $cookieCategory): RedirectResponse
    {
        $cookieCategory->delete();
        cookieCategories(true);

        return back()->with('toast_success', __('crud.parent.destroyed', [
            'parent' => 'Cookies',
            'entity' => 'Categories',
            'name' => $cookieCategory->title,
        ]));
    }
}

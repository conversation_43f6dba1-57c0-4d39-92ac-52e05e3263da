<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\ContentLocationTrait;
use App\Http\Requests\News\NewsArticlesIndexRequest;
use App\Http\Requests\News\NewsArticleStoreRequest;
use App\Http\Requests\News\NewsArticleUpdateRequest;
use App\Models\News\NewsArticle;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\NewsArticlesIndexService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;

class NewsArticlesController extends Controller
{
    use ContentLocationTrait;

    /** @throws \ErrorException */
    public function index(NewsArticlesIndexRequest $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Actualités']));

        return view('templates.admin.news.articles.index', compact('request'));
    }

    public function create(): View
    {
        $article = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Actualités']));

        return view('templates.admin.news.articles.edit', compact('article'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function store(NewsArticleStoreRequest $request): RedirectResponse
    {
        /** @var \App\Models\News\NewsArticle $article */
        $article = NewsArticle::create($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
        ])->toArray());
        $article->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));
        $article->addMediaFromRequest('illustration')->toMediaCollection('illustrations');
        if ($request->has('audio')) {
            Config::set('media-library.max_file_size', 1024 * 1024 * 600);
            $article->addMediaFromRequest('audio')->toMediaCollection('audio');
        }
        $article->saveSeoMetaFromRequest($request);

        $this->storeLocationFromRequest($request, $article);

        if ($article->active) {
            app(NewsArticlesIndexService::class)->updateOrCreate($article);
            app(CacheService::class)->clearBrowseNewsArticlesCache();
        }

        return redirect()
            ->route('news.articles.index')
            ->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Actualités',
                'name' => $article->title,
            ]));
    }

    public function edit(NewsArticle $article, Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Actualités',
            'detail' => $article->title,
        ]));

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return view('templates.admin.news.articles.edit', compact('article', 'previousParams'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function update(NewsArticleUpdateRequest $request, NewsArticle $article): RedirectResponse
    {
        $article->update($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
        ])->toArray());
        $article->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));
        if ($request->file('illustration')) {
            $article->addMediaFromRequest('illustration')->toMediaCollection('illustrations');
        }
        if ($request->has('audio')) {
            Config::set('media-library.max_file_size', 1024 * 1024 * 600);
            $article->addMediaFromRequest('audio')->toMediaCollection('audio');
        } elseif ($request->remove_audio_file) {
            $article->clearMediaCollection('audio');
        }
        $article->saveSeoMetaFromRequest($request);

        $this->updateLocationFromRequest($request, $article);

        $article->active
            ? app(NewsArticlesIndexService::class)->updateOrCreate($article)
            : app(NewsArticlesIndexService::class)->delete($article);
        app(CacheService::class)->clearBrowseNewsArticlesCache();

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query'])
            ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()
            ->to(route('news.article.edit', $article) . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Actualités',
                'name' => $article->title,
            ]));
    }

    /** @throws \Exception */
    public function destroy(NewsArticle $article): RedirectResponse
    {
        $article->delete();
        app(NewsArticlesIndexService::class)->delete($article);
        app(CacheService::class)->clearBrowseNewsArticlesCache();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Actualités',
            'name' => $article->title,
        ]));
    }
}

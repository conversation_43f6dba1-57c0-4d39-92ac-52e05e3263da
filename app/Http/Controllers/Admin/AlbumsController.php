<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\ContentLocationTrait;
use App\Http\Requests\Performers\AlbumUpdateRequest;
use App\Models\Performers\Album;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\SongsIndexService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\File;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AlbumsController extends Controller
{
    use ContentLocationTrait;

    /** @throws \ErrorException */
    public function index(Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Albums']));

        return view('templates.admin.albums.index');
    }

    /** @throws \Exception */
    public function edit(Album $album, Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Album',
            'detail' => $album->name . ' (' . $album->performer()->first()?->name . ')',
        ]));

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return view('templates.admin.albums.edit', compact('album', 'previousParams'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function update(AlbumUpdateRequest $request, Album $album): RedirectResponse
    {
        // Nom d'album et/ou date de sortie d'album mis à jour sur Album et sur chaque musique associée
        $albumOriginalName = $album->name;
        $album->name = $request->safe()->offsetGet('name');
        $album->published_at = $request->safe()->offsetGet('published_at');
        $nameChanged = $album->isDirty('name');
        $publishedAtChanged = $album->isDirty('published_at');
        if ($nameChanged || $publishedAtChanged) {
            $album->save();

            $songUpdateArray = [];
            if ($nameChanged) {
                $songUpdateArray['album'] = $request->safe()->offsetGet('name');
            }
            if ($publishedAtChanged && $request->safe()->offsetGet('published_at')) {
                $songUpdateArray['_release_date'] = Date::parse($request->safe()->offsetGet('published_at'))->timezone('Europe/Paris')->format('Y-m-d');
                $songUpdateArray['year'] = Date::parse($request->safe()->offsetGet('published_at'))->timezone('Europe/Paris')->format('Y');
            }

            /** @var \App\Models\Audio\Song $songToUpdate */
            foreach ($album->songs->load(['performerRelationship', 'albumRelationship', 'labelRelationship']) as $songToUpdate) {
                $songToUpdate->update($songUpdateArray);
                app(SongsIndexService::class)->updateOrCreate($songToUpdate);
            }

            // Nom d'image album changé pour correspondre avec le nouveau nom
            if ($nameChanged && ! $request->file('detail_album_source')) {
                $performerNamesSlug = Str::slug(Str::ascii($album->performer()->first()?->name));
                $albumOriginalNameSlug = Str::slug(Str::ascii($albumOriginalName));
                $albumNameSlug = Str::slug(Str::ascii($album->name));

                Storage::disk('public')->copy(
                    'winmedia/images_albums/' . $performerNamesSlug . '/' . $albumOriginalNameSlug . '.jpg',
                    'winmedia/images_albums/' . $performerNamesSlug . '/' . $albumNameSlug . '.jpg'
                );
            }
        }

        // Image d'album stockée :
        // -> dans un dossier temporaire pour transfert Winmedia
        // -> dans la media collection Laravel pour stockage pérenne
        // -> dans le dossier public "winmedia/images_albums/", stockage de l'image convertie (pour prise en compte instantanée)
        if ($request->file('detail_album_source')) {
            $performerNamesSlug = Str::slug(Str::ascii($album->performer()->first()?->name));
            $albumNameSlug = Str::slug(Str::ascii($album->name));

            Storage::disk('private')->putFileAs(
                'tmp/albums',
                $request->file('detail_album_source'),
                $album->id . '.jpg'
            );

            $albumMedia = $album->detail->addMediaFromRequest('detail_album_source')->toMediaCollection('album_source');

            $albumConversionPath = $albumMedia->getPath('small_square');

            Storage::disk('public')->putFileAs(
                'winmedia/images_albums/' . $performerNamesSlug,
                new File($albumConversionPath),
                $albumNameSlug . '.jpg'
            );
        }
        //ToDO : mettre à jour image de chaque titre ?

        // Données supplémentaires (localisation)
        $album->detail->update($request->safe()->offsetGet('detail'));

        // Location
        $this->updateLocationFromRequest($request, $album);

        // Refresh updated_at even if the album has not been modified
        $album->touch();

        app(CacheService::class)->clearBrowseSongsCache();

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to(route('album.edit', $album) . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Album',
                'name' => $album->name,
            ]));
    }
}

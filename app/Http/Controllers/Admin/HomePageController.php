<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Home\HomePageUpdateRequest;
use App\Models\PageContents\PageContent;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class HomePageController extends Controller
{
    public function edit(): View
    {
        $pageContent = PageContent::where('unique_key', 'home_page_content')->sole();
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Accueil',
            'detail' => 'Page',
        ]));

        return view('templates.admin.home.page.edit', compact('pageContent'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function update(HomePageUpdateRequest $request): RedirectResponse
    {
        /** @var \App\Models\PageContents\PageContent $pageContent */
        $pageContent = PageContent::where('unique_key', 'home_page_content')->sole();
        $pageContent->saveSeoMetaFromRequest($request);

        return back()->with('toast_success', __('crud.orphan.updated', [
            'entity' => 'Accueil',
            'name' => 'Page',
        ]));
    }
}

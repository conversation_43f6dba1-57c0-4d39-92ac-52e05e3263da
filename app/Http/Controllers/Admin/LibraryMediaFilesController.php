<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\LibraryMedia\LibraryMediaFilesIndexRequest;
use App\Http\Requests\LibraryMedia\LibraryMediaFileStoreRequest;
use App\Http\Requests\LibraryMedia\LibraryMediaFileUpdateRequest;
use App\Models\LibraryMedia\LibraryMediaCategory;
use App\Models\LibraryMedia\LibraryMediaFile;
use App\Services\LibraryMedia\FilesService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class LibraryMediaFilesController extends Controller
{
    /**
     * @throws \ErrorException
     * @throws \Exception
     */
    public function index(LibraryMediaFilesIndexRequest $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.parent.index', [
            'parent' => 'Bibliothèque médias',
            'entity' => 'Fichiers',
        ]));
        app(FilesService::class)->injectJavascriptInView();
        $js = mix('/js/templates/admin/library-media/edit.js');

        return view('templates.admin.library-media.files.index', compact('request', 'js'));
    }

    public function create(): View
    {
        $file = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Bibliothèque médias']));

        return view('templates.admin.library-media.files.edit', compact('file'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function store(LibraryMediaFileStoreRequest $request): RedirectResponse
    {
        $file = LibraryMediaFile::create($request->validated());
        $file->addMediaFromRequest('media')->toMediaCollection('media');

        return redirect()->route('libraryMedia.files.index')
            ->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Bibliothèque médias',
                'name' => $file->name,
            ]));
    }

    /** @throws \Exception */
    public function edit(LibraryMediaFile $file): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Bibliothèque médias',
            'detail' => $file->name,
        ]));
        app(FilesService::class)->injectJavascriptInView();
        $js = mix('/js/templates/admin/library-media/edit.js');

        return view('templates.admin.library-media.files.edit', compact('file', 'js'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function update(LibraryMediaFileUpdateRequest $request, LibraryMediaFile $file): RedirectResponse
    {
        $file->update($request->validated());
        if ($request->file('media')) {
            $file->addMediaFromRequest('media')->toMediaCollection('media');
        }

        return back()->with('toast_success', __('crud.orphan.updated', [
            'entity' => 'Bibliothèque médias',
            'name' => $file->name,
        ]));
    }

    /** @throws \Exception */
    public function destroy(LibraryMediaFile $file): RedirectResponse
    {
        $file->delete();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Bibliothèque médias',
            'name' => $file->name,
        ]));
    }

    public function clipboardContent(LibraryMediaFile $file, string $type, ?string $locale = null): JsonResponse
    {
        $media = $file->getFirstMedia('media');
        if (! $media) {
            $returnCode = Response::HTTP_NOT_FOUND;
            $clipboardContent = null;
            $message = __('Not media has been attached to this file.');

            return response()->json(compact('clipboardContent', 'message'), $returnCode);
        }
        $locale = $locale ?: app()->getLocale();
        switch ($type) {
            case 'url':
                $returnCode = Response::HTTP_OK;
                $clipboardContent = $file->getFirstMedia('media')->getFullUrl();
                $message = __('Clipboard copy: :name - :type.', [
                    'name' => $file->name,
                    'type' => __('URL'),
                ]);
                break;
            case 'display':
                if (! $file->can_be_displayed_on_page) {
                    $returnCode = Response::HTTP_NOT_FOUND;
                    $clipboardContent = null;
                    $message = __('This type of media can\'t be displayed.');
                    break;
                }
                $returnCode = Response::HTTP_OK;
                $clipboardContent = trim(view(
                    'components.admin.library-media.clipboard-copy.display-html',
                    compact('file', 'media', 'locale')
                )->toHtml());
                $message = __('Clipboard copy: :name - :type.', [
                    'name' => $file->name,
                    'type' => __('HTML Display'),
                ]);
                break;
            case 'download':
                $returnCode = Response::HTTP_OK;
                $clipboardContent = trim(view(
                    'components.admin.library-media.clipboard-copy.download-html',
                    compact('file', 'media', 'locale')
                )->toHtml());
                $message = __('Clipboard copy: :name - :type.', [
                    'name' => $file->name,
                    'type' => __('HTML Download'),
                ]);
                break;
            default:
                $returnCode = Response::HTTP_BAD_REQUEST;
                $clipboardContent = null;
                $message = __('An unexpected error occurred. If the problem persists, please contact support.');
        }

        return response()->json(compact('clipboardContent', 'message'), $returnCode);
    }

    /** @throws \Exception */
    public function upload(Request $request): JsonResponse
    {
        $mediaCategory = LibraryMediaCategory::firstOrCreate([
            'title' => $request->type,
        ]);

        $mediaFile = LibraryMediaFile::create([
            'category_id' => $mediaCategory->id,
            'name' => $request->file('file')->getClientOriginalName(),
        ]);

        $returnCompressedImage = true;
        if ($request->file('file')?->getMimeType() === 'image/gif') {
            $returnCompressedImage = false;
        }

        $mediaFile
            ->addMedia($request->file('file'))
            ->toMediaCollection('media');

        return response()->json([
            'location' => $returnCompressedImage ? $mediaFile->getFirstMediaUrl('media', 'compress') : $mediaFile->getFirstMediaUrl('media'),
        ]);
    }
}

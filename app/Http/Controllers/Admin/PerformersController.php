<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Performers\PerformerUpdateRequest;
use App\Models\Performers\Performer;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\PerformersIndexService;
use App\Services\Elasticsearch\SongsIndexService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\File;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PerformersController extends Controller
{
    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Artistes']));

        return view('templates.admin.performers.index');
    }

    /** @throws \Exception */
    public function edit(Performer $performer, Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Artiste',
            'detail' => $performer->name,
        ]));

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return view('templates.admin.performers.edit', compact('performer', 'previousParams'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function update(PerformerUpdateRequest $request, Performer $performer): RedirectResponse
    {
        // Nom d'artiste mis à jour sur Performer et sur chaque musique associée
        $performerOriginalName = $performer->name;
        $performer->name = $request->safe()->offsetGet('name');
        if ($performer->isDirty('name')) {
            $performer->save();

            /** @var \App\Models\Audio\Song $songToUpdate */
            foreach ($performer->songs->load(['performerRelationship', 'albumRelationship', 'labelRelationship']) as $songToUpdate) {
                $songToUpdate->update([
                    'performer' => $request->safe()->offsetGet('name'),
                ]);
                app(SongsIndexService::class)->updateOrCreate($songToUpdate);
            }

            // Nom d'image artiste changé pour correspondre avec le nouveau nom
            if (! $request->file('detail_artist_picture_source')) {
                $performerNameSlug = Str::slug(Str::ascii($performer->name));
                $performerOriginalNameSlug = Str::slug(Str::ascii($performerOriginalName));

                Storage::disk('public')->copy(
                    'winmedia/performers_thumbnail/' . $performerOriginalNameSlug . '.jpg',
                    'winmedia/performers_thumbnail/' . $performerNameSlug . '.jpg'
                );
            }
        }

        // Image d'artiste stockée :
        // -> dans un dossier temporaire pour transfert Winmedia
        // -> dans la media collection Laravel pour stockage pérenne
        // -> dans le dossier public "winmedia/performers_thumbnail/", stockage de l'image convertie (pour prise en compte instantanée)
        if ($request->file('detail_artist_picture_source')) {
            $performerNameSlug = Str::slug(Str::ascii($performer->name));

            Storage::disk('private')->putFileAs(
                'tmp/performers',
                $request->file('detail_artist_picture_source'),
                $performer->id . '.jpg'
            );

            $artistPictureMedia = $performer->detail->addMediaFromRequest('detail_artist_picture_source')->toMediaCollection('artist_picture_source');

            $artistPictureConversionPath = $artistPictureMedia->getPath('jpg_conversion');

            Storage::disk('public')->putFileAs(
                'winmedia/performers_thumbnail',
                new File($artistPictureConversionPath),
                $performerNameSlug . '.jpg'
            );
        }

        // Données supplémentaires
        $performer->detail->update($request->safe()->offsetGet('detail'));

        // Membres
        $membersIdsWithPivot = collect($request->safe()->offsetGet('members_ids'))->mapWithKeys(fn (
            int $memberId,
            int $index
        ) => [$memberId => ['index' => $index]])->toArray();
        $performer->members()->sync($membersIdsWithPivot);

        app(PerformersIndexService::class)->updateOrCreate($performer);

        app(CacheService::class)->clearBrowseSongsCache();

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to(route('performer.edit', $performer) . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Artiste',
                'name' => $performer->name,
            ]));
    }
}

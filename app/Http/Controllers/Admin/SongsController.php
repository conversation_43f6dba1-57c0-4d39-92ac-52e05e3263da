<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Songs\SongUpdateRequest;
use App\Models\Audio\Song;
use App\Models\Performers\Album;
use App\Models\Performers\Performer;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\PerformersIndexService;
use App\Services\Elasticsearch\SongsIndexService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\File;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class SongsController extends Controller
{
    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Titres']));

        return view('templates.admin.songs.index');
    }

    /** @throws \Exception */
    public function edit(Song $song, Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Titre',
            'detail' => $song->performer . ' - ' . $song->title,
        ]));

        $allPerformers = Performer::distinct(['name'])->pluck('name')->toArray();
        if (! in_array(strtolower($song->performer), array_map('strtolower', $allPerformers))) {
            $allPerformers[] = $song->performer;
        }
        natcasesort($allPerformers);

        $allAlbums = Album::distinct(['name'])->pluck('name')->toArray();
        if (! in_array(strtolower($song->album), array_map('strtolower', $allAlbums))) {
            $allAlbums[] = $song->album;
        }
        natcasesort($allAlbums);

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        $performerSongsCount = $song->performerRelationship?->songs()->musicArchivageType()->count();
        $albumSongsCount = $song->albumRelationship?->songs()->musicArchivageType()->count();

        return view('templates.admin.songs.edit', compact('song', 'previousParams', 'performerSongsCount', 'albumSongsCount', 'allPerformers', 'allAlbums'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function update(SongUpdateRequest $request, Song $song): RedirectResponse
    {
        if ($request->safe()->offsetExists('performer_edit')) {
            if ($song->performerRelationship) {
                if (! Performer::where('name', $request->safe()->offsetGet('performer'))->exists()) {
                    $song->performerRelationship->update([
                        'name' => $request->safe()->offsetGet('performer'),
                    ]);
                }

                $songsToUpdate = $song->performerRelationship->songs()->musicArchivageType()->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])->get();
                /** @var Song $songToUpdate */
                foreach ($songsToUpdate as $songToUpdate) {
                    $songToUpdate->update([
                        'performer' => $request->safe()->offsetGet('performer'),
                    ]);
                    app(SongsIndexService::class)->updateOrCreate($songToUpdate);
                }
            }
        }

        if ($request->safe()->offsetExists('album_edit')) {
            if ($song->albumRelationship) {
                $song->albumRelationship->update([
                    'name' => $request->safe()->offsetGet('album'),
                ]);
                $songsToUpdate = $song->albumRelationship->songs()->musicArchivageType()->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])->get();
                /** @var Song $songToUpdate */
                foreach ($songsToUpdate as $songToUpdate) {
                    $songToUpdate->update([
                        'album' => $request->safe()->offsetGet('album'),
                    ]);
                    app(SongsIndexService::class)->updateOrCreate($songToUpdate);
                }
            }
        }

        if ($song->performerRelationship) {
            app(PerformersIndexService::class)->updateOrCreate($song->performerRelationship);
        }

        $song->update($request->validated());

        app(SongsIndexService::class)->updateOrCreate($song);

        // Image d'artiste stockée :
        // -> dans un dossier temporaire pour transfert Winmedia
        // -> dans la media collection Laravel pour stockage pérenne
        // -> dans les dossiers public "winmedia/pochettes_thumbnail/", "winmedia/pochettes_middle/", "winmedia/pochettes_brut/", stockage de l'image convertie (pour prise en compte instantanée)
        if ($request->file('cover_source')) {
            Storage::disk('private')->putFileAs(
                'tmp/songs',
                $request->file('cover_source'),
                $song->id . '.jpg'
            );

            $songMedia = $song->addMediaFromRequest('cover_source')->toMediaCollection('cover_source');

            $songConversionPathThumb = $songMedia->getPath('thumb_square');
            $songConversionPathMiddle = $songMedia->getPath('middle_square');
            $songConversionPathBrut = $songMedia->getPath();

            Storage::disk('public')->putFileAs(
                'winmedia/pochettes_thumbnail',
                new File($songConversionPathThumb),
                $song->id . '.jpg'
            );

            Storage::disk('public')->putFileAs(
                'winmedia/pochettes_middle',
                new File($songConversionPathMiddle),
                $song->id . '.jpg'
            );

            Storage::disk('public')->putFileAs(
                'winmedia/pochettes_brut',
                new File($songConversionPathBrut),
                $song->id . '.jpg'
            );
        }

        app(CacheService::class)->clearBrowseSongsCache();

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to(route('song.edit', $song) . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Titre',
                'name' => $song->performer . ' - ' . $song->title,
            ]));
    }

    /** @throws \Exception */
    public function destroy(Song $song): RedirectResponse
    {
        if ($song->albumRelationship && count($song->albumRelationship->songs) === 1) {
            /** @var Song $uniqueSongOfAlbum */
            $uniqueSongOfAlbum = $song->albumRelationship->songs[0];
            if ($uniqueSongOfAlbum->id === $song->id) {
                $song->albumRelationship->delete();
            }
        }

        app(SongsIndexService::class)->delete($song);
        $song->delete();

        app(CacheService::class)->clearBrowseSongsCache();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Titre',
            'name' => $song->performer . ' - ' . $song->title,
        ]));
    }
}

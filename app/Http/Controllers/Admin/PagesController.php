<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Pages\PageStoreRequest;
use App\Http\Requests\Pages\PageUpdateRequest;
use App\Models\Pages\Page;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;

class PagesController extends Controller
{
    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Pages']));

        return view('templates.admin.pages.index');
    }

    public function create(): View
    {
        $page = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Pages']));

        return view('templates.admin.pages.edit', compact('page'));
    }

    /** @throws \Exception */
    public function store(PageStoreRequest $request): RedirectResponse
    {
        /** @var \App\Models\Pages\Page $page */
        $page = Page::create($request->validated());
        $page->saveSeoMetaFromRequest($request);
        pages(true);

        return redirect()->route('pages.index')->with('toast_success', __('crud.orphan.created', [
            'entity' => 'Pages',
            'name' => $page->nav_title,
        ]));
    }

    public function edit(Page $page): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Pages',
            'detail' => $page->nav_title,
        ]));

        return view('templates.admin.pages.edit', compact('page'));
    }

    /** @throws \Exception */
    public function update(PageUpdateRequest $request, Page $page): RedirectResponse
    {
        $page->update($request->safe()->except('unique_key'));
        $page->saveSeoMetaFromRequest($request);
        pages(true);

        return redirect()->route('page.edit', $page)->with('toast_success', __('crud.orphan.updated', [
            'entity' => 'Pages',
            'name' => $page->nav_title,
        ]));
    }

    /** @throws \Exception */
    public function destroy(Page $page): RedirectResponse
    {
        $page->delete();
        pages(true);

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Pages',
            'name' => $page->nav_title,
        ]));
    }
}

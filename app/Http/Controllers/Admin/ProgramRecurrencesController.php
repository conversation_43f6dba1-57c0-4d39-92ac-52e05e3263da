<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Programs\ProgramRecurrenceStoreRequest;
use App\Http\Requests\Programs\ProgramRecurrenceUpdateRequest;
use App\Models\Radio\Program;
use App\Models\Radio\ProgramRecurrence;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Date;

class ProgramRecurrencesController extends Controller
{
    public function create(Program $program): View
    {
        $programRecurrence = null;
        SEOTools::setTitle(__('breadcrumbs.parent.create', [
            'parent' => 'Émissions',
            'entity' => 'Récurrences',
        ]));

        return view('templates.admin.programs.recurrences.edit', compact('programRecurrence', 'program'));
    }

    /** @throws \Exception */
    public function store(Program $program, ProgramRecurrenceStoreRequest $request): RedirectResponse
    {
        $programRecurrence = ProgramRecurrence::create(array_merge(
            $request->validated(),
            [
                'program_id' => $program->id,
                'months' => json_decode($request->months, false, 512, JSON_THROW_ON_ERROR),
                'month_days' => json_decode($request->month_days, false, 512, JSON_THROW_ON_ERROR),
                'week_days' => json_decode($request->week_days, false, 512, JSON_THROW_ON_ERROR),
                'time' => Date::parse($request->safe()->offsetGet('local_time'))
                    ->setTimezone('UTC')
                    ->format('H:i'),
                'local_time' => Date::parse($request->safe()->offsetGet('local_time'))
                    ->setTimezone('Europe/Paris')
                    ->format('H:i'),
                'local_time_tz' => 'Europe/Paris',
                'month_week' => json_decode($request->month_week, false, 512, JSON_THROW_ON_ERROR),
            ]
        ));
        $programRecurrence->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));
        $subProgramIdsWithPivot = collect($request->sub_programs)
            ->mapWithKeys(function (array $subProgram) {
                $localTime = Date::parse($subProgram['local_time']);

                return [
                    $subProgram['id'] => [
                        'time' => $localTime->setTimezone('UTC')->format('H:i'),
                        'local_time' => $localTime->setTimezone('Europe/Paris')->format('H:i'),
                        'local_time_tz' => 'Europe/Paris',
                    ],
                ];
            })
            ->toArray();
        $programRecurrence->subPrograms()->sync($subProgramIdsWithPivot);
        programs(true);

        return redirect()->route('program.edit', $program)
            ->with('toast_success', __('crud.parent.created', [
                'parent' => 'Émissions',
                'entity' => 'Récurrences',
                'name' => $programRecurrence->label,
            ]));
    }

    public function edit(ProgramRecurrence $programRecurrence): View
    {
        SEOTools::setTitle(__('breadcrumbs.parent.edit', [
            'parent' => 'Émissions',
            'entity' => 'Récurrences',
            'detail' => $programRecurrence->label,
        ]));
        $program = $programRecurrence->program;

        return view('templates.admin.programs.recurrences.edit', compact('programRecurrence', 'program'));
    }

    public function update(
        ProgramRecurrence $programRecurrence,
        ProgramRecurrenceUpdateRequest $request
    ): RedirectResponse {
        $programRecurrence->update(array_merge(
            $request->validated(),
            [
                'months' => json_decode($request->months, false, 512, JSON_THROW_ON_ERROR),
                'month_days' => json_decode($request->month_days, false, 512, JSON_THROW_ON_ERROR),
                'week_days' => json_decode($request->week_days, false, 512, JSON_THROW_ON_ERROR),
                'time' => Date::parse($request->safe()->offsetGet('local_time'))
                    ->setTimezone('UTC')
                    ->format('H:i'),
                'local_time' => Date::parse($request->safe()->offsetGet('local_time'))
                    ->setTimezone('Europe/Paris')
                    ->format('H:i'),
                'local_time_tz' => 'Europe/Paris',
                'month_week' => json_decode($request->month_week, false, 512, JSON_THROW_ON_ERROR),
            ]
        ));
        $programRecurrence->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));
        $subProgramIdsWithPivot = collect($request->sub_programs)
            ->mapWithKeys(function (array $subProgram) {
                $localTime = Date::parse($subProgram['local_time']);

                return [
                    $subProgram['id'] => [
                        'time' => $localTime->setTimezone('UTC')->format('H:i'),
                        'local_time' => $localTime->setTimezone('Europe/Paris')->format('H:i'),
                        'local_time_tz' => 'Europe/Paris',
                    ],
                ];
            })
            ->toArray();
        $programRecurrence->subPrograms()->sync($subProgramIdsWithPivot);
        programs(true);

        return redirect()->route('program.edit', $programRecurrence->program)
            ->with('toast_success', __('crud.parent.updated', [
                'parent' => 'Émissions',
                'entity' => 'Récurrences',
                'name' => $programRecurrence->label,
            ]));
    }

    /** @throws \Exception */
    public function destroy(ProgramRecurrence $programRecurrence): RedirectResponse
    {
        $programRecurrence->delete();
        programs(true);

        return back()->with('toast_success', __('crud.parent.destroyed', [
            'parent' => 'Émissions',
            'entity' => 'Récurrences',
            'name' => $programRecurrence->label,
        ]));
    }
}

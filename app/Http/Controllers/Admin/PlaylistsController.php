<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\ContentLocationTrait;
use App\Http\Requests\PublicPlaylists\PublicPlaylistStoreRequest;
use App\Http\Requests\PublicPlaylists\PublicPlaylistUpdateRequest;
use App\Models\Audio\Playlist;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\PlaylistIndexService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class PlaylistsController extends Controller
{
    use ContentLocationTrait;

    /** @throws \ErrorException */
    public function index(Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Playlists publiques']));

        return view('templates.admin.playlists.index');
    }

    /** @throws \Exception */
    public function create(): View
    {
        $playlist = null;
        SEOTools::setTitle(__('breadcrumbs.orphan.create', ['entity' => 'Playlist publique']));
        share(['api' => ['song_url' => route('api.songs.search')]]);
        $js = mix('js/templates/admin/playlists/edit.js');

        return view('templates.admin.playlists.edit', compact('playlist', 'js'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function store(PublicPlaylistStoreRequest $request): RedirectResponse
    {
        /** @var \App\Models\Audio\Playlist $playlist */
        $playlist = Playlist::create($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
        ])->toArray());
        $playlist->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));
        $songIdsWithPivot = collect($request->safe()->offsetGet('song_ids'))->mapWithKeys(fn (
            int $songId,
            int $index
        ) => [$songId => ['index' => $index]])->toArray();
        $playlist->songsAllTypes()->sync($songIdsWithPivot);
        if ($request->file('cover')) {
            $playlist->addMediaFromRequest('cover')->toMediaCollection('cover');
        }

        $this->storeLocationFromRequest($request, $playlist);

        app(PlaylistIndexService::class)->updateOrCreate($playlist->loadMissing(['media', 'songsAllTypes']));

        app(CacheService::class)->clearBrowsePlaylistsCache();

        return redirect()
            ->route('playlists.index')
            ->with('toast_success', __('crud.orphan.created', [
                'entity' => 'Playlist publique',
                'name' => $playlist->title,
            ]));
    }

    /** @throws \Exception */
    public function edit(Playlist $playlist, Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Playlist publique',
            'detail' => $playlist->title,
        ]));
        share(['api' => ['song_url' => route('api.songs.search')]]);
        $js = mix('js/templates/admin/playlists/edit.js');

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return view('templates.admin.playlists.edit', compact('playlist', 'js', 'previousParams'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function update(PublicPlaylistUpdateRequest $request, Playlist $playlist): RedirectResponse
    {
        $playlist->update($request->safe()->merge([
            'tags' => implode(', ', $request->safe()->offsetGet('tags')),
        ])->toArray());
        $playlist->radioStations()->sync($request->safe()->offsetGet('radio_station_ids'));
        $songIdsWithPivot = collect($request->safe()->offsetGet('song_ids'))->mapWithKeys(fn (
            int $songId,
            int $index
        ) => [$songId => ['index' => $index]])->toArray();
        $playlist->songsAllTypes()->sync($songIdsWithPivot);
        if ($request->safe()->offsetGet('remove_cover')) {
            $playlist->clearMediaCollection('cover');
        }
        if ($request->file('cover') && ! $request->safe()->offsetGet('remove_cover')) {
            $playlist->addMediaFromRequest('cover')->toMediaCollection('cover');
        }

        $this->updateLocationFromRequest($request, $playlist);

        app(PlaylistIndexService::class)->updateOrCreate($playlist->loadMissing(['media', 'songsAllTypes']));

        app(CacheService::class)->clearBrowsePlaylistsCache();

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()
            ->to(route('playlist.edit', $playlist) . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Playlist publique',
                'name' => $playlist->title,
            ]));
    }

    /** @throws \Exception */
    public function destroy(Playlist $playlist): RedirectResponse
    {
        $playlist->delete();
        app(PlaylistIndexService::class)->delete($playlist);

        app(CacheService::class)->clearBrowsePlaylistsCache();

        return back()->with('toast_success', __('crud.orphan.destroyed', [
            'entity' => 'Playlist publique',
            'name' => $playlist->title,
        ]));
    }
}

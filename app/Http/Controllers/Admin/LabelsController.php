<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\ContentLocationTrait;
use App\Http\Requests\Performers\LabelUpdateRequest;
use App\Models\Performers\Label;
use App\Services\Cache\CacheService;
use App\Services\Elasticsearch\SongsIndexService;
use Artesaos\SEOTools\Facades\SEOTools;
use Illuminate\Contracts\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class LabelsController extends Controller
{
    use ContentLocationTrait;

    /** @throws \ErrorException */
    public function index(): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.index', ['entity' => 'Labels']));

        return view('templates.admin.labels.index');
    }

    /** @throws \Exception */
    public function edit(Label $label, Request $request): View
    {
        SEOTools::setTitle(__('breadcrumbs.orphan.edit', [
            'entity' => 'Label',
            'detail' => $label->name,
        ]));

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return view('templates.admin.labels.edit', compact('label', 'previousParams'));
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Exception
     */
    public function update(LabelUpdateRequest $request, Label $label): RedirectResponse
    {
        //$labelOriginalName = $label->name;
        $label->name = $request->safe()->offsetGet('name');
        $nameChanged = $label->isDirty('name');
        if ($nameChanged) {
            $label->save();

            /** @var \App\Models\Audio\Song $songToUpdate */
            foreach ($label->songs->load(['performerRelationship', 'albumRelationship', 'labelRelationship']) as $songToUpdate) {
                $songToUpdate->update([
                    'publisher' => $request->safe()->offsetGet('name'),
                ]);
                app(SongsIndexService::class)->updateOrCreate($songToUpdate);
            }
        }

        // Location
        $this->updateLocationFromRequest($request, $label);

        // Refresh updated_at even if the label has not been modified
        $label->touch();

        app(CacheService::class)->clearBrowseSongsCache();

        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to(route('label.edit', $label) . $previousParams)
            ->with('toast_success', __('crud.orphan.updated', [
                'entity' => 'Label',
                'name' => $label->name,
            ]));
    }
}

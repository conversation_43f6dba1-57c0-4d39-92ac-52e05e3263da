<?php

namespace App\Http\Middleware;

use Closure;

class XFrameOptions
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        if ($request->attributes->getBoolean('sent_from_iframe')) {
            // Allow embedding in iframes from any domain
            $response->headers->set('Content-Security-Policy', 'frame-ancestors *');
            $response->headers->set('X-Frame-Options', 'ALLOWALL');
        } else {
            // Restrict iframe embedding to same origin for all other routes
            $response->headers->set('X-Frame-Options', 'SAMEORIGIN');
        }

        return $response;
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class BasicApiAuth
{
    /**
     * Handle an incoming request.
     *
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$keys)
    {
        $transmittedApiKey = $request->headers->get('Authorization');
        $validWinmediaApiKey = 'Basic ' . base64_encode(config('api.winmedia_api_key'));
        $validAgendaApiKey = 'Basic ' . base64_encode(config('api.agenda_api_key'));

        $isValid = false;
        foreach ($keys as $key) {
            if ($key === 'winmedia_api_key' && $transmittedApiKey === $validWinmediaApiKey) {
                $isValid = true;
                break;
            }
            if ($key === 'agenda_api_key' && $transmittedApiKey === $validAgendaApiKey) {
                $isValid = true;
                break;
            }
        }

        if (! $isValid) {
            return response()->json('Unauthorized', Response::HTTP_UNAUTHORIZED);
        }

        return $next($request);
    }
}

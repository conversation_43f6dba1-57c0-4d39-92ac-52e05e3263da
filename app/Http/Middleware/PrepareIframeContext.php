<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;
use Livewire;
use Symfony\Component\HttpFoundation\Response;

class PrepareIframeContext
{
    public function handle(Request $request, Closure $next): Response
    {
        $route = $request->route();
        $iframe = false;

        if ($route) {
            if ($route->getName() === 'sharing.index') {
                $iframe = true;
            } elseif (Livewire::isDefinitelyLivewireRequest()) {
                $iframe = \str_starts_with(Livewire::originalPath(), 'sharing/');
            }
        }

        if ($iframe) {
            $request->attributes->set('sent_from_iframe', true);
        }

        View::share('iframe', $iframe);

        return $next($request);
    }
}

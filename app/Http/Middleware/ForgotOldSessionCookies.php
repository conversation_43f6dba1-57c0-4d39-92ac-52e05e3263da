<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Str;

/**
 * @SuppressWarnings(PHPMD.MissingImport)
 */
class ForgotOldSessionCookies
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        /* @phpstan-ignore-next-line */
        Cookie::queue(new \Symfony\Component\HttpFoundation\Cookie(Str::slug(config('app.name'), '_') . '_session', null, now()->subYear(1)->timestamp, '/'));
        /* @phpstan-ignore-next-line */
        Cookie::queue(new \Symfony\Component\HttpFoundation\Cookie('XSRF-TOKEN', null, now()->subYear(1)->timestamp, '/'));
        /* @phpstan-ignore-next-line */
        Cookie::queue(new \Symfony\Component\HttpFoundation\Cookie('remember_web_' . sha1(\Illuminate\Auth\SessionGuard::class), null, now()->subYear(1)->timestamp, '/'));

        return $next($request);
    }
}

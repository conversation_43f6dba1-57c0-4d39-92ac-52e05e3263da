<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Session\Middleware\StartSession as Middleware;

class StartSession extends Middleware
{
    /**
     * {@inheritdoc}
     */
    public function handle($request, Closure $next)
    {
        if (
            $this->sessionConfigured() &&
            $request->attributes->getBoolean('sent_from_iframe')
        ) {
            // Dans le cas d'une requête supposée avoir été envoyée depuis
            // une iframe, on s'assure de ne créer aucune nouvelle session,
            // ni d'être authentifié.
            $this->manager->setDefaultDriver('null');
        }

        return parent::handle($request, $next);
    }

    /**
     * {@inheritdoc}
     */
    protected function sessionIsPersistent(?array $config = null)
    {
        $config = $config ?: $this->manager->getSessionConfig();

        return parent::sessionIsPersistent($config) && $config['driver'] !== 'null';
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class ShareJavascriptToView
{
    /** @throws \Exception */
    public function handle(Request $request, Closure $next): mixed
    {
        $gdprPage = pages()->where('unique_key', 'gdpr_page')->first();
        share([
            'domain' => request()->getHost(),
            'locale' => app()->getLocale(),
            'notify' => __('notify'),
            'gdpr_page_url' => $gdprPage ? route('page.show', $gdprPage) : null,
            'cookie_categories' => cookieCategories(),
            'is_desktop' => \Browser::isDesktop(),
            'is_mobile' => \Browser::isMobile(),
        ]);

        return $next($request);
    }
}

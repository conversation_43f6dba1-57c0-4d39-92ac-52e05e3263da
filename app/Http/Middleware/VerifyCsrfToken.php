<?php

namespace App\Http\Middleware;

use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken as Middleware;

class VerifyCsrfToken extends Middleware
{
    /**
     * The URIs that should be excluded from CSRF verification.
     *
     * @var array<int, string>
     */
    protected $except = [];

    /**
     * The URIs that should be excluded from CSRF verification when the request
     * has been sent from an iframe.
     *
     * @var array<int, string>
     */
    protected $exceptWhenIframe = [
        'livewire/message/audio.play-pause',
        'livewire/message/footer.audio-player',
        'livewire/message/footer.current-audio-details',
        'livewire/message/footer.radio-station-stream-selector',
        'livewire/message/map.content-rail',
        'livewire/message/map.filter-widget',
        'livewire/message/map.index',
        'livewire/message/map.info-panel',
        'livewire/message/map.info-panel.event-info',
        'livewire/message/map.info-panel.news-info',
        'livewire/message/map.info-panel.playlist-info',
        'livewire/message/map.info-panel.performer-info',
        'livewire/message/map.info-panel.podcast-info',
        'livewire/message/map.map',
        'livewire/message/mobile-player.current-audio-details',
        'livewire/message/modal',
        'livewire/message/modals.share',
        'livewire/message/share.copy-iframe',
        'livewire/message/share.copy-link',
    ];

    /**
     * Determine if the request has a URI that should pass through CSRF verification.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function inExceptArray($request)
    {
        if (parent::inExceptArray($request)) {
            return true;
        }

        if ($request->attributes->getBoolean('sent_from_iframe')) {
            // Nous sommes dans le cas d'une requête envoyée depuis une iframe,
            // on considère donc les exceptions spécifiques au cas de figure.
            foreach ($this->exceptWhenIframe as $except) {
                if ($except !== '/') {
                    $except = trim($except, '/');
                }

                if ($request->fullUrlIs($except) || $request->is($except)) {
                    // Inutile d'ajouter un cookie XSRF-TOKEN à la réponse dans
                    // le contexte d'une iframe (requête issue d'une iframe).
                    $this->addHttpCookie = false;

                    return true;
                }
            }
        }

        return false;
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Sentry\State\Scope;

use function Sentry\configureScope;

class SentryContext
{
    public function handle(Request $request, Closure $next): mixed
    {
        if (app()->bound('sentry')) {
            configureScope(function (Scope $scope): void {
                if (Auth::check()) {
                    $scope->setUser(Auth::user()->toArray());
                }
                $scope->setExtra('session', session()->all());
            });
        }

        return $next($request);
    }
}

<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class ForbidAccessToPrivatePlaylist
{
    public function handle(Request $request, Closure $next): Response|RedirectResponse
    {
        if ($request->playlist->user_id) {
            alert()->html('Erreur', 'Action interdite car la playlist est privée.', 'error')->showConfirmButton();

            return back();
        }

        return $next($request);
    }
}

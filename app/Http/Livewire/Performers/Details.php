<?php

namespace App\Http\Livewire\Performers;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Audio\SongHistory;
use App\Models\Events\Event;
use App\Models\News\NewsArticle;
use App\Models\Performers\Album;
use App\Models\Performers\Performer;
use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use hisorange\BrowserDetect\Facade as Browser;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Livewire\Component;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Details extends Component
{
    public bool $initialized = false;

    public bool $dedicationServiceInitialized = false;

    public Performer $performer;

    public Collection $headerBackgroundCoverUrls;

    public bool $lastAlbumIsPlaying = false;

    public ?int $lastAlbumId = null;

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllAudioSources = false;

    public RadioStation $selectedRadioStation;

    public Collection $newsArticles;

    public Collection $podcasts;

    public Collection $playlists;

    public array $selectedAudioSourceIds = [];

    public Collection $events;

    public Collection $lastProducedSongs;

    public Collection $topStreamedSongs;

    public bool $dedicationDisabled = false;

    public array $dedicatableSongWinmediaIds = [];

    public Collection $dedicatableSongs;

    public Collection $albums;

    public Collection $similarPerformers;

    public bool $hasPerformerUrls = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'radio:station:universe:updated' => 'updateSelectedRadio',
        'songs:dedicatable:update' => 'setDedicatableSongIds',
    ];

    public function mount(): void
    {
        $this->headerBackgroundCoverUrls = collect();
        $this->newsArticles = collect();
        $this->podcasts = collect();
        $this->playlists = collect();
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->setPlayingStatus();
        $this->events = collect();
        $this->lastProducedSongs = collect();
        $this->topStreamedSongs = collect();
        $this->dedicatableSongs = collect();
        $this->albums = collect();
        $this->similarPerformers = collect();
    }

    public function setSelectedRadioStation(int $radioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $radioStationId)->firstOrFail();
    }

    /** @throws \Exception */
    public function updateSelectedRadio(int $radioStationId): void
    {
        $this->setSelectedRadioStation($radioStationId);
        $this->emitSelf('songs:dedicatable:update');
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    protected function setPlayingStatus(): void
    {
        $this->lastAlbumIsPlaying = $this->playedAudioSourceClass === Album::class
            && $this->lastAlbumId !== null
            && $this->playedAudioSourceId === $this->lastAlbumId
            && ! $this->pauseAllAudioSources;
    }

    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'performer_details',
            routeParams: ['performer' => $this->performer],
            livewireComponent: $this
        );
        $this->initialized = true;
        $this->emitSelf('songs:dedicatable:update');
    }

    public function render(): View
    {
        $this->performer->loadMissing([
            'albums.songs.performerRelationship',
            'songs.albumRelationship',
            'songs.performerRelationship',
            'songs.labelRelationship',
            'songs.playlists',
            'albums.contentLocations',
        ]);
        if ($this->initialized) {
            $this->setCoversPatchworkHeaderBackgroundData();
            $this->emit('rail:load', ['railName' => 'performer-details-dedications-rail']);
            $this->emit('rail:load', ['railName' => 'performer-details-related-news-podcasts-playlists-rail']);
            $this->emit('rail:load', ['railName' => 'performer-details-related-events-rail']);
            $this->emit('rail:load', ['railName' => 'performer-details-similar-performers-rail']);
            $isPgsql = config('database.default') === 'pgsql';
            $this->newsArticles = $this->getRelatedNewsArticles($isPgsql);
            $this->podcasts = $this->getRelatedPodcasts($isPgsql);
            $this->playlists = $this->getRelatedPlaylists();
            $this->events = $this->getRelatedEvents($isPgsql);
            $this->lastProducedSongs = $this->getLastProducedSongs();
            $this->topStreamedSongs = $this->getTopStreamedSongs();
            $this->albums = $this->getRelatedAlbums();
            $this->similarPerformers = $this->getSimilarPerformers();
            $this->updateAudioSourceStatuses('newsArticles');
            $this->updateAudioSourceStatuses('podcasts');
            $this->updateAudioSourceStatuses('playlists');
            $this->updateAudioSourceStatuses('lastProducedSongs');
            $this->updateAudioSourceStatuses('topStreamedSongs');
            $this->updateAudioSourceStatuses('albums');
            $this->updateAudioSourceStatuses('dedicatableSongs');
            $this->hasPerformerUrls = ($this->performer->detail->url_web
                || $this->performer->detail->url_wiki
                || $this->performer->detail->url_bandcamp
                || $this->performer->detail->url_discogs
                || $this->performer->detail->url_instagram
                || $this->performer->detail->url_facebook
                || $this->performer->detail->url_youtube) ? true : false;
        }

        return view('livewire.performers.details');
    }

    protected function setCoversPatchworkHeaderBackgroundData(): void
    {
        if ($this->performer->albums->isEmpty()) {
            $this->headerBackgroundCoverUrls = collect();

            return;
        }

        $performerThumb = $this->performer->cover_thumb;
        $albumLimit = Browser::isDesktop() ? 5 : 3;

        $this->headerBackgroundCoverUrls = $this->performer
            ->albums
            ->take($albumLimit)
            ->pluck('cover_thumb');

        if ($performerThumb !== asset('storage/winmedia/performers_thumbnail/noimage.jpg')) {
            $this->headerBackgroundCoverUrls->prepend($performerThumb);
            $this->headerBackgroundCoverUrls = $this->headerBackgroundCoverUrls->take($albumLimit);
        }
    }

    protected function getRelatedNewsArticles(bool $isPgsql = false): Collection
    {
        if ($isPgsql) {
            $performerName = Str::lower(Str::replace("'", "''", Str::replace('"', '', $this->performer->name)));

            return NewsArticle::where('published_at', '<=', Date::now())
                ->whereRaw("'" . $performerName . "' = ANY (regexp_split_to_array(LOWER(tags),', '))")
                ->where('active', true)
                ->orderByDesc('published_at')
                ->limit(7)
                ->get();
        }

        return NewsArticle::where('published_at', '<=', Date::now())
            ->whereRaw(
                'JSON_CONTAINS(CONCAT(\'["\', REPLACE(LOWER(tags), \', \', \'","\'), \'"]\'), ?)',
                '"' . Str::lower(Str::replace('"', '', $this->performer->name)) . '"'
            )
            ->where('active', true)
            ->orderByDesc('published_at')
            ->limit(7)
            ->get();
    }

    protected function getRelatedPodcasts(bool $isPgsql = false): Collection
    {
        if ($isPgsql) {
            $performerName = Str::lower(Str::replace("'", "''", Str::replace('"', '', $this->performer->name)));

            return Podcast::hasAudio()
                ->with('program')
                ->where('published_at', '<=', Date::now())
                ->where('active', true)
                ->whereRaw("'" . $performerName . "' = ANY (regexp_split_to_array(LOWER(tags),', '))")
                ->orderByDesc('published_at')
                ->limit(7)
                ->get();
        }

        return Podcast::hasAudio()
            ->with('program')
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->whereRaw(
                'JSON_CONTAINS(CONCAT(\'["\', REPLACE(LOWER(tags), \', \', \'","\'), \'"]\'), ?)',
                '"' . Str::lower(Str::replace('"', '', $this->performer->name)) . '"'
            )
            ->orderByDesc('published_at')
            ->limit(7)
            ->get();
    }

    protected function getRelatedPlaylists(): Collection
    {
        return Playlist::with(['songs'])
            ->where('published_at', '<=', Date::now())
            ->where(static fn (Builder $builder) => $builder->where('unpublished_at', '<=', Date::now())
                ->orWhere('unpublished_at', null))
            ->whereIn('id', $this->performer->playlists->pluck('id'))
            ->where('active', true)
            ->orderByDesc('published_at')
            ->limit(7)
            ->get();
    }

    protected function getRelatedEvents(bool $isPgsql = false): Collection
    {
        if ($isPgsql) {
            $performerName = Str::lower(Str::replace("'", "''", Str::replace('"', '', $this->performer->name)));

            return Event::where('active', true)
                ->where('ended_at', '>=', Date::now()->subDay()->startOfDay())
                ->whereRaw("'" . $performerName . "' = ANY (regexp_split_to_array(LOWER(tags),', '))")
                ->orderByDesc('started_at')
                ->limit(3)
                ->get();
        }

        return Event::where('active', true)
            ->where('ended_at', '>=', Date::now()->subDay()->startOfDay())
            ->whereRaw(
                'JSON_CONTAINS(CONCAT(\'["\', REPLACE(LOWER(tags), \', \', \'","\'), \'"]\'), ?)',
                '"' . Str::lower(Str::replace('"', '', $this->performer->name)) . '"'
            )
            ->orderByDesc('started_at')
            ->limit(3)
            ->get();
    }

    protected function getLastProducedSongs(): Collection
    {
        return $this->performer->songs->sortByDesc('albumRelationship.published_at')->take(5);
    }

    protected function getTopStreamedSongs(): Collection
    {
        return SongHistory::with(['song.performerRelationship', 'song.albumRelationship', 'song.labelRelationship'])
            ->select('imedia')
            ->addSelect(DB::raw('count(*) as stream_count'))
            ->whereRelation('song', 'performer', $this->performer->name)
            ->groupBy('imedia')
            ->orderByDesc('stream_count')
            ->limit(5)
            ->get()
            ->pluck('song');
    }

    protected function getRelatedAlbums(): Collection
    {
        $albums = $this->performer->albums->sortByDesc('published_at')->take(20);

        if ($albums->isEmpty()) {
            $this->lastAlbumId = null;
        } else {
            $this->lastAlbumId = $albums->first()->id;
        }

        return $albums;
    }

    protected function getSimilarPerformers(): Collection
    {
        $similarPerformerNames = $this->performer
            ->loadMissing(['songs.albumRelationship'])
            ->albums
            ->pluck('songs.*._artistes_similaires')
            ->flatten()
            ->filter()
            ->map(static fn (string $performers) => explode('/', $performers))
            ->flatten()
            ->map(static fn (string $performer) => Str::of($performer)->lower()->ascii()->trim())
            ->unique()
            ->toArray();
        if (empty($similarPerformerNames)) {
            return collect();
        }
        $query = Performer::with(['albums', 'songs.albumRelationship.songs']);
        foreach ($similarPerformerNames as $similarPerformerName) {
            $query->orWhere('name', config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE', $similarPerformerName);
        }

        return $query->limit(8)->get();
    }

    protected function updateAudioSourceStatuses(string $type): void
    {
        if ($this->{$type}) {
            if ($this->pauseAllAudioSources) {
                $this->{$type} = $this->{$type}->map(function (NewsArticle|Podcast|Playlist|Song|Album $audioItem) {
                    $audioItem->selected = in_array($audioItem->id, $this->selectedAudioSourceIds, true);
                    $audioItem->playing = false;
                    if ($audioItem::class === Song::class) {
                        $audioItem->dedicatable = in_array($audioItem->imedia, $this->dedicatableSongWinmediaIds, true);
                    } else {
                        $audioItem->dedicatable = false;
                    }

                    return $audioItem;
                });

                return;
            }
            $this->selectedAudioSourceIds = [];
            $this->{$type} = $this->{$type}->map(function (NewsArticle|Podcast|Playlist|Song|Album $audioItem) {
                $isPlaying = $audioItem::class === $this->playedAudioSourceClass
                    && $audioItem->id === $this->playedAudioSourceId;
                if ($isPlaying) {
                    $this->selectedAudioSourceIds[] = $audioItem->id;
                }
                $audioItem->selected = $isPlaying;
                $audioItem->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
                $audioItem->dedicatable = false;
                if ($audioItem::class === Song::class) {
                    $audioItem->dedicatable = in_array($audioItem->imedia, $this->dedicatableSongWinmediaIds, true);
                } else {
                    $audioItem->dedicatable = false;
                }

                return $audioItem;
            });
        }
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \JsonException
     */
    public function setDedicatableSongIds(): void
    {
        if (! Auth::check()) {
            $this->dedicationServiceInitialized = true;

            return;
        }
        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::user()->id,
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
        }

        $songWinmediaIds = $this->performer
            ->songs()
            ->limit(500)
            ->pluck('imedia')
            ->toArray();

        if (count(array_filter($songWinmediaIds)) > 0) {
            $this->dedicatableSongWinmediaIds = app(DedicationService::class)->verifySongsEligibility(
                $this->selectedRadioStation->winmedia_id,
                $songWinmediaIds
            );
        } else {
            $this->dedicatableSongWinmediaIds = [];
        }

        $this->dedicatableSongs = Song::whereIn('imedia', $this->dedicatableSongWinmediaIds)
            ->with(['performerRelationship', 'albumRelationship'])
            ->limit(30)
            ->get();

        $this->dedicationServiceInitialized = true;
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->setPlayingStatus();
        $this->pauseAllAudioSources = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllAudioSources = true;
        $this->setPlayingStatus();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllAudioSources = false;
        $this->setPlayingStatus();
    }
}

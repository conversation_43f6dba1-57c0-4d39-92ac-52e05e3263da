<?php

namespace App\Http\Livewire;

use App\Exceptions\InvalidRouteDefinition;
use App\Services\Router\RouterService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Request;
use Illuminate\View\View;
use Livewire\Component;

class App extends Component
{
    public ?string $path = null;

    public array $params = [];

    public ?string $css = null;

    public ?string $js = null;

    public ?string $userTheme = null;

    public bool $desktopProfileNav = true;

    protected $listeners = ['template:load' => 'loadTemplate'];

    public int $initialRadioStationWinmediaID = 0;

    public function mount(): void
    {
        // Hack to avoid tests to fail with no explanation.
        // ToDo: to check again when upgrading to Laravle 9
        if (! app()->environment('testing')) {
            $initialUrl = urldecode(Request::getRequestUri());
            app(SeoMetaService::class)->generateSeoMetaOnAppLoad($initialUrl);
        }

        if (Auth::user()) {
            $this->userTheme = Auth::user()->settings->dark_mode_enum->value;
        }

        if (array_key_exists('station', request()->query())) {
            $this->initialRadioStationWinmediaID = intval(request()->query()['station']);
        }
    }

    public function init(): void
    {
        // Initialize player status to false
        app(UserJourneysService::class)->setPlayerPlayingStatus(false);
        if (Auth::user()?->settings->use_geolocation) {
            if ($this->initialRadioStationWinmediaID === 0) {
                $this->emit('geolocation:position:request');
            }
        } elseif (Auth::user()?->settings->default_radio_station_id === null) {
            app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId(null);
            $this->emit('modal:show', 'choose-default-radio-station');
        } elseif (Auth::user()->settings->default_radio_station_id) {
            app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId(Auth::user()->settings->default_radio_station_id);
        } else {
            app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId(null);
        }
        app(UserJourneysService::class)->setSelectedDefaultSettingWebradioId(Auth::user()?->settings->default_webradio_id);
        if (settings()->login_modal_on_init) {
            $this->emit('modal:show', 'login');
        }
    }

    /** @throws \Exception */
    public function loadTemplate(array $route, array $params): void
    {
        if (! isset($route['view'])) {
            throw new InvalidRouteDefinition('Missing required "view" attribute.');
        }

        $this->path = $route['view'];
        $this->params = app(RouterService::class)->instantiateParamBindings($params);
        $this->desktopProfileNav = $route['desktopProfileNav'] ?? true;

        if (! empty($route['css'])) {
            $this->css = mix($route['css']);
        }

        $this->emit('scroll:top', '#template');
    }

    public function render(): View
    {
        return view('livewire.app', ['authenticated' => Auth::check()])
            ->extends('layouts.front.app', [
                'css' => $this->css,
                'userTheme' => $this->userTheme,
            ])
            ->section('template');
    }
}

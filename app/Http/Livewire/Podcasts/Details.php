<?php

namespace App\Http\Livewire\Podcasts;

use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Livewire\Component;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Details extends Component
{
    public Podcast $podcast;

    public Collection $podcastsOfProgram;

    public Collection $songs;

    public bool $initialized = false;

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllPodcasts = false;

    public bool $pauseAllSongs = false;

    public RadioStation $selectedRadioStation;

    public array $selectedPodcastIds = [];

    public bool $dedicationDisabled = false;

    public array $selectedSongIds = [];

    public array $dedicatableSongWinmediaIds = [];

    public bool $podcastIsPlaying = false;

    public ?string $url = null;

    public bool $subscribed = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'radio:station:universe:updated' => 'updateSelectedRadio',
        'songs:dedicatable:update' => 'setDedicatableSongIds',
    ];

    public function mount(Request $request): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->setPlayingStatus();
        if (Auth::check()) {
            $this->subscribed = Auth::user()->subscribedPrograms->contains($this->podcast->program_id);
        }
    }

    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'podcast_details',
            routeParams: ['podcast' => $this->podcast],
            livewireComponent: $this
        );
        $this->initialized = true;
        $this->emitSelf('songs:dedicatable:update');
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->setPodcastsOfProgram();
            $this->setSongs();
            $this->emit('rail:load', ['railName' => 'podcast-details-podcasts']);
        }

        return view('livewire.podcasts.details');
    }

    public function setSongs(): void
    {
        $this->songs = $this->podcast->songs()->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])->get();
        $this->updateSongStatuses();
    }

    protected function setPodcastsOfProgram(): void
    {
        $this->podcastsOfProgram = Podcast::with(['media', 'program'])
            ->where('program_id', $this->podcast->program_id)
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->hasAudio()
            ->latest('published_at')
            ->limit(10)
            ->get();
        $this->updatePodcastsOfProgramStatuses();
    }

    protected function updateSongStatuses(): void
    {
        if ($this->pauseAllSongs) {
            $this->songs = $this->songs->map(function (Song $song) {
                $song->selected = in_array($song->id, $this->selectedSongIds, true);
                $song->playing = false;
                $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);

                return $song;
            });

            return;
        }
        $this->selectedSongIds = [];
        $this->songs = $this->songs->map(function (Song $song) {
            $isPlaying = $this->playedAudioSourceClass === Song::class
                        && $this->playedAudioSourceId === $song->id;
            if ($isPlaying) {
                $this->selectedSongIds[] = $song->id;
            }
            $song->selected = $isPlaying;
            $song->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
            $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);

            return $song;
        });
    }

    protected function updatePodcastsOfProgramStatuses(): void
    {
        if ($this->pauseAllPodcasts) {
            $this->podcastsOfProgram = $this->podcastsOfProgram->map(function (Podcast $podcast) {
                $podcast->selected = in_array($podcast->id, $this->selectedPodcastIds, true);
                $podcast->playing = false;

                return $podcast;
            });

            return;
        }
        $this->selectedPodcastIds = [];
        $this->podcastsOfProgram = $this->podcastsOfProgram->map(function (Podcast $podcast) {
            $isPlaying = $podcast::class === $this->playedAudioSourceClass
                && $podcast->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedPodcastIds[] = $podcast->id;
            }
            $podcast->selected = $isPlaying;
            $podcast->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $podcast;
        });
    }

    public function toggleSubscribed(): void
    {
        $this->subscribed = ! $this->subscribed;
        Auth::user()->subscribedPrograms()->toggle($this->podcast->program_id);
        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'Votre ' . ($this->subscribed ? 'abonnement' : 'désabonnement') . ' a été pris en compte.',
        ]);
    }

    public function setSelectedRadioStation(int $radioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $radioStationId)->firstOrFail();
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \JsonException
     */
    public function setDedicatableSongIds(): void
    {
        if (! Auth::check()) {
            return;
        }
        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::user()->id,
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
        }
        $songWinmediaIds = $this->songs->pluck('imedia')->toArray();
        if (count(array_filter($songWinmediaIds)) > 0) {
            $this->dedicatableSongWinmediaIds = app(DedicationService::class)->verifySongsEligibility(
                $this->selectedRadioStation->winmedia_id,
                $songWinmediaIds
            );
        } else {
            $this->dedicatableSongWinmediaIds = [];
        }
    }

    protected function setPlayedAudioSource(
        ?string $audioSourceClass,
        ?int $audioSourceId,
    ): void {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    protected function setPlayingStatus(): void
    {
        $this->podcastIsPlaying = $this->playedAudioSourceClass === Podcast::class
            && $this->playedAudioSourceId === $this->podcast->id;
    }

    /** @throws \Exception */
    public function updateSelectedRadio(int $radioStationId): void
    {
        $this->setSelectedRadioStation($radioStationId);
        $this->emitSelf('songs:dedicatable:update');
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->setPlayingStatus();
        $this->pauseAllPodcasts = false;
        $this->pauseAllSongs = false;
    }

    public function playerHasPaused(): void
    {
        $this->podcastIsPlaying = false;
        $this->pauseAllPodcasts = true;
        $this->pauseAllSongs = true;
    }

    public function playerIsResuming(): void
    {
        $this->setPlayingStatus();
        $this->pauseAllPodcasts = false;
        $this->pauseAllSongs = false;
    }
}

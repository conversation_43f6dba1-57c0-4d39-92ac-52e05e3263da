<?php

namespace App\Http\Livewire\Podcasts;

use App\Models\Audio\Podcast;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Rail extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = true;

    public ?string $title = null;

    public string $railName;

    public Collection $podcasts;

    public ?string $showAllRoute = null;

    public array $showAllParams = [];

    public array $selectedPodcastIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllPodcasts = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function init(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->updateAudioPodcastsStatuses();
        $this->initialized = true;
    }

    public function render(): View
    {
        return view('livewire.podcasts.rail');
    }

    protected function updateAudioPodcastsStatuses(): void
    {
        if ($this->pauseAllPodcasts) {
            $this->podcasts = $this->podcasts->map(function (Podcast $podcast) {
                $podcast->selected = in_array($podcast->id, $this->selectedPodcastIds, true);
                $podcast->playing = false;

                return $podcast;
            });

            return;
        }
        $this->selectedPodcastIds = [];
        $this->podcasts = $this->podcasts->map(function (Podcast $podcast) {
            $isPlaying = $podcast::class === $this->playedAudioSourceClass
                && $podcast->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedPodcastIds[] = $podcast->id;
            }
            $podcast->selected = $isPlaying;
            $podcast->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $podcast;
        });
        $this->emit('rail:load', ['railName' => $this->railName]);
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllPodcasts = false;
        $this->updateAudioPodcastsStatuses();
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllPodcasts = true;
        $this->updateAudioPodcastsStatuses();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllPodcasts = false;
        $this->updateAudioPodcastsStatuses();
    }
}

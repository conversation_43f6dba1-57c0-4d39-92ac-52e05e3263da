<?php

namespace App\Http\Livewire\Podcasts;

use App\Models\Audio\Thematic as ThematicModel;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class ThematicsRail extends Component
{
    public bool $initialized = false;

    public string $title;

    public Collection $thematics;

    public ?int $selectedId = null;

    public string $railName;

    protected $listeners = ['thematic:selected' => 'thematicSelected'];

    public function render(): View
    {
        if ($this->initialized) {
            $this->updateThematicsStatuses();
        }

        return view('livewire.podcasts.thematics-rail');
    }

    protected function updateThematicsStatuses(): void
    {
        $this->thematics = $this->thematics->map(function (ThematicModel $thematic) {
            $thematic->selected = $thematic->id === $this->selectedId;

            return $thematic;
        });
    }

    public function init(): void
    {
        $this->initialized = true;
        $this->emit('rail:load', ['railName' => $this->railName]);
    }

    public function thematicSelected(int $thematicId): void
    {
        $this->selectedId = $this->selectedId !== $thematicId ? $thematicId : null;
        $this->updateThematicsStatuses();
        $this->emitTo(Index::class, 'thematic:selected', $this->selectedId);
    }
}

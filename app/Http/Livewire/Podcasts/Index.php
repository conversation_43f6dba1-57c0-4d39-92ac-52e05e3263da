<?php

namespace App\Http\Livewire\Podcasts;

use App\Models\Audio\Podcast;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\View\View;
use Livewire\Component;

class Index extends Component
{
    public bool $initialized = false;

    public Collection $lastPodcasts;

    public Collection $subscribedPodcasts;

    public Collection $replayPodcasts;

    public Collection $originalPodcasts;

    public Collection $thematics;

    public ?int $selectedThematicId = null;

    public ?int $selectedRadioStationId = null;

    protected $listeners = [
        'radio:station:universe:updated' => 'setSelectedRadioStationId',
        'thematic:selected' => 'thematicSelected',
    ];

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'podcasts', livewireComponent: $this);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStationId($selectedRadioStationId);
        $this->thematics = thematics();
        $this->initialized = true;
    }

    public function setSelectedRadioStationId(int $selectedRadioStationId): void
    {
        $this->selectedRadioStationId = $selectedRadioStationId;
    }

    /** @throws \Exception */
    public function render(): View
    {
        if ($this->initialized) {
            $this->setPodcasts();
        }

        return view('livewire.podcasts.index');
    }

    /** @throws \Exception */
    public function setPodcasts(): void
    {
        /** @var \Illuminate\Support\Collection $podcasts */
        $podcasts = Podcast::hasAudio()
            ->with(['media', 'program'])
            ->when(
                $this->selectedThematicId,
                fn (Builder $podcast) => $podcast->where('thematic_id', $this->selectedThematicId)
            )
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereRelation(
                    'radioStations',
                    fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStationId)
                )->orWhereDoesntHave('radioStations');
            })
            ->get();
        $this->lastPodcasts = $podcasts->take(4);
        $subscribedPodcastIds = Auth::check()
            ? Auth::user()
                ->load('subscribedPrograms.podcasts.media')
                ->subscribedPrograms
                ->pluck('podcasts')
                ->flatten()
                ->filter(fn (Podcast $podcast) => $podcast->audio_stream['mp3'])
                ->take(15)
                ->pluck('id')
                ->toArray()
            : collect();
        $this->subscribedPodcasts = $podcasts->whereIn('id', $subscribedPodcastIds);
        $this->replayPodcasts = $podcasts->where('type', Podcast::TYPE_REPLAY)->take(15);
        $this->originalPodcasts = $podcasts->where('type', Podcast::TYPE_ORIGINAL)->take(15);
    }

    public function thematicSelected(?int $thematicId): void
    {
        $this->selectedThematicId = $this->selectedThematicId === $thematicId ? null : $thematicId;
    }
}

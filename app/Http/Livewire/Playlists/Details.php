<?php

namespace App\Http\Livewire\Playlists;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\News\NewsArticle;
use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Elasticsearch\PlaylistIndexService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Browser;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithPagination;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Details extends Component
{
    use WithPagination;

    public bool $initialized = false;

    public ?Playlist $playlist = null;

    public Collection $songs;

    public Collection $headerSongs;

    public int $totalDuration = 0;

    public int $totalCount = 0;

    protected $songsLinks;

    public int $elementsPerPage = 100;

    public Collection $headerBackgroundCoverUrls;

    public bool $playlistIsPlaying = false;

    public array $selectedAudioSourceIds = [];

    public array $selectedSongsIds = [];

    public RadioStation $selectedRadioStation;

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public ?string $playedSubAudioSourceClass = null;

    public ?int $playedSubAudioSourceId = null;

    public bool $pauseAllAudioSources = false;

    public ?string $firstCoverPath = null;

    public bool $dedicationDisabled = false;

    public array $dedicatableSongWinmediaIds = [];

    public Collection $newsArticlesRelated;

    public Collection $podcastsRelated;

    public Collection $playlistsRelated;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:sub:audio:source:updated' => 'updatePlayedSubAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'playlist:destroy:confirmed' => 'destroyPlaylistConfirmed',
        'radio:station:universe:updated' => 'updateSelectedRadio',
        'songs:dedicatable:update' => 'setDedicatableSongIds',
        'playlist:updated' => '$refresh',
        'songs:favorites:updated' => '$refresh',
    ];

    /** @throws \Exception */
    public function mount(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        [
            'played_sub_audio_source_class' => $subAudioSourceClass,
            'played_sub_audio_source_id' => $subAudioSourceId,
        ] = app(UserJourneysService::class)->getPlayedSubAudioSource();
        $this->setPlayedSubAudioSource($subAudioSourceClass, $subAudioSourceId);
        $this->setPlayingStatus();
        $this->songs = collect();
        $this->headerSongs = collect();
        $this->newsArticlesRelated = collect();
        $this->podcastsRelated = collect();
        $this->playlistsRelated = collect();
        $this->headerBackgroundCoverUrls = collect();
    }

    public function setSelectedRadioStation(int $radioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $radioStationId)->firstOrFail();
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    protected function setPlayedSubAudioSource(
        ?string $subAudioSourceClass,
        ?int $subAudioSourceId,
    ): void {
        $this->playedSubAudioSourceClass = $subAudioSourceClass;
        $this->playedSubAudioSourceId = $subAudioSourceId;
    }

    protected function setPlayingStatus(): void
    {
        $this->playlistIsPlaying = $this->playedAudioSourceClass === Playlist::class
            && $this->playedAudioSourceId === ($this->playlist?->id ?: 0)
            && ! $this->pauseAllAudioSources;
    }

    /** @throws \Exception */
    public function init(): void
    {
        $this->initialized = true;
        $this->emitSelf('songs:dedicatable:update');
    }

    /** @throws \Exception */
    public function updateSelectedRadio(int $radioStationId): void
    {
        $this->setSelectedRadioStation($radioStationId);
        $this->emitSelf('songs:dedicatable:update');
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \JsonException
     */
    public function setDedicatableSongIds(): void
    {
        if (! Auth::check()) {
            return;
        }
        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::user()->id,
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
        }
        $songWinmediaIds = $this->songs->pluck('imedia')->toArray();
        if (count(array_filter($songWinmediaIds)) > 0) {
            $this->dedicatableSongWinmediaIds = app(DedicationService::class)->verifySongsEligibility(
                $this->selectedRadioStation->winmedia_id,
                $songWinmediaIds
            );
        } else {
            $this->dedicatableSongWinmediaIds = [];
        }
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->setPlayingStatus();
        $this->pauseAllAudioSources = false;
    }

    public function updatePlayedSubAudioSource(
        string $subAudioSourceClass,
        int $subAudioSourceId
    ): void {
        $this->setPlayedSubAudioSource($subAudioSourceClass, $subAudioSourceId);
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllAudioSources = true;
        $this->setPlayingStatus();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllAudioSources = false;
        $this->setPlayingStatus();
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->emit('rail:load', ['railName' => 'playlist-details-related-news-podcasts-playlists-rail']);
            $this->setSongs();
            $this->setRelatedContents();
            app(SeoMetaService::class)->generateSeoMeta(
                routeKey: $this->playlist ? 'playlist_details' : 'favorite_songs',
                routeParams: ['playlist' => $this->playlist],
                livewireComponent: $this
            );
        }

        return view('livewire.playlists.details', ['links' => $this->songsLinks]);
    }

    public function setSongs(): void
    {
        if ($this->playlist) {
            if ($this->playlist->user_id) {
                // Playlist utilisateur
                $songs = $this->playlist->songs()->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])->paginate($this->elementsPerPage);
                $this->headerSongs = $this->playlist->songs()->take(5)->get();
                $durations = $this->playlist->songs()->pluck('duration');
            } else {
                // Playlist publique
                $songs = $this->playlist->songsAllTypes()->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])->paginate($this->elementsPerPage);
                $this->headerSongs = $this->playlist->songsAllTypes()->take(5)->get();
                $durations = $this->playlist->songsAllTypes()->pluck('duration');
            }
        } else {
            // Titres aimés
            if (Auth::check()) {
                $songs = Auth::user()->favoriteSongs()->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])->paginate($this->elementsPerPage);
                $this->headerSongs = Auth::user()->favoriteSongs()->take(5)->get();
                $durations = Auth::user()->favoriteSongs()->pluck('duration');
            } else {
                $songs = new LengthAwarePaginator([], 0, $this->elementsPerPage);
                $this->headerSongs = collect();
                $durations = collect();
            }
        }
        $this->totalCount = $durations->count();
        $this->totalDuration = $durations->sum();
        $this->songsLinks = $songs->links();
        $this->songs = collect($songs->items());
        $this->setCoversPatchworkHeaderBackgroundData();
        $this->updateSongStatuses();
    }

    protected function setCoversPatchworkHeaderBackgroundData(): void
    {
        if ($this->headerSongs->isEmpty()) {
            $this->headerBackgroundCoverUrls = collect();

            return;
        }
        $playlistPatchwork = $this->playlist?->getFirstMediaUrl('cover', 'patchwork');
        if ($playlistPatchwork) {
            $this->headerBackgroundCoverUrls = collect([$playlistPatchwork])->merge($this->headerSongs
                ->take(Browser::isDesktop() ? 4 : 2)
                ->pluck('cover_thumb'));
            $this->firstCoverPath = $this->playlist?->getFirstMediaPath('cover', 'patchwork');
        } else {
            $this->headerBackgroundCoverUrls = $this->headerSongs
                ->take(Browser::isDesktop() ? 5 : 3)
                ->pluck('cover_thumb');
            $this->firstCoverPath = Storage::path('public/winmedia/pochettes_thumbnail/'
                . $this->headerSongs->first()->id . '.jpg');
        }
    }

    protected function updateSongStatuses(): void
    {
        if ($this->pauseAllAudioSources) {
            $this->songs = $this->songs->map(function (Song $song) {
                $song->selected = in_array($song->id, $this->selectedSongsIds, true);
                $song->playing = false;
                $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);

                return $song;
            });

            return;
        }
        $this->selectedSongsIds = [];
        $this->songs = $this->songs->map(function (Song $song) {
            $songIsPlayingInPlaylistMode = $this->playedAudioSourceClass === Playlist::class
                && $this->playedAudioSourceId === ($this->playlist?->id ?: 0)
                && $this->playedSubAudioSourceClass === Song::class
                && $this->playedSubAudioSourceId === $song->id;
            $songIsPlayingInDirectMode = $this->playedAudioSourceClass === Song::class
                && $this->playedAudioSourceId === $song->id;
            $isPlaying = $songIsPlayingInPlaylistMode || $songIsPlayingInDirectMode;
            if ($isPlaying) {
                $this->selectedSongsIds[] = $song->id;
            }
            $song->selected = $isPlaying;
            $song->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
            $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);

            return $song;
        });
    }

    protected function updateRelatedAudioSourceStatuses(string $type): void
    {
        if ($this->{$type}) {
            if ($this->pauseAllAudioSources) {
                $this->{$type} = $this->{$type}->map(function (NewsArticle|Podcast|Playlist $audioItem) {
                    $audioItem->selected = in_array($audioItem->id, $this->selectedAudioSourceIds, true);
                    $audioItem->playing = false;

                    return $audioItem;
                });

                return;
            }
            $this->selectedAudioSourceIds = [];
            $this->{$type} = $this->{$type}->map(function (NewsArticle|Podcast|Playlist $audioItem) {
                $isPlaying = $audioItem::class === $this->playedAudioSourceClass
                    && $audioItem->id === $this->playedAudioSourceId;
                if ($isPlaying) {
                    $this->selectedAudioSourceIds[] = $audioItem->id;
                }
                $audioItem->selected = $isPlaying;
                $audioItem->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

                return $audioItem;
            });
        }
    }

    protected function setRelatedContents(): void
    {
        if (! $this->playlist || $this->playlist->user_id || ! $this->playlist->rail_displayed) {
            $this->newsArticlesRelated = collect();
            $this->podcastsRelated = collect();
            $this->playlistsRelated = collect();

            return;
        }
        $this->newsArticlesRelated = $this->getRelatedNewsArticles();
        $this->updateRelatedAudioSourceStatuses('newsArticlesRelated');
        $this->podcastsRelated = $this->getRelatedPodcasts();
        $this->updateRelatedAudioSourceStatuses('podcastsRelated');
        $this->playlistsRelated = $this->getRelatedPlaylists();
        $this->updateRelatedAudioSourceStatuses('playlistsRelated');
    }

    protected function getRelatedNewsArticles(): Collection
    {
        return NewsArticle::with(['media'])
            ->where('active', true)
            ->where('published_at', '<=', Date::now())
            ->orderBy('published_at', 'desc')
            ->get()
            ->filter(function (NewsArticle $newsArticle) {
                $currentPlaylistTags = array_map(
                    static fn (string $tag) => Str::of($tag)->trim()->lower(),
                    explode(',', $this->playlist->tags)
                );
                $newsArticleTags = array_map(
                    static fn (string $tag) => Str::of($tag)->trim()->lower(),
                    explode(',', $newsArticle->tags)
                );

                return ! empty(array_intersect($currentPlaylistTags, $newsArticleTags));
            })
            ->take(7);
    }

    protected function getRelatedPodcasts(): Collection
    {
        return Podcast::hasAudio()
            ->with('program')
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->orderByDesc('published_at')
            ->get()
            ->filter(function (Podcast $podcast) {
                $currentPlaylistTags = array_map(
                    static fn (string $tag) => Str::of($tag)->trim()->lower(),
                    explode(',', $this->playlist->tags)
                );
                $podcastTags = array_map(
                    static fn (string $tag) => Str::of($tag)->trim()->lower(),
                    explode(',', $podcast->tags)
                );

                return ! empty(array_intersect($currentPlaylistTags, $podcastTags));
            })
            ->take(7);
    }

    protected function getRelatedPlaylists(): Collection
    {
        return Playlist::with(['songs'])
            ->where('published_at', '<=', Date::now())
            ->where(static fn (Builder $builder) => $builder->where('unpublished_at', '<=', Date::now())
                ->orWhere('unpublished_at', null))
            ->where('active', true)
            ->where('id', '!=', $this->playlist->id)
            ->orderByDesc('published_at')
            ->get()
            ->filter(function (Playlist $playlist) {
                $currentPlaylistTags = array_map(
                    static fn (string $tag) => Str::of($tag)->trim()->lower(),
                    explode(',', $this->playlist->tags)
                );
                $playlistTags = array_map(
                    static fn (string $tag) => Str::of($tag)->trim()->lower(),
                    explode(',', $playlist->tags)
                );

                return ! empty(array_intersect($currentPlaylistTags, $playlistTags));
            })
            ->take(7);
    }

    public function destroyPlaylist(): void
    {
        $this->dispatchBrowserEvent('popin:confirm', [
            'html' => 'Êtes-vous sûr de vouloir supprimer cette playlist ?',
            'onConfirm' => "Livewire.emit('playlist:destroy:confirmed')",
        ]);
    }

    public function destroyPlaylistConfirmed(): void
    {
        if (! $this->playlist->user_id) {
            $this->dispatchBrowserEvent('popin:error', [
                'html' => 'La suppression d\'une playlist publique n\'est pas autorisée.',
            ]);

            return;
        }
        $this->playlist->delete();
        app(PlaylistIndexService::class)->delete($this->playlist);
        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'Votre playlist « ' . $this->playlist->title . ' » a été supprimée.',
        ]);
        $this->emitTo('router', 'nav:to', 'browse');
    }

    public function detachSongFromPlaylist(int $songId, string $songTitle): void
    {
        if (! $this->playlist->user_id) {
            $this->dispatchBrowserEvent('popin:error', [
                'html' => 'La suppression d\'un morceau d\'une playlist publique n\'est pas autorisée.',
            ]);

            return;
        }
        $this->playlist->songs()->detach($songId);
        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'Le titre « ' . $songTitle . ' » a été supprimé de la playlist.',
        ]);
        $this->emitSelf('playlist:updated');
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }
}

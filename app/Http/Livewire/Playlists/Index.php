<?php

namespace App\Http\Livewire\Playlists;

use App\Models\Audio\Playlist;
use App\Models\Radio\RadioStation;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\View\View;
use Livewire\Component;

class Index extends Component
{
    public bool $initialized = false;

    public Collection $publicPlaylists;

    public Collection $privatePlaylists;

    public Collection $favoriteSongs;

    public RadioStation $selectedRadioStation;

    public Collection $thematics;

    public ?int $selectedThematicId = null;

    protected $listeners = [
        'radio:station:universe:updated' => 'setSelectedRadioStation',
        'thematic:selected' => 'thematicSelected',
        'playlist:created' => 'setPlaylists',
        'songs:favorites:updated' => '$refresh',
    ];

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'playlists', livewireComponent: $this);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        $this->thematics = thematics();
        $this->initialized = true;
    }

    public function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $selectedRadioStationId)->firstOrFail();
    }

    /** @throws \Exception */
    public function render(): View
    {
        if ($this->initialized) {
            $this->setPlaylists();
        }

        return view('livewire.playlists.index');
    }

    /** @throws \Exception */
    public function setPlaylists(): void
    {
        $this->publicPlaylists = Playlist::with(['media', 'thematic', 'songs'])
            ->where('user_id', null)
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereRelation(
                    'radioStations',
                    fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStation->id)
                )->orWhereDoesntHave('radioStations');
            })
            ->where('active', true)
            ->where('published_at', '<=', Date::now())
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereNull('unpublished_at')
                    ->orWhere('unpublished_at', '>=', Date::now());
            })
            ->when(
                $this->selectedThematicId,
                fn (Builder $playlist) => $playlist->where('thematic_id', $this->selectedThematicId)
            )
            ->latest('published_at')
            ->get()
            ->take(20);
        $this->privatePlaylists = Auth::user()
            ? Auth::user()->playlists()
                ->with(['media', 'thematic', 'songs'])
                ->when(
                    $this->selectedThematicId,
                    fn (Builder|HasMany $playlist) => $playlist->where('thematic_id', $this->selectedThematicId)
                )
                ->latest()
                ->get()
                ->take(20)
            : collect();
        $this->favoriteSongs = Auth::user()
            ? Auth::user()->favoriteSongs()
                ->with(['performerRelationship', 'albumRelationship'])
                ->take(20)
                ->get()
            : collect();
    }

    public function thematicSelected(?int $thematicId): void
    {
        if ($this->selectedThematicId === $thematicId) {
            $this->selectedThematicId = null;
        } else {
            $this->selectedThematicId = $thematicId;
        }
    }
}

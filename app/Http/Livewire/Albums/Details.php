<?php

namespace App\Http\Livewire\Albums;

use App\Models\Audio\Song;
use App\Models\Performers\Album;
use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Details extends Component
{
    public bool $initialized = false;

    public Album $album;

    public Collection $songs;

    public bool $albumIsPlaying = false;

    public array $selectedSongIds = [];

    public RadioStation $selectedRadioStation;

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public ?string $playedSubAudioSourceClass = null;

    public ?int $playedSubAudioSourceId = null;

    public bool $pauseAllSongs = false;

    public bool $dedicationDisabled = false;

    public array $dedicatableSongWinmediaIds = [];

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:sub:audio:source:updated' => 'updatePlayedSubAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'radio:station:universe:updated' => 'updateSelectedRadio',
        'songs:dedicatable:update' => 'setDedicatableSongIds',
        'songs:favorites:updated' => '$refresh',
    ];

    public function mount(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        [
            'played_sub_audio_source_class' => $subAudioSourceClass,
            'played_sub_audio_source_id' => $subAudioSourceId,
        ] = app(UserJourneysService::class)->getPlayedSubAudioSource();
        $this->setPlayedSubAudioSource($subAudioSourceClass, $subAudioSourceId);
        $this->setPlayingStatus();
        $this->songs = collect();
    }

    public function setSelectedRadioStation(int $radioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $radioStationId)->firstOrFail();
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    protected function setPlayedSubAudioSource(
        ?string $subAudioSourceClass,
        ?int $subAudioSourceId,
    ): void {
        $this->playedSubAudioSourceClass = $subAudioSourceClass;
        $this->playedSubAudioSourceId = $subAudioSourceId;
    }

    protected function setPlayingStatus(): void
    {
        $this->albumIsPlaying = $this->playedAudioSourceClass === Album::class
            && $this->playedAudioSourceId === $this->album->id
            && ! $this->pauseAllSongs;
    }

    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'album_details',
            routeParams: ['album' => $this->album],
            livewireComponent: $this
        );
        $this->initialized = true;
        $this->emitSelf('songs:dedicatable:update');
    }

    /** @throws \Exception */
    public function updateSelectedRadio(int $radioStationId): void
    {
        $this->setSelectedRadioStation($radioStationId);
        $this->emitSelf('songs:dedicatable:update');
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \JsonException
     */
    public function setDedicatableSongIds(): void
    {
        if (! Auth::check()) {
            return;
        }
        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::user()->id,
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
        }
        $songWinmediaIds = $this->songs->pluck('imedia')->toArray();
        if (count(array_filter($songWinmediaIds)) > 0) {
            $this->dedicatableSongWinmediaIds = app(DedicationService::class)->verifySongsEligibility(
                $this->selectedRadioStation->winmedia_id,
                $songWinmediaIds
            );
        } else {
            $this->dedicatableSongWinmediaIds = [];
        }
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->setPlayingStatus();
        $this->pauseAllSongs = false;
    }

    public function updatePlayedSubAudioSource(
        string $subAudioSourceClass,
        int $subAudioSourceId
    ): void {
        $this->setPlayedSubAudioSource($subAudioSourceClass, $subAudioSourceId);
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllSongs = true;
        $this->setPlayingStatus();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllSongs = false;
        $this->setPlayingStatus();
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->setSongs();
        }

        return view('livewire.albums.details');
    }

    public function setSongs(): void
    {
        $this->songs = $this->album
            ->songs()
            ->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])
            ->get();
        $this->updateSongStatuses();
    }

    protected function updateSongStatuses(): void
    {
        if ($this->pauseAllSongs) {
            $this->songs = $this->songs->map(function (Song $song) {
                $song->selected = in_array($song->id, $this->selectedSongIds, true);
                $song->playing = false;
                $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);

                return $song;
            });

            return;
        }
        $this->selectedSongIds = [];
        $this->songs = $this->songs->map(function (Song $song) {
            $songIsPlayingInPlaylistMode = $this->playedAudioSourceClass === Album::class
                && $this->playedAudioSourceId === $this->album->id
                && $this->playedSubAudioSourceClass === Song::class
                && $this->playedSubAudioSourceId === $song->id;
            $songIsPlayingInDirectMode = $this->playedAudioSourceClass === Song::class
                && $this->playedAudioSourceId === $song->id;
            $isPlaying = $songIsPlayingInPlaylistMode || $songIsPlayingInDirectMode;
            if ($isPlaying) {
                $this->selectedSongIds[] = $song->id;
            }
            $song->selected = $isPlaying;
            $song->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
            $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);

            return $song;
        });
    }
}

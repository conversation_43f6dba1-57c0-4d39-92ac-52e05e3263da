<?php

namespace App\Http\Livewire\Search;

use App\Models\Events\Event;
use App\Models\Map\Place;
use App\Services\Elasticsearch\CustomSearchService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\WithPagination;

class EventResults extends Component
{
    use WithPagination;

    public bool $initialized = false;

    public ?Place $place = null;

    public string $placeId = '';

    public string $eventsToDisplay = '';

    public string $search = '';

    public ?string $customTitle = null;

    public int $elementsPerPage = 12;

    public Collection $events;

    public array $selectedEventIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllEvents = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    public function queryStringWithPagination()
    {
        return [];
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function mount(): void
    {
        $this->setPage($this->page);
        $this->events = collect();
        if ($this->placeId) {
            $this->place = Place::find($this->placeId);
        }
    }

    /** @throws \Exception */
    public function init(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->initialized = true;
    }

    public function render(): View
    {
        $this->customTitle = null;
        if ($this->search) {
            if (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_AUTHOR)) {
                $this->customTitle = 'Tous les évènements de "' . Str::replaceFirst(CustomSearchService::FILTER_AUTHOR, '', $this->search) . '"';
            } elseif (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_TAGS)) {
                $this->customTitle = 'Tous les évènements du tag "#' . Str::replaceFirst(CustomSearchService::FILTER_TAGS, '', $this->search) . '"';
            } elseif (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_DATE)) {
                $date = Date::createFromFormat('Y-m-d', Str::replaceFirst(CustomSearchService::FILTER_DATE, '', $this->search));
                $this->customTitle = 'Tous les évènements du ' . $date->timezone('Europe/Paris')->isoFormat('D MMMM Y');
            }
            $esEvents = app(CustomSearchService::class)->searchEvents(
                $this->search,
                $this->elementsPerPage,
                $this->elementsPerPage * ($this->page - 1)
            )['hits'];
            $eventsMap = array_map(function ($esEvents) {
                return Event::find($esEvents['_source']['id']);
            }, $esEvents['hits']);
            $events = (new LengthAwarePaginator(
                $eventsMap,
                $esEvents['total']['value'],
                $this->elementsPerPage,
                $this->page
            ));
        } elseif ($this->place) {
            $placeId = $this->place->id;
            switch ($this->eventsToDisplay) {
                case Event::PAST:
                    $events = Event::with(['contentLocations', 'media'])
                        ->where('ended_at', '<', now()->subDay()->format('Y-m-d'))
                        ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                            $query->where('location_id', $placeId)
                                ->where('location_type', Place::class);
                        })
                        ->where('active', true)
                        ->orderBy('started_at', 'DESC')
                        ->orderBy('ended_at', 'DESC')
                        ->paginate($this->elementsPerPage);
                    break;
                case Event::CURRENT:
                    $events = Event::with(['contentLocations', 'media'])
                        ->where('started_at', '<=', now()->format('Y-m-d'))
                        ->where('ended_at', '>=', now()->subDay()->format('Y-m-d'))
                        ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                            $query->where('location_id', $placeId)
                                ->where('location_type', Place::class);
                        })
                        ->where('active', true)
                        ->orderByRaw(config('database.default') === 'pgsql'
                            ? 'ABS((started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date - (now() AT TIME ZONE \'Europe/Paris\')::date)'
                            : 'ABS(DATEDIFF(started_at, NOW()))')
                        ->orderBy('started_at')
                        ->paginate($this->elementsPerPage);
                    break;
                case Event::NEXT:
                    $events = Event::with(['contentLocations', 'media'])
                        ->where('started_at', '>', now()->format('Y-m-d'))
                        ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                            $query->where('location_id', $placeId)
                                ->where('location_type', Place::class);
                        })
                        ->where('active', true)
                        ->orderBy('started_at', 'ASC')
                        ->orderBy('ended_at', 'ASC')
                        ->paginate($this->elementsPerPage);
                    break;
                default:
                    $events = Event::with(['media'])
                        ->active()
                        ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                            $query->where('location_id', $placeId)
                                ->where('location_type', Place::class);
                        })
                        ->orderBy('started_at', 'DESC')
                        ->orderBy('ended_at', 'DESC')
                        ->paginate($this->elementsPerPage);
            }
        } else {
            $events = Event::orderBy('id', 'desc')->active()->paginate($this->elementsPerPage);
        }
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'search_event_results',
            routeParams: ['search' => $this->search],
            livewireComponent: $this
        );

        $links = $events->links();
        $this->events = collect($events->items());

        $this->updateAudioEventsStatuses();

        return view('livewire.search.event-results', [
            'links' => $links,
        ]);
    }

    public function setEventsToDisplay(string $eventsToDisplay): void
    {
        $this->eventsToDisplay = $eventsToDisplay;
        $this->resetPage();
    }

    protected function updateAudioEventsStatuses(): void
    {
        if ($this->pauseAllEvents) {
            $this->events = $this->events->map(function (Event $event) {
                $event->selected = in_array($event->id, $this->selectedEventIds, true);
                $event->playing = false;

                return $event;
            });

            return;
        }
        $this->selectedEventIds = [];
        $this->events = $this->events->map(function (Event $event) {
            $isPlaying = $event::class === $this->playedAudioSourceClass
                && $event->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedEventIds[] = $event->id;
            }
            $event->selected = $isPlaying;
            $event->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $event;
        });
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllEvents = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllEvents = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllEvents = false;
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }
}

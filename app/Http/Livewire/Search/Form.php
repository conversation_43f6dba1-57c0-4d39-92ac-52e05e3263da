<?php

namespace App\Http\Livewire\Search;

use Illuminate\Support\Str;
use Illuminate\View\View;
use Livewire\Component;

class Form extends Component
{
    public const TYPE_ALL = 'all';

    public const TYPE_PODCASTS = 'podcasts';

    public const TYPE_EVENTS = 'events';

    public const TYPE_NEWS_ARTICLES = 'news';

    public const TYPE_MUSIC = 'music';

    public array $types = [
        self::TYPE_ALL => 'Tout',
        self::TYPE_NEWS_ARTICLES => 'Actualités',
        self::TYPE_EVENTS => 'Évènements',
        self::TYPE_MUSIC => 'Musique et playlists',
        self::TYPE_PODCASTS => 'Podcasts',
    ];

    public ?string $selectedType = self::TYPE_ALL;

    public ?string $search = '';

    public bool $shouldRedirectOnSearch = true;

    public bool $hideTypeSelect = false;

    public function init(): void
    {
        if (! $this->shouldRedirectOnSearch) {
            $this->emitTo('search.results', 'search:refresh', $this->search, $this->selectedType);
        }
    }

    public function updateSearch(): void
    {
        if ($this->shouldSwitchToResultsView()) {
            if ($this->shouldRedirectOnSearch) {
                $this->emitTo('router', 'nav:to', 'search_results', [
                    'search' => $this->search,
                    'selectedType' => $this->selectedType,
                ]);

                return;
            }
            $this->emitTo('search.results', 'search:refresh', $this->search, $this->selectedType);
        }
    }

    protected function shouldSwitchToResultsView(): bool
    {
        return Str::length($this->search) >= 2;
    }

    public function clear(): void
    {
        if ($this->search) {
            $this->search = '';
            $this->selectedType = self::TYPE_ALL;
            $this->emitTo('router', 'nav:back');
        }
    }

    public function setSelectedType(string $typeKey): void
    {
        $this->selectedType = $typeKey;
        if ($this->shouldSwitchToResultsView()) {
            $this->emitTo('search.results', 'search:refresh', $this->search, $this->selectedType);
        }
    }

    public function render(): View
    {
        return view('livewire.search.form');
    }
}

<?php

namespace App\Http\Livewire\Search;

use App\Models\Audio\Playlist;
use App\Models\Map\Place;
use App\Services\Elasticsearch\CustomSearchService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\WithPagination;

class PlaylistResults extends Component
{
    use WithPagination;

    public bool $initialized = false;

    public ?Place $place = null;

    public string $placeId = '';

    public bool $privatePlaylists = false;

    public array $selectedPlaylistIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllPlaylists = false;

    public int $elementsPerPage = 33;

    public string $search = '';

    public ?string $customTitle = null;

    public Collection $playlists;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    public function queryStringWithPagination()
    {
        return [];
    }

    public function mount(): void
    {
        $this->setPage($this->page);
        $this->playlists = collect();
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        if ($this->placeId) {
            $this->place = Place::find($this->placeId);
        }
    }

    protected function setPlayedAudioSource(
        ?string $audioSourceClass,
        ?int $audioSourceId,
    ): void {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    /** @throws \Exception */
    public function init(): void
    {
        $this->initialized = true;
    }

    /** @throws \Exception */
    public function render(): View
    {
        $this->customTitle = null;
        if ($this->search) {
            if (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_TAGS)) {
                $this->customTitle = 'Toutes les playlists du tag "#' . Str::replaceFirst(CustomSearchService::FILTER_TAGS, '', $this->search) . '"';
            }
            $esPlaylists = app(CustomSearchService::class)->searchPlaylists(
                $this->search,
                $this->elementsPerPage,
                $this->elementsPerPage * ($this->page - 1)
            )['hits'];
            $playlistsMap = array_map(function ($esPlaylists) {
                return Playlist::with(['media', 'songs'])->find($esPlaylists['_source']['id']);
            }, $esPlaylists['hits']);
            $playlists = (new LengthAwarePaginator(
                $playlistsMap,
                $esPlaylists['total']['value'],
                $this->elementsPerPage,
                $this->page
            ));
        } elseif ($this->place) {
            $placeId = $this->place->id;
            $playlists = Playlist::with(['media', 'songs'])
                ->active()
                ->published()
                ->public()
                ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                    $query->where('location_id', $placeId)
                        ->where('location_type', Place::class);
                })
                ->latest('published_at')
                ->paginate($this->elementsPerPage);
        } elseif ($this->privatePlaylists && Auth::user()) {
            $playlists = Auth::user()->playlists()
                ->with('songs')
                ->latest('published_at')
                ->paginate($this->elementsPerPage);
        } else {
            $playlists = Playlist::with('songs')
                ->active()
                ->published()
                ->public()
                ->latest('published_at')
                ->paginate($this->elementsPerPage);
        }
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'search_playlist_results',
            routeParams: ['search' => $this->search],
            livewireComponent: $this
        );

        $links = $playlists->links();
        $this->playlists = collect($playlists->items());

        $this->updateAudioPlaylistsStatuses();

        return view('livewire.search.playlist-results', [
            'links' => $links,
        ]);
    }

    protected function updateAudioPlaylistsStatuses(): void
    {
        if ($this->pauseAllPlaylists) {
            $this->playlists = $this->playlists->map(function (Playlist $playlist) {
                $playlist->selected = in_array($playlist->id, $this->selectedPlaylistIds, true);
                $playlist->playing = false;

                return $playlist;
            });

            return;
        }
        $this->selectedPlaylistIds = [];
        $this->playlists = $this->playlists->map(function (Playlist $playlist) {
            $isPlaying = $playlist::class === $this->playedAudioSourceClass
                && $playlist->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedPlaylistIds[] = $playlist->id;
            }
            $playlist->selected = $isPlaying;
            $playlist->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $playlist;
        });
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllPlaylists = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllPlaylists = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllPlaylists = false;
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }
}

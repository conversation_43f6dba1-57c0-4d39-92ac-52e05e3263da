<?php

namespace App\Http\Livewire\Search;

use App\Models\Map\Place;
use App\Models\Performers\Album;
use App\Models\Performers\Performer;
use App\Services\Elasticsearch\CustomSearchService;
use App\Services\Seo\SeoMetaService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\WithPagination;

class PerformerResults extends Component
{
    use WithPagination;

    public bool $initialized = false;

    public ?Place $place = null;

    public string $placeId = '';

    public int $elementsPerPage = 33;

    public string $search = '';

    public ?string $customTitle = null;

    public Collection $performers;

    public string $performerOrder = 'alphabetical_asc';

    protected $queryString = [
        'placeId' => ['except' => ''],
        'performerOrder' => ['except' => 'alphabetical_asc'],
    ];

    protected $listeners = [];

    public function mount(): void
    {
        $this->setPage($this->page);
        $this->performers = collect();
        if ($this->placeId) {
            $this->place = Place::find($this->placeId);
        }
    }

    /** @throws \Exception */
    public function init(): void
    {
        $this->initialized = true;
    }

    /** @throws \Exception */
    public function render(): View
    {
        $this->customTitle = null;
        if ($this->search) {
            if (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_TAGS)) {
                $this->customTitle = 'Tous les artistes du tag "#' . Str::replaceFirst(CustomSearchService::FILTER_TAGS, '', $this->search) . '"';
            }
            $esPerformers = app(CustomSearchService::class)->searchPerformers(
                $this->search,
                $this->elementsPerPage,
                $this->elementsPerPage * ($this->page - 1)
            )['hits'];
            $performersMap = array_map(function ($esPerformers) {
                return Performer::with(['albums'])->find($esPerformers['_source']['id']);
            }, $esPerformers['hits']);
            $performers = (new LengthAwarePaginator(
                $performersMap,
                $esPerformers['total']['value'],
                $this->elementsPerPage,
                $this->page
            ));
        } elseif ($this->place) {
            $placeId = $this->place->id;
            $albumsSubquery = Album::select('performer_id')
                ->selectRaw('MAX(published_at) as max_published_at')
                ->selectRaw('DATE_TRUNC(\'minute\', MAX(created_at)) as max_created_at_truncated')
                ->selectRaw('MAX(created_at) as max_created_at')
                ->where(function (Builder $query) use ($placeId) {
                    $query->whereHas('contentLocations', function (Builder $query) use ($placeId) {
                        $query->where('location_id', $placeId)
                            ->where('location_type', Place::class);
                    })
                        ->orWhereHas('label', function (Builder $query) use ($placeId) {
                            $query->whereHas('contentLocations', function (Builder $query) use ($placeId) {
                                $query->where('location_id', $placeId)
                                    ->where('location_type', Place::class);
                            });
                        });
                })
                ->where('compilation_id', null)
                ->groupBy('performer_id');

            $performers = Performer::with(['albums', 'labels'])
                ->select('performers.id', 'performers.name')
                ->joinSub($albumsSubquery, 'albums_max', function ($join) {
                    $join->on('performers.id', '=', 'albums_max.performer_id');
                });

            switch ($this->performerOrder) {
                case 'last_released':
                    $performers = $performers
                        ->withCount('songs')
                        ->orderByRaw('albums_max.max_published_at DESC NULLS LAST')
                        ->orderByDesc('albums_max.max_created_at_truncated')
                        ->orderByDesc('songs_count')
                        ->orderByDesc('albums_max.max_created_at');
                    break;
                case 'last_created':
                    $performers = $performers
                        ->withCount('songs')
                        ->orderByDesc('albums_max.max_created_at_truncated')
                        ->orderByDesc('songs_count')
                        ->orderByDesc('albums_max.max_created_at');
                    break;
                case 'alphabetical_asc':
                    $performers = $performers->orderByRaw('LOWER(TRIM(name)) ASC');
                    break;
                case 'alphabetical_desc':
                    $performers = $performers->orderByRaw('LOWER(TRIM(name)) DESC');
                    break;
                default:
                    $performers = $performers->orderBy('id', 'desc');
            }

            $performers = $performers->paginate($this->elementsPerPage);
        } else {
            $performers = Performer::with('albums')->orderBy('id', 'desc')->paginate($this->elementsPerPage);
        }
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'search_performer_results',
            routeParams: ['search' => $this->search],
            livewireComponent: $this
        );

        $links = $performers->links();
        $this->performers = collect($performers->items());

        return view('livewire.search.performer-results', [
            'links' => $links,
        ]);
    }

    public function setPerformersOrderToDisplay(string $performerOrder): void
    {
        $this->performerOrder = $performerOrder;
        $this->resetPage();
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }
}

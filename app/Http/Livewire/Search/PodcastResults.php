<?php

namespace App\Http\Livewire\Search;

use App\Models\Audio\Podcast;
use App\Models\Map\Place;
use App\Models\Radio\Program;
use App\Services\Elasticsearch\CustomSearchService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\WithPagination;

class PodcastResults extends Component
{
    use WithPagination;

    public const TYPES_PODCASTS = [Podcast::TYPE_REPLAY, Podcast::TYPE_ORIGINAL, 'subscribed'];

    public bool $initialized = false;

    public ?Place $place = null;

    public string $placeId = '';

    public array $selectedPodcastIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllPodcasts = false;

    public int $elementsPerPage = 33;

    public ?string $selectedType = null;

    public string $search = '';

    public ?string $customTitle = null;

    protected LengthAwarePaginator $podcasts;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'type:selected' => 'typeSelected',
    ];

    public function queryStringWithPagination()
    {
        return [];
    }

    public function mount(): void
    {
        $this->setPage($this->page);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        if ($this->placeId) {
            $this->place = Place::find($this->placeId);
        }
    }

    protected function setPlayedAudioSource(
        ?string $audioSourceClass,
        ?int $audioSourceId,
    ): void {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    /** @throws \Exception */
    public function init(): void
    {
        $this->initialized = true;
    }

    /** @throws \Exception */
    public function render(): View
    {
        $this->customTitle = null;
        $subscribedProgramsIds = Auth::check()
            ? Auth::user()->subscribedPrograms->pluck('id')->toArray()
            : [];
        switch ($this->selectedType) {
            case 'elasticsearch':
                if (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_AUTHOR)) {
                    $this->customTitle = 'Tous les podcasts de "' . Str::replaceFirst(CustomSearchService::FILTER_AUTHOR, '', $this->search) . '"';
                } elseif (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_TAGS)) {
                    $this->customTitle = 'Tous les podcasts du tag "#' . Str::replaceFirst(CustomSearchService::FILTER_TAGS, '', $this->search) . '"';
                } elseif (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_DATE)) {
                    $date = Date::createFromFormat('Y-m-d', Str::replaceFirst(CustomSearchService::FILTER_DATE, '', $this->search));
                    $this->customTitle = 'Tous les podcasts du ' . $date->timezone('Europe/Paris')->isoFormat('D MMMM Y');
                } elseif (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_PROGRAM)) {
                    $programName = Program::find(Str::replaceFirst(CustomSearchService::FILTER_PROGRAM, '', $this->search), ['title'])?->title;
                    $this->customTitle = 'Tous les podcasts de l\'émission "' . $programName . '"';
                }
                $esPodcasts = app(CustomSearchService::class)->searchPodcasts(
                    $this->search,
                    $this->elementsPerPage,
                    $this->elementsPerPage * ($this->page - 1)
                )['hits'];
                $podcasts = array_map(
                    static fn ($esPodcast) => Podcast::find($esPodcast['_source']['id']),
                    $esPodcasts['hits']
                );
                $this->podcasts = new LengthAwarePaginator(
                    $podcasts,
                    $esPodcasts['total']['value'],
                    $this->elementsPerPage,
                    $this->page
                );
                break;
            case 'subscribed':
                $this->podcasts = Podcast::with(['media', 'program'])
                    ->whereIn('program_id', $subscribedProgramsIds)
                    ->where('published_at', '<=', Date::now())
                    ->where('active', true)
                    ->hasAudio()
                    ->paginate($this->elementsPerPage);
                break;
            case 'place':
                $placeId = $this->place?->id;
                $this->podcasts = Podcast::with(['media', 'program'])
                    ->active()
                    ->hasAudio()
                    ->where('published_at', '<=', Date::now())
                    ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                        $query->where('location_id', $placeId)
                            ->where('location_type', Place::class);
                    })
                    ->orderByDesc('published_at')
                    ->paginate($this->elementsPerPage);
                break;
            default:
                $this->podcasts = Podcast::with(['media', 'program'])
                    ->when(
                        $this->selectedType,
                        fn (Builder $podcast) => $podcast->where('type', $this->selectedType)
                    )
                    ->where('published_at', '<=', Date::now())
                    ->where('active', true)
                    ->hasAudio()
                    ->paginate($this->elementsPerPage);
                break;
        }
        $this->updateAudioPodcastsStatuses();
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'search_podcast_results',
            routeParams: ['search' => $this->search],
            livewireComponent: $this
        );

        return view('livewire.search.podcast-results', [
            'podcasts' => $this->initialized ? $this->podcasts : collect(),
            'subscribedProgramsIds' => $subscribedProgramsIds,
        ]);
    }

    protected function updateAudioPodcastsStatuses(): void
    {
        $podcastsCollection = $this->podcasts->getCollection();
        if ($this->pauseAllPodcasts) {
            $podcastsCollection = $podcastsCollection->map(function (Podcast $podcast) {
                $podcast->selected = in_array($podcast->id, $this->selectedPodcastIds, true);
                $podcast->playing = false;

                return $podcast;
            });

            $this->podcasts->setCollection($podcastsCollection);

            return;
        }
        $this->selectedPodcastIds = [];
        $podcastsCollection = $podcastsCollection->map(function (Podcast $podcast) {
            $isPlaying = $podcast::class === $this->playedAudioSourceClass
                && $podcast->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedPodcastIds[] = $podcast->id;
            }
            $podcast->selected = $isPlaying;
            $podcast->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $podcast;
        });

        $this->podcasts->setCollection($podcastsCollection);
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllPodcasts = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllPodcasts = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllPodcasts = false;
    }

    public function typeSelected(?string $type): void
    {
        $this->selectedType = $this->selectedType === $type ? null : $type;
        $this->resetPage();
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }
}

<?php

namespace App\Http\Livewire\Search;

use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Elasticsearch\CustomSearchService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Component;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Results extends Component
{
    public bool $initialized = false;

    public bool $showScore = false;

    public ?string $selectedType = null;

    public string $search = '';

    public RadioStation $selectedRadioStation;

    public array $selectedAudioSourceIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllAudioSources = false;

    public array $songs = [];

    public array $podcasts = [];

    public array $newsArticles = [];

    public array $events = [];

    public array $performers = [];

    public bool $dedicationDisabled = false;

    public array $dedicatableSongWinmediaIds = [];

    public array $playlists = [];

    protected $queryString = ['search' => ['except' => '']];

    protected $listeners = [
        'search:refresh' => 'search',
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'radio:station:universe:updated' => 'updateSelectedRadio',
        'songs:dedicatable:update' => 'setDedicatableSongIds',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    /** @throws \Exception */
    public function mount(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->showScore = auth()->user()?->id === 7;
    }

    public function init(): void
    {
        $this->initialized = (bool) $this->search;
    }

    /** @throws \Exception */
    protected function setSelectedRadioStation(int $radioStationId): void
    {
        $this->selectedRadioStation = radioStations()->firstOrFail('id', $radioStationId);
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    /** @throws \Exception */
    public function updateSelectedRadio(int $radioStationId): void
    {
        $this->setSelectedRadioStation($radioStationId);
        $this->emitSelf('songs:dedicatable:update');
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \JsonException
     */
    public function setDedicatableSongIds(): void
    {
        if (! Auth::check()) {
            return;
        }
        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::user()->id,
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
        }
        $songWinmediaIds = collect($this->songs)->pluck('imedia')->toArray();
        if (count(array_filter($songWinmediaIds)) > 0) {
            $this->dedicatableSongWinmediaIds = app(DedicationService::class)->verifySongsEligibility(
                $this->selectedRadioStation->winmedia_id,
                $songWinmediaIds
            );
        } else {
            $this->dedicatableSongWinmediaIds = [];
        }
    }

    /** @SuppressWarnings(PHPMD.NPathComplexity) */
    public function search(string $search, string $selectedType): void
    {
        $this->initialized = true;
        $this->search = $search;
        $this->selectedType = $selectedType;
        if ($selectedType === Form::TYPE_ALL || $selectedType === Form::TYPE_MUSIC) {
            $this->songs = $this->getSearchedSongs($search, $selectedType === Form::TYPE_MUSIC ? 10 : 5);
            $this->emitSelf('songs:dedicatable:update');
        }
        if ($selectedType === Form::TYPE_ALL || $selectedType === Form::TYPE_PODCASTS) {
            $this->podcasts = $this->getSearchedPodcasts($search, 14);
        }
        if ($selectedType === Form::TYPE_ALL || $selectedType === Form::TYPE_NEWS_ARTICLES) {
            $this->newsArticles = $this->getSearchedNews($search, 7);
        }
        if ($selectedType === Form::TYPE_ALL || $selectedType === Form::TYPE_EVENTS) {
            $this->events = $this->getSearchedEvents($search, 7);
        }
        if ($selectedType === Form::TYPE_ALL || $selectedType === Form::TYPE_MUSIC) {
            $this->performers = $this->getSearchedperformers($search, 7);
        }
        if ($selectedType === Form::TYPE_ALL || $selectedType === Form::TYPE_MUSIC) {
            $this->playlists = $this->getSearchedPlaylists($search, 7);
        }
    }

    protected function getSearchedPerformers(
        string $search,
        int $size
    ): array {
        if (Str::startsWith($search, [
            CustomSearchService::FILTER_AUTHOR,
            CustomSearchService::FILTER_DATE,
            CustomSearchService::FILTER_PROGRAM,
        ])) {
            return [];
        }

        return array_map(
            static fn (array $esResult) => array_merge($esResult['_source'], ['_score' => $esResult['_score']]),
            app(CustomSearchService::class)->searchPerformers(
                $search,
                $size
            )['hits']['hits']
        );
    }

    protected function getSearchedSongs(
        string $search,
        int $size
    ): array {
        if (Str::startsWith($search, [
            CustomSearchService::FILTER_AUTHOR,
            CustomSearchService::FILTER_DATE,
            CustomSearchService::FILTER_PROGRAM,
        ])) {
            return [];
        }

        $esResult = app(CustomSearchService::class)->searchSongs(
            $search,
            $size
        )['hits']['hits'];

        return array_map(fn (array $esResult) => array_merge(
            $esResult['_source'],
            [
                'selected' => $esResult['_source']['audio_source_class'] === $this->playedAudioSourceClass
                    && $esResult['_source']['id'] === $this->playedAudioSourceId,
                'playing' => ! $this->pauseAllAudioSources
                    && $esResult['_source']['audio_source_class'] === $this->playedAudioSourceClass
                    && $esResult['_source']['id'] === $this->playedAudioSourceId,
                '_score' => $esResult['_score'],
            ]
        ), $esResult);
    }

    protected function getSearchedPodcasts(
        string $search,
        int $size
    ): array {
        $esResult = app(CustomSearchService::class)->searchPodcasts(
            $search,
            $size
        )['hits']['hits'];

        return array_map(fn (array $esResult) => array_merge(
            $esResult['_source'],
            [
                'selected' => $esResult['_source']['audio_source_class'] === $this->playedAudioSourceClass
                    && $esResult['_source']['id'] === $this->playedAudioSourceId,
                'playing' => ! $this->pauseAllAudioSources
                    && $esResult['_source']['audio_source_class'] === $this->playedAudioSourceClass
                    && $esResult['_source']['id'] === $this->playedAudioSourceId,
                '_score' => $esResult['_score'],
            ]), $esResult);
    }

    protected function getSearchedPlaylists(
        string $search,
        int $size
    ): array {
        if (Str::startsWith($search, [
            CustomSearchService::FILTER_AUTHOR,
            CustomSearchService::FILTER_DATE,
            CustomSearchService::FILTER_PROGRAM,
            CustomSearchService::FILTER_GENRE,
        ])) {
            return [];
        }

        $publicFilter = [
            'must' => [
                ['term' => ['active' => true]],
                [
                    'bool' => [
                        'must_not' => ['exists' => ['field' => 'user_id']],
                        'should' => [
                            [
                                'bool' => [
                                    'must' => [
                                        ['range' => ['published_at' => ['lte' => 'now']]],
                                        ['bool' => ['must_not' => ['exists' => ['field' => 'unpublished_at']]]],
                                    ],
                                ],
                            ],
                            [
                                'bool' => [
                                    'must' => [
                                        ['range' => ['published_at' => ['lte' => 'now']]],
                                        ['range' => ['unpublished_at' => ['gte' => 'now']]],
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $filterCondition = Auth::check()
        ? [
            'bool' => [
                'should' => [
                    ['term' => ['user_id' => Auth::id()]],
                    ['bool' => $publicFilter],
                ],
            ],
        ]
        : [
            'bool' => $publicFilter,
        ];

        return array_map(
            static fn (array $esResult) => array_merge($esResult['_source'], ['_score' => $esResult['_score']]),
            app(CustomSearchService::class)->searchPlaylists(
                $search,
                $size,
                0,
                $filterCondition
            )['hits']['hits']
        );
    }

    protected function getSearchedNews(
        string $search,
        int $size
    ): array {
        if (Str::startsWith($search, [
            CustomSearchService::FILTER_PROGRAM,
            CustomSearchService::FILTER_GENRE,
        ])) {
            return [];
        }

        return array_map(
            static fn (array $esResult) => array_merge($esResult['_source'], ['_score' => $esResult['_score']]),
            app(CustomSearchService::class)->searchNews(
                $search,
                $size
            )['hits']['hits']
        );
    }

    protected function getSearchedEvents(
        string $search,
        int $size
    ): array {
        if (Str::startsWith($search, [
            CustomSearchService::FILTER_PROGRAM,
            CustomSearchService::FILTER_GENRE,
        ])) {
            return [];
        }

        return array_map(
            static fn (array $esResult) => array_merge($esResult['_source'], ['_score' => $esResult['_score']]),
            app(CustomSearchService::class)->searchEvents(
                $search,
                $size
            )['hits']['hits']
        );
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllAudioSources = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllAudioSources = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllAudioSources = false;
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->updateAudioSourceStatuses('songs');
            $this->updateAudioSourceStatuses('podcasts');
            $this->updateAudioSourceStatuses('playlists');
            $this->updateAudioSourceStatuses('newsArticles');
            $this->updateAudioSourceStatuses('events');
        }
        if ($this->selectedType === Form::TYPE_ALL || $this->selectedType === Form::TYPE_MUSIC) {
            $this->emit('rail:load', ['railName' => 'search-performers-rail']);
        }
        if ($this->selectedType === Form::TYPE_ALL || $this->selectedType === Form::TYPE_PODCASTS) {
            $this->emit('rail:load', ['railName' => 'search-podcasts-rail']);
        }
        if ($this->selectedType === Form::TYPE_ALL || $this->selectedType === Form::TYPE_NEWS_ARTICLES) {
            $this->emit('rail:load', ['railName' => 'news-articles-search']);
        }
        if ($this->selectedType === Form::TYPE_ALL || $this->selectedType === Form::TYPE_EVENTS) {
            $this->emit('rail:load', ['railName' => 'browse-events']);
        }
        if ($this->selectedType === Form::TYPE_ALL || $this->selectedType === Form::TYPE_MUSIC) {
            $this->emit('rail:load', ['railName' => 'search-playlists-rail']);
        }
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'search_results',
            routeParams: ['search' => $this->search],
            livewireComponent: $this,
        );

        return view('livewire.search.results');
    }

    protected function updateAudioSourceStatuses(string $type): void
    {
        if ($this->{$type}) {
            if ($this->pauseAllAudioSources) {
                $this->{$type} = array_map(function (array $audioItem) use ($type) {
                    $audioItem['selected'] = in_array($audioItem['id'], $this->selectedAudioSourceIds, true);
                    $audioItem['playing'] = false;
                    $audioItem['dedicatable'] = $type === 'songs'
                        && in_array($audioItem['imedia'], $this->dedicatableSongWinmediaIds, true);

                    return $audioItem;
                }, $this->{$type});

                return;
            }
            $this->selectedAudioSourceIds = [];
            $this->{$type} = array_map(function (array $audioItem) use ($type) {
                $isPlaying = $audioItem['audio_source_class'] === $this->playedAudioSourceClass
                    && $audioItem['id'] === $this->playedAudioSourceId;
                if ($isPlaying) {
                    $this->selectedAudioSourceIds[] = $audioItem['id'];
                }
                $audioItem['selected'] = $isPlaying;
                $audioItem['playing'] = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
                $audioItem['dedicatable'] = $type === 'songs'
                    && in_array($audioItem['imedia'], $this->dedicatableSongWinmediaIds, true);

                return $audioItem;
            }, $this->{$type});
        }
    }
}

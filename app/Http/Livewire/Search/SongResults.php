<?php

namespace App\Http\Livewire\Search;

use App\Models\Audio\Song;
use App\Models\Radio\LiveBroadcasting;
use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Elasticsearch\CustomSearchService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\WithPagination;

class SongResults extends Component
{
    use WithPagination;

    public bool $initialized = false;

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllAudioSources = false;

    public array $selectedSongIds = [];

    public bool $dedicationDisabled = false;

    public array $dedicatableSongWinmediaIds = [];

    public int $elementsPerPage = 15;

    public ?string $selectedType = null;

    public string $search = '';

    public Collection $songs;

    public RadioStation $selectedRadioStation;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'radio:station:universe:updated' => 'updateSelectedRadio',
        'songs:dedicatable:update' => 'setDedicatableSongIds',
    ];

    public function queryStringWithPagination()
    {
        return [];
    }

    public function mount(): void
    {
        $this->setPage($this->page);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->songs = collect();
    }

    public function setSelectedRadioStation(int $radioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $radioStationId)->firstOrFail();
    }

    protected function setPlayedAudioSource(
        ?string $audioSourceClass,
        ?int $audioSourceId,
    ): void {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'search_song_results',
            routeParams: ['search' => $this->search],
            livewireComponent: $this
        );
        $this->initialized = true;
        $this->emitSelf('songs:dedicatable:update');
    }

    /** @throws \Exception */
    public function updateSelectedRadio(int $radioStationId): void
    {
        $this->setSelectedRadioStation($radioStationId);
        $this->emitSelf('songs:dedicatable:update');
    }

    protected function updateAudioSourceStatuses(): void
    {
        $isLiveBroadcasting = $this->selectedType === 'dedicated';
        if ($this->pauseAllAudioSources) {
            if ($isLiveBroadcasting) {
                $this->songs = $this->songs->map(function (LiveBroadcasting $liveBroadcasting) {
                    /** @var Song $song */
                    $song = $liveBroadcasting->song;
                    $song->selected = in_array($song->id, $this->selectedSongIds, true);
                    $song->playing = false;
                    $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);
                    $song->dedicationUser = $liveBroadcasting->dedicationUser;

                    return $song;
                });
            } else {
                $this->songs = $this->songs->map(function (Song $song) {
                    $song->selected = in_array($song->id, $this->selectedSongIds, true);
                    $song->playing = false;
                    $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);
                    $song->dedicationUser = null;

                    return $song;
                });
            }

            return;
        }
        $this->selectedSongIds = [];
        if ($isLiveBroadcasting) {
            $this->songs = $this->songs->map(function (LiveBroadcasting $liveBroadcasting) {
                /** @var Song $song */
                $song = $liveBroadcasting->song;
                $isPlaying = $song->id === $this->playedAudioSourceId;
                if ($isPlaying) {
                    $this->selectedSongIds[] = $song->id;
                }
                $song->selected = $isPlaying;
                $song->playing = $isPlaying;
                $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);
                $song->dedicationUser = $liveBroadcasting->dedicationUser;

                return $song;
            });
        } else {
            $this->songs = $this->songs->map(function (Song $song) {
                $isPlaying = $song->id === $this->playedAudioSourceId;
                if ($isPlaying) {
                    $this->selectedSongIds[] = $song->id;
                }
                $song->selected = $isPlaying;
                $song->playing = $isPlaying;
                $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);
                $song->dedicationUser = null;

                return $song;
            });
        }
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \JsonException
     */
    public function setDedicatableSongIds(): void
    {
        if (! Auth::check()) {
            return;
        }
        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::user()->id,
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
        }
        $songWinmediaIds = $this->songs->pluck('imedia')->unique()->values()->toArray();
        if (count(array_filter($songWinmediaIds)) > 0) {
            $this->dedicatableSongWinmediaIds = app(DedicationService::class)->verifySongsEligibility(
                $this->selectedRadioStation->winmedia_id,
                $songWinmediaIds
            );
        } else {
            $this->dedicatableSongWinmediaIds = [];
        }
    }

    /** @throws \Exception */
    public function render(): View
    {
        $links = null;
        if ($this->initialized) {
            switch ($this->selectedType) {
                case 'elasticsearch':
                    $esSongs = app(CustomSearchService::class)->searchSongs(
                        $this->search,
                        $this->elementsPerPage,
                        $this->elementsPerPage * ($this->page - 1)
                    )['hits'];
                    $songs = array_map(function ($esSong) {
                        $song = Song::find($esSong['_source']['id']);
                        $song['selected'] = $esSong['_source']['audio_source_class'] === $this->playedAudioSourceClass
                            && $esSong['_source']['id'] === $this->playedAudioSourceId;
                        $song['playing'] = $this->pauseAllAudioSources
                            && $esSong['_source']['audio_source_class'] === $this->playedAudioSourceClass
                            && $esSong['_source']['id'] === $this->playedAudioSourceId;
                        $song['performer_id'] = $esSong['_source']['performer_id'];
                        $song['performer_name'] = $esSong['_source']['performer_name'];
                        $song['album_id'] = $esSong['_source']['album_id'];
                        $song['album_name'] = $esSong['_source']['album_name'];

                        return $song;
                    }, $esSongs['hits']);

                    $songs =
                        (new LengthAwarePaginator($songs, $esSongs['total']['value'], $this->elementsPerPage, $this->page));
                    break;
                case 'news':
                    $songs = Song::musicWinmediaType()
                        ->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])
                        ->where('_station', config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE', '%/' . $this->selectedRadioStation->winmedia_id . '/%')
                        ->news()
                        ->orderByDesc('imedia')
                        ->paginate($this->elementsPerPage);
                    break;
                case 'newsGO':
                    $songs = Song::musicWinmediaType()
                        ->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])
                        ->where('_station', config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE', '%/' . $this->selectedRadioStation->winmedia_id . '/%')
                        ->news()
                        ->localGO()
                        ->orderByDesc('imedia')
                        ->paginate($this->elementsPerPage);
                    break;
                case 'dedicated':
                    $songs = LiveBroadcasting::with(['song.performerRelationship', 'song.albumRelationship', 'song.labelRelationship', 'dedicationUser.media'])
                        ->where('winmedia_radio_station_id', $this->selectedRadioStation->winmedia_id)
                        ->whereNotNull('song_id')
                        ->whereNotNull('dedication_user_id')
                        ->orderByDesc('started_at')
                        ->paginate($this->elementsPerPage);
                    break;
                default:
                    $songs = Song::with(['performerRelationship', 'albumRelationship', 'labelRelationship'])
                        ->orderByDesc('id')
                        ->paginate($this->elementsPerPage);
                    break;
            }

            $links = $songs->links();
            $this->songs = collect($songs->items());

            $this->updateAudioSourceStatuses();
        }

        return view('livewire.search.song-results', [
            'links' => $links,
        ]);
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllAudioSources = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllAudioSources = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllAudioSources = false;
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }

    public function previousPage($pageName = 'page'): void
    {
        $this->setPage(max($this->paginators[$pageName] - 1, 1), $pageName);
        $this->emitSelf('songs:dedicatable:update');
        $this->emit('scroll:top', '#template');
    }

    public function nextPage($pageName = 'page'): void
    {
        $this->setPage($this->paginators[$pageName] + 1, $pageName);
        $this->emitSelf('songs:dedicatable:update');
        $this->emit('scroll:top', '#template');
    }

    public function gotoPage($page, $pageName = 'page'): void
    {
        $this->setPage($page, $pageName);
        $this->emitSelf('songs:dedicatable:update');
        $this->emit('scroll:top', '#template');
    }
}

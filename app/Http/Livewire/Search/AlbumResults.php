<?php

namespace App\Http\Livewire\Search;

use App\Models\Map\Place;
use App\Models\Performers\Album;
use App\Models\Performers\Performer;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\WithPagination;

class AlbumResults extends Component
{
    use WithPagination;

    public bool $initialized = false;

    public ?Place $place = null;

    public string $placeId = '';

    public ?Performer $performer = null;

    public string $performerId = '';

    public array $selectedAlbumIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllAlbums = false;

    public int $elementsPerPage = 33;

    public Collection $albums;

    public string $albumOrder = 'alphabetical_asc';

    protected $queryString = [
        'performerId' => ['except' => ''],
        'placeId' => ['except' => ''],
        'albumOrder' => ['except' => 'alphabetical_asc'],
    ];

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    public function mount(): void
    {
        $this->setPage($this->page);
        $this->albums = collect();
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        if ($this->placeId) {
            $this->place = Place::find($this->placeId);
        }
        if ($this->performerId) {
            $this->performer = Performer::find($this->performerId);
        }
    }

    protected function setPlayedAudioSource(
        ?string $audioSourceClass,
        ?int $audioSourceId,
    ): void {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    /** @throws \Exception */
    public function init(): void
    {
        $this->initialized = true;
    }

    /** @throws \Exception */
    public function render(): View
    {
        if ($this->place) {
            $placeId = $this->place->id;
            $albums = Album::with(['contentLocations'])
                ->where(function (Builder $query) use ($placeId) {
                    $query->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                        $query->where('location_id', $placeId)
                            ->where('location_type', Place::class);
                    })
                        ->orWhereHas('label', function (Builder $query) use ($placeId): void {
                            $query->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                                $query->where('location_id', $placeId)
                                    ->where('location_type', Place::class);
                            });
                        });
                })
                ->where(function (Builder $query) {
                    $query->whereNull('compilation_id')
                        ->orWhereIn('id', function ($subquery) {
                            $subquery->select(\DB::raw('MIN(id)'))
                                ->from('albums')
                                ->whereNotNull('compilation_id')
                                ->groupBy('compilation_id');
                        });
                });

            switch ($this->albumOrder) {
                case 'last_released':
                    $albums = $albums
                        ->orderByRaw('published_at DESC NULLS LAST')
                        ->orderByDesc('created_at');
                    break;
                case 'last_created':
                    $albums = $albums->orderByDesc('created_at');
                    break;
                case 'alphabetical_asc':
                    $albums = $albums->orderByRaw('LOWER(TRIM(name)) ASC');
                    break;
                case 'alphabetical_desc':
                    $albums = $albums->orderByRaw('LOWER(TRIM(name)) DESC');
                    break;
                default:
                    $albums = $albums->orderBy('id', 'desc');

            }
            $albums = $albums->paginate($this->elementsPerPage);
        } elseif ($this->performer) {
            $albums = Album::with(['contentLocations'])
                ->where('performer_id', $this->performer->id)
                ->orderByDesc('published_at')
                ->paginate($this->elementsPerPage);
        } else {
            $albums = Album::with(['contentLocations'])->orderBy('id', 'desc')->paginate($this->elementsPerPage);
        }
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'search_album_results',
            routeParams: [],
            livewireComponent: $this
        );

        $links = $albums->links();
        $this->albums = collect($albums->items());

        $this->updateAudioAlbumsStatuses();

        return view('livewire.search.album-results', [
            'links' => $links,
        ]);
    }

    public function setAlbumsOrderToDisplay(string $albumOrder): void
    {
        $this->albumOrder = $albumOrder;
        $this->resetPage();
    }

    protected function updateAudioAlbumsStatuses(): void
    {
        if ($this->pauseAllAlbums) {
            $this->albums = $this->albums->map(function (Album $album) {
                $album->selected = in_array($album->id, $this->selectedAlbumIds, true);
                $album->playing = false;

                return $album;
            });

            return;
        }
        $this->selectedAlbumIds = [];
        $this->albums = $this->albums->map(function (Album $album) {
            $isPlaying = $album::class === $this->playedAudioSourceClass
                && $album->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedAlbumIds[] = $album->id;
            }
            $album->selected = $isPlaying;
            $album->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $album;
        });
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllAlbums = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllAlbums = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllAlbums = false;
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }
}

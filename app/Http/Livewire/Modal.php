<?php

namespace App\Http\Livewire;

use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Livewire\Component;

class Modal extends Component
{
    public ?string $alias = null;

    public array $params = [];

    protected $listeners = [
        'modal:show' => 'showModal',
        'modal:close' => 'closeModal',
        'modal:reset' => 'resetModal',
    ];

    public function showModal(string $alias, array $params = []): void
    {
        if ($this->shouldNotBeOpened($alias)) {
            return;
        }
        $this->alias = $alias;
        $this->params = $params;

        $this->emit('bootstrap:modal:show');
    }

    protected function shouldNotBeOpened(string $alias): bool
    {
        return $this->userShouldNotBeAuthenticatedButIs($alias) || $this->requiresAuthenticationButUserIsNot($alias);
    }

    protected function userShouldNotBeAuthenticatedButIs(string $alias): bool
    {
        return in_array($alias, [
            'login',
            'create-account',
            'forgotten-password',
        ], true)
            && Auth::check();
    }

    protected function requiresAuthenticationButUserIsNot(string $alias): bool
    {
        return in_array($alias, [
            'change-profile-picture',
            'create-playlist',
            'add-son-to-playlist',
            'dedication-validation',
            'choose-default-radio-station',
            'share-song',
            'share-album',
            'share-podcast',
            'share-emission',
            'share-playlist',
            'share-article',
        ], true)
            && ! Auth::check();
    }

    public function closeModal(): void
    {
        $this->emit('bootstrap:modal:hide');
    }

    public function resetModal(): void
    {
        $this->reset();
    }

    public function render(): View
    {
        return view('livewire.modal');
    }
}

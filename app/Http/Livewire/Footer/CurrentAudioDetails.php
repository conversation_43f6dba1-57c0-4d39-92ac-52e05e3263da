<?php

namespace App\Http\Livewire\Footer;

use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Events\Event;
use App\Models\News\NewsArticle;
use App\Models\Radio\RadioStation;
use App\Services\Users\UserJourneysService;
use Browser;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class CurrentAudioDetails extends Component
{
    public bool $initialized = false;

    public string $audioSourceClass;

    public int $audioSourceId;

    public ?int $songId = null;

    public ?int $songFavoriteUsersCount = null;

    public bool $songIsInFavorites = false;

    public ?string $coverMobile = null;

    public ?string $coverThumb = null;

    public ?string $dedicationUserCoverThumb = null;

    public ?string $dedicationUserName = null;

    public ?string $title = null;

    public ?string $subtitle = null;

    public ?string $details = null;

    public ?string $additionalDetails = null;

    public array $binding = [];

    public bool $keepUpdating = false;

    public bool $canAddToFavorites = false;

    public bool $canAddToPlaylist = false;

    public bool $currentSongHasBeenAddedToFavorites = false;

    public array $lastRadioStationsLiveBroadcastIds = [];

    public bool $currentAudioSourceIsPlaying = false;

    protected $listeners = [
        'audio:current:details:update' => 'configureCurrentAudioDetailsDisplaying',
        'user:authenticated' => '$refresh',
        'user:unauthenticated' => '$refresh',
        'songs:favorites:updated' => 'setSongFavoriteStatus',
        'player:audio:source:paused' => 'setPlayingStatus',
        'player:audio:source:played' => 'setPlayingStatus',
    ];

    /** @throws \Exception */
    public function configureCurrentAudioDetailsDisplaying(string $audioSourceClass, int $audioSourceId, bool $canPreviousNext = false): void
    {
        $this->audioSourceClass = $audioSourceClass;
        $this->audioSourceId = $audioSourceId;
        $this->updateCurrentAudioDetails($canPreviousNext);
    }

    /** @throws \Exception */
    public function updateCurrentAudioDetails(bool $canPreviousNext = false): void
    {
        $lastRadioStationsLiveBroadcasts = lastRadioStationsLiveBroadcasts();
        switch ($this->audioSourceClass) {
            case RadioStation::class:
                $radioStation = radioStations()->firstOrFail('id', $this->audioSourceId);
                $broadcast = $lastRadioStationsLiveBroadcasts->where(
                    'winmedia_radio_station_id',
                    $radioStation->winmedia_id
                )->first();
                $song = $broadcast?->song;
                $podcast = $broadcast?->podcast;
                if ($song) {
                    $this->songId = $song->id;
                    $this->songFavoriteUsersCount = $song->favorite_users_count;
                    $this->authorizeActions(addToFavorites: $song->isMusic ?? false, addToPlaylist: $song->isMusic ?? false);
                    $this->setPlayedMobileAudioDetails(
                        audioSourceClass: Song::class,
                        audioSourceId: $song->id,
                        coverMobile: $song->cover_thumb,
                        title: $song->title,
                        subtitle: $song->performer,
                        details: $song->yearFourDigit,
                        additionalDetails: $song->publisher,
                        dedicationUserCoverThumb: $broadcast->dedicationUser?->getFirstMediaUrl(
                            'profile_picture',
                            'dedication_thumb_front_mobile'
                        ),
                        dedicationUserName: $broadcast->dedicationUser?->username,
                        binding: [
                            'performerId' => $song->performerRelationship?->id,
                            'albumId' => $song->albumRelationship?->id,
                            'placeLabelId' => $song->labelRelationship?->place()?->id,
                        ]
                    );
                    $this->setPlayedAudioDetails(
                        coverThumb: $song->cover_thumb,
                        title: $song->title,
                        subtitle: $song->performerRelationship?->name ?: $song->performer,
                        details: $song->yearFourDigit,
                        additionalDetails: $song->publisher,
                        coverThumbType: 'image/jpg',
                        coverThumbSize: '300x300',
                        dedicationUserCoverThumb: $broadcast->dedicationUser?->getFirstMediaUrl(
                            'profile_picture',
                            'dedication_thumb_front'
                        ),
                        dedicationUserName: $broadcast->dedicationUser?->username,
                        keepUpdating: true,
                        canPreviousNext: $canPreviousNext,
                        binding: [
                            'performerId' => Browser::isDesktop() ? $song->performerRelationship?->id : null,
                            'albumId' => Browser::isDesktop() ? $song->albumRelationship?->id : null,
                            'placeLabelId' => Browser::isDesktop() ? $song->labelRelationship?->place()?->id : null,
                        ]
                    );
                    $this->setSongFavoriteStatus();
                } else {
                    $this->songId = null;
                    $this->songFavoriteUsersCount = null;
                    $this->authorizeActions(addToFavorites: false, addToPlaylist: false);
                    $this->setPlayedMobileAudioDetails(
                        audioSourceClass: $podcast ? Podcast::class : RadioStation::class,
                        audioSourceId: $podcast ? $podcast->id : $radioStation->id,
                        coverMobile: $podcast ? $podcast->getFirstMediaUrl('cover', 'player_mobile') : $radioStation->getFirstMediaUrl('cover', 'player_mobile'),
                        title: $podcast ? $podcast->title : 'En direct',
                        subtitle: $podcast ? $podcast->program->title : $radioStation->name,
                        details: $podcast ? 'En direct' : $radioStation->label,
                        binding: [
                            'podcastId' => $podcast?->id,
                            'programId' => $podcast?->program_id,
                        ]
                    );
                    $this->setPlayedAudioDetails(
                        coverThumb: $podcast ? $podcast->getFirstMediaUrl('cover', 'small') : $radioStation->getFirstMediaUrl('cover', 'small'),
                        title: $podcast ? $podcast->title : 'En direct',
                        subtitle: $podcast ? $podcast->program->title : $radioStation->name,
                        details: $podcast ? 'En direct' : $radioStation->label,
                        coverThumbType: 'image/webp',
                        coverThumbSize: '337x337',
                        keepUpdating: true,
                        canPreviousNext: $canPreviousNext,
                        binding: [
                            'podcastId' => Browser::isDesktop() ? $podcast?->id : null,
                            'programId' => Browser::isDesktop() ? $podcast?->program_id : null,
                        ]
                    );
                }
                break;
            case Podcast::class:
                $podcast = Podcast::findOrFail($this->audioSourceId);
                $this->songId = null;
                $this->songFavoriteUsersCount = null;
                $this->authorizeActions(addToFavorites: false, addToPlaylist: false);
                $this->setPlayedMobileAudioDetails(
                    audioSourceClass: Podcast::class,
                    audioSourceId: $podcast->id,
                    coverMobile: $podcast->getFirstMediaUrl('cover', 'player_mobile'),
                    title: $podcast->title,
                    subtitle: $podcast->program->title,
                    binding: [
                        'podcastId' => $podcast->id,
                        'programId' => $podcast->program_id,
                    ]
                );
                $this->setPlayedAudioDetails(
                    coverThumb: $podcast->getFirstMediaUrl('cover', 'small'),
                    title: $podcast->title,
                    subtitle: $podcast->program->title,
                    coverThumbType: 'image/webp',
                    coverThumbSize: '337x337',
                    canPreviousNext: $canPreviousNext,
                    binding: [
                        'podcastId' => Browser::isDesktop() ? $podcast->id : null,
                        'programId' => Browser::isDesktop() ? $podcast->program_id : null,
                    ]
                );
                break;
            case Song::class:
                $song = Song::allTypes()->with(['performerRelationship', 'albumRelationship', 'labelRelationship'])->withCount('favoriteUsers')->findOrFail($this->audioSourceId);
                $this->songId = $song->id;
                $this->songFavoriteUsersCount = $song->favorite_users_count;
                $this->authorizeActions(addToFavorites: $song->isMusic ?? false, addToPlaylist: $song->isMusic ?? false);
                $this->setPlayedMobileAudioDetails(
                    audioSourceClass: Song::class,
                    audioSourceId: $song->id,
                    coverMobile: $song->cover_thumb,
                    title: $song->title,
                    subtitle: $song->performer,
                    details: $song->yearFourDigit,
                    additionalDetails: $song->publisher,
                    binding: [
                        'performerId' => $song->performerRelationship?->id,
                        'albumId' => $song->albumRelationship?->id,
                        'placeLabelId' => $song->labelRelationship?->place()?->id,
                    ]
                );
                $this->setPlayedAudioDetails(
                    coverThumb: $song->cover_thumb,
                    title: $song->title,
                    subtitle: $song->performer,
                    details: $song->yearFourDigit,
                    additionalDetails: $song->publisher,
                    coverThumbType: 'image/jpg',
                    coverThumbSize: '300x300',
                    canPreviousNext: $canPreviousNext,
                    binding: [
                        'performerId' => Browser::isDesktop() ? $song->performerRelationship?->id : null,
                        'albumId' => Browser::isDesktop() ? $song->albumRelationship?->id : null,
                        'placeLabelId' => Browser::isDesktop() ? $song->labelRelationship?->place()?->id : null,
                    ]
                );
                $this->setSongFavoriteStatus();
                break;
            case NewsArticle::class:
                $article = NewsArticle::findOrFail($this->audioSourceId);
                $this->songId = null;
                $this->songFavoriteUsersCount = null;
                $subtitle = $article->audio_caption
                    ?: $article->published_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y');
                $this->authorizeActions(addToFavorites: false, addToPlaylist: false);
                $this->setPlayedMobileAudioDetails(
                    audioSourceClass: NewsArticle::class,
                    audioSourceId: $article->id,
                    coverMobile: $article->getFirstMediaUrl('illustrations', 'player_mobile'),
                    title: $article->title,
                    subtitle: $subtitle,
                    binding: [
                        'articleId' => $article->id,
                    ]
                );
                $this->setPlayedAudioDetails(
                    coverThumb: $article->getFirstMediaUrl('illustrations', 'player_thumb'),
                    title: $article->title,
                    subtitle: $subtitle,
                    coverThumbType: 'image/webp',
                    coverThumbSize: '337x337',
                    canPreviousNext: $canPreviousNext,
                    binding: [
                        'articleId' => Browser::isDesktop() ? $article->id : null,
                    ]
                );
                break;
            case Event::class:
                $event = Event::findOrFail($this->audioSourceId);
                $this->songId = null;
                $this->songFavoriteUsersCount = null;
                if (! $event->audio_caption) {
                    $startDate = $event->started_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y');
                    $endDate = $event->started_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y')
                    !== $event->ended_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y')
                        ? ' - ' . $event->ended_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y')
                        : '';
                    $eventPeriod = $startDate . $endDate;
                }
                $this->authorizeActions(addToFavorites: false, addToPlaylist: false);
                $this->setPlayedMobileAudioDetails(
                    audioSourceClass: Event::class,
                    audioSourceId: $event->id,
                    coverMobile: $event->getFirstMediaUrl('cover', 'player_mobile'),
                    title: $event->title,
                    subtitle: $eventPeriod ?? $event->audio_caption,
                    binding: [
                        'eventId' => $event->id,
                    ]
                );
                $this->setPlayedAudioDetails(
                    coverThumb: $event->getFirstMediaUrl('cover', 'player_thumb'),
                    title: $event->title,
                    subtitle: $eventPeriod ?? $event->audio_caption,
                    coverThumbType: 'image/webp',
                    coverThumbSize: '337x337',
                    canPreviousNext: $canPreviousNext,
                    binding: [
                        'eventId' => Browser::isDesktop() ? $event->id : null,
                    ]
                );
                break;
            default:
                throw new Exception('Unrecognized ' . $this->audioSourceClass . ' source class.');
        }
        $currentRadioStationsLiveBroadcastIds = $lastRadioStationsLiveBroadcasts->pluck('id')->toArray();
        $this->triggerRadioBroadcastUpdate($currentRadioStationsLiveBroadcastIds);
        $this->lastRadioStationsLiveBroadcastIds = $currentRadioStationsLiveBroadcastIds;
        $this->initialized = true;
    }

    protected function authorizeActions(bool $addToFavorites, $addToPlaylist): void
    {
        $this->canAddToFavorites = $addToFavorites;
        $this->canAddToPlaylist = $addToPlaylist;
    }

    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    protected function setPlayedMobileAudioDetails(
        string $audioSourceClass,
        int $audioSourceId,
        string $coverMobile,
        string $title,
        string $subtitle,
        ?string $details = null,
        ?string $additionalDetails = null,
        ?string $dedicationUserCoverThumb = null,
        ?string $dedicationUserName = null,
        array $binding = []
    ): void {
        if (Browser::isDesktop()) {
            return;
        }
        $coverMobileHasChanged = $this->coverMobile !== $coverMobile;
        $titleHasChanged = $this->title !== $title;
        $subtitleHasChanged = $this->subtitle !== $subtitle;
        $detailsHasChanged = $this->details !== $details;
        $additionalDetailsHasChanged = $this->additionalDetails !== $additionalDetails;
        $dedicationUserNameHasChanged = $this->dedicationUserName !== $dedicationUserName;
        if ($coverMobileHasChanged || $titleHasChanged || $subtitleHasChanged || $detailsHasChanged || $additionalDetailsHasChanged || $dedicationUserNameHasChanged) {
            $this->coverMobile = $coverMobile;
            $this->emitTo(
                'mobile-player.current-audio-details',
                'audio:current:details:updated',
                $audioSourceClass,
                $audioSourceId,
                $coverMobile,
                $title,
                $subtitle,
                $details,
                $additionalDetails,
                $dedicationUserCoverThumb,
                $dedicationUserName,
                $this->canAddToFavorites,
                $this->canAddToPlaylist,
                $binding,
                $this->songFavoriteUsersCount
            );
        }
    }

    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    protected function setPlayedAudioDetails(
        string $coverThumb,
        string $title,
        string $subtitle,
        ?string $details = null,
        ?string $additionalDetails = null,
        string $coverThumbType = 'image/webp',
        string $coverThumbSize = '337x337',
        ?string $dedicationUserCoverThumb = null,
        ?string $dedicationUserName = null,
        bool $keepUpdating = false,
        bool $canPreviousNext = false,
        array $binding = []
    ): void {
        if (
            $this->coverThumb !== $coverThumb
            && $this->title !== $title
            && $this->subtitle !== $subtitle
            && $this->details !== $details
            && $this->additionalDetails !== $additionalDetails
        ) {
            $metadata = [
                'title' => $title,
                'artist' => $subtitle,
                'album' => $details,
                'cover' => $coverThumb,
                'coverType' => $coverThumbType,
                'coverSize' => $coverThumbSize,
            ];
            $this->emit('player:audio:details:updated', $metadata, $canPreviousNext);
        }
        $this->coverThumb = $coverThumb;
        $this->title = $title;
        $this->subtitle = $subtitle;
        $this->details = $details;
        $this->additionalDetails = $additionalDetails;
        $this->binding = $binding;
        $this->dedicationUserCoverThumb = $dedicationUserCoverThumb;
        $this->dedicationUserName = $dedicationUserName;
        $this->keepUpdating = $keepUpdating;
    }

    public function setSongFavoriteStatus(?string $action = null): void
    {
        $this->songIsInFavorites = Auth::user() && Auth::user()->favoriteSongs()->where('id', $this->songId)->exists();
        if ($action === 'add') {
            $this->songFavoriteUsersCount++;
        } elseif ($action === 'remove') {
            $this->songFavoriteUsersCount--;
        }
    }

    /** @throws \Exception */
    protected function triggerRadioBroadcastUpdate(array $currentRadioStationsLiveBroadcastIds): void
    {
        if (! $currentRadioStationsLiveBroadcastIds) {
            return;
        }
        if ($this->lastRadioStationsLiveBroadcastIds
            && ! array_diff($this->lastRadioStationsLiveBroadcastIds, $currentRadioStationsLiveBroadcastIds)) {
            return;
        }
        $this->emit('radio:broadcast:updated');
    }

    public function render(): View
    {
        return view('livewire.footer.current-audio-details');
    }

    public function setPlayingStatus(): void
    {
        $this->currentAudioSourceIsPlaying = app(UserJourneysService::class)->getPlayerPlayingStatus();
    }
}

<?php

namespace App\Http\Livewire\Footer;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class UpcomingAudioDetails extends Component
{
    public bool $loading = true;

    public string $illustration;

    public string $title;

    public string $subtitle;

    public function getUpcomingAudioSource(): void
    {
        $this->loading = false;
        $this->illustration = 'https://loremflickr.com/48/48';
        $this->title = 'Someone like you';
        $this->subtitle = 'Adèle';
    }

    public function render(): View
    {
        return view('livewire.footer.upcoming-audio-details');
    }
}

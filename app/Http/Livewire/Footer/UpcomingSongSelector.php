<?php

namespace App\Http\Livewire\Footer;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class UpcomingSongSelector extends Component
{
    protected $listeners = [
        'user:authenticated' => '$refresh',
        'user:unauthenticated' => '$refresh',
    ];

    public function chooseUpcomingSong()
    {
        if (! Auth::check()) {
            $this->emit('modal:show', 'login', ['destinationRouteKey' => 'dedicace']);
        } else {
            $this->emitTo('router', 'nav:to', 'dedicace');
        }
    }

    public function render(): View
    {
        return view('livewire.footer.upcoming-song-selector');
    }
}

<?php

namespace App\Http\Livewire\Footer;

use App\Models\Map\Place;
use App\Models\Radio\RadioStation;
use App\Services\Users\UserJourneysService;
use Browser;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Livewire\Component;

class RadioStationStreamSelector extends Component
{
    public Collection $radioStations;

    public ?RadioStation $selectedRadioStation = null;

    public ?RadioStation $streamedRadioStation = null;

    public int $initialRadioStationWinmediaID = 0;

    public ?string $initialRoute = null;

    protected $listeners = [
        'geolocation:position:found' => 'browserLocationFound',
        'player:audio:source:updated' => 'updateStreamedRadioStationFromEvent',
    ];

    /** @throws \Exception */
    public function mount(): void
    {
        $this->radioStations = radioStations();
        $this->initialRoute = request()->route()->getName();
        if (array_key_exists('station', request()->query())) {
            $this->initialRadioStationWinmediaID = intval(request()->query()['station']);
        }
    }

    public function init(): void
    {
        if ($this->initialRadioStationWinmediaID === 0) {
            if (! Auth::user()?->settings->use_geolocation) {
                if (Auth::user()?->settings->default_radio_station_id) {
                    $defaultStreamedRadioStation = $this->radioStations->where('id', Auth::user()->settings->default_radio_station_id)->sole();
                } else {
                    $defaultStreamedRadioStation = $this->radioStations->first();
                }
                $this->streamRadioStation($defaultStreamedRadioStation->id, playRadioStream: false);
            }
        } else {
            $defaultStreamedRadioStation = $this
                ->radioStations
                ->where('winmedia_id', $this->initialRadioStationWinmediaID)
                ->first();

            $defaultStreamedRadioStation ??= $this->radioStations->first();

            $this->streamRadioStation($defaultStreamedRadioStation->id, playRadioStream: false);

            if ($this->initialRoute !== 'app.map.index') {
                $this->emit('history:clean:url:parameters');
            }
        }
    }

    public function streamRadioStation(int $radioStationId, bool $playRadioStream): void
    {
        $streamedRadioStation = $this->radioStations->where('id', $radioStationId)->sole();
        $this->selectedRadioStation = $streamedRadioStation;
        $this->streamedRadioStation = $streamedRadioStation;
        $this->emitTo(
            'footer.audio-player',
            'player:audio:source:update',
            RadioStation::class,
            $this->streamedRadioStation->id,
            $playRadioStream
        );
    }

    public function updateStreamedRadioStationFromDropDown(int $radioStationId, bool $playRadioStream): void
    {
        if (! Browser::isDesktop()) {
            $this->selectedRadioStation = $this->radioStations->where('id', $radioStationId)->sole();

            return;
        }
        $this->streamRadioStation($radioStationId, $playRadioStream);
    }

    public function streamSelectedRadioStation(): void
    {
        if (! Browser::isDesktop()) {
            $this->streamRadioStation($this->selectedRadioStation->id, true);
        }
    }

    /** @throws \Exception */
    public function browserLocationFound(?float $browserLatitude = null, ?float $browserLongitude = null): void
    {
        // Location is found without coordinates
        if (! $browserLatitude || ! $browserLongitude) {
            app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId(null);
            $defaultStreamedRadioStation = $this->radioStations->first();
            $this->streamRadioStation($defaultStreamedRadioStation->id, playRadioStream: false);

            return;
        }
        $closestRadioStation = $this->getClosestRadioStationFromUser($browserLatitude, $browserLongitude);
        app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId($closestRadioStation->id);
        $this->streamRadioStation($closestRadioStation->id, playRadioStream: false);
    }

    protected function getClosestRadioStationFromUser(float $browserLatitude, float $browserLongitude): RadioStation
    {
        $locatableRadioStations = radioStations()
            ->load([
                'contentLocations' => function ($query): void {
                    $query->where('location_type', Place::class);
                    $query->with(['location' => function (MorphTo $morphTo) {
                        $morphTo->morphWith([
                            Place::class => ['currentPoint'],
                        ]);
                    }]);
                },
            ])
            ->filter(fn (RadioStation $station): bool => (bool) $station->point());

        foreach ($locatableRadioStations as $locatableRadioStation) {
            $position = $locatableRadioStation->point();

            $distanceBetweenBrowserAndRadio = $this->getDistanceBetweenTwoCoords(
                $browserLatitude,
                $browserLongitude,
                $position->getLatitude(),
                $position->getLongitude()
            );

            // @phpstan-ignore-next-line
            $locatableRadioStation->distance = $distanceBetweenBrowserAndRadio;
        }

        $closestRadioStation = $locatableRadioStations->where('distance', $locatableRadioStations->min('distance'))->first();

        // @phpstan-ignore-next-line
        return $closestRadioStation->distance <= 70 ? $closestRadioStation : $locatableRadioStations->first();
    }

    protected function getDistanceBetweenTwoCoords(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $radianInKm = 6371; // km
        $distanceLat = $this->toRad($lat2 - $lat1);
        $distanceLon = $this->toRad($lon2 - $lon1);
        $lat1 = $this->toRad($lat1);
        $lat2 = $this->toRad($lat2);
        $buffer = sin($distanceLat / 2)
            * sin($distanceLat / 2)
            + sin($distanceLon / 2)
            * sin($distanceLon / 2)
            * cos($lat1) * cos($lat2);
        $toConvert = 2 * atan2(sqrt($buffer), sqrt(1 - $buffer));

        return $radianInKm * $toConvert;
    }

    protected function toRad(float $value): float
    {
        return $value * M_PI / 180;
    }

    public function updateStreamedRadioStationFromEvent(string $audioSourceClass, int $audioSourceId): void
    {
        if ($audioSourceClass !== RadioStation::class) {
            return;
        }
        $streamedRadioStation = $this->radioStations->where('id', $audioSourceId)->sole();
        $this->selectedRadioStation = $streamedRadioStation;
        $this->streamedRadioStation = $streamedRadioStation;
    }

    public function render(): View
    {
        return view('livewire.footer.radio-station-stream-selector');
    }
}

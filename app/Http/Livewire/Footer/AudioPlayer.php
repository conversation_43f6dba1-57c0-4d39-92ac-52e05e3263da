<?php

namespace App\Http\Livewire\Footer;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Events\Event;
use App\Models\News\NewsArticle;
use App\Models\Performers\Album;
use App\Models\Radio\Program;
use App\Models\Radio\RadioStation;
use App\Services\Users\UserJourneysService;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Component;

class AudioPlayer extends Component
{
    public ?string $audioSourceClass = null;

    public ?int $audioSourceId = null;

    public array $audioSourceParams = [];

    public array $audioStream = [];

    public bool $canRandomize = false;

    public bool $randomizeMode = false;

    public bool $canSeekPrevious = false;

    public bool $isPlaying = false;

    public bool $canSeekNext = false;

    public bool $canRepeat = false;

    public bool $repeatMode = false;

    public array $alreadyPlayedAudioSourceListIds = [];

    protected $listeners = [
        'player:audio:source:update' => 'updateAudioSource',
        'player:audio:source:played' => 'playedAudioSource',
        'player:audio:source:paused' => 'pausedAudioSource',
        'player:audio:source:ended' => 'manageAudioSourceEnd',
        'radio:broadcast:updated' => 'displayRadioBroadcastProgressBar',
        'player:audio:source:seekPrevious' => 'seekPrevious',
        'player:audio:source:seekNext' => 'seekNext',
    ];

    /** @throws \Exception */
    public function updateAudioSource(
        string $audioSourceClass,
        int $audioSourceId,
        bool $playOnUpdate,
        array $audioSourceParams = [],
        ?int $audioPositionId = null,
        bool $needRadioRailRefresh = false,
    ): void {
        // Always setting player as not playing by default
        app(UserJourneysService::class)->setPlayerPlayingStatus(false);
        // Triggering play/pause on the same audio source just update the play/pause status.
        $sameAudioSourceClass = $audioSourceClass === $this->audioSourceClass;
        $sameAudioSourceId = $audioSourceId === $this->audioSourceId;
        $skipIdenticalAudioSourceCheck = $audioSourceClass === RadioStation::class && ! $this->isPlaying;
        ['played_sub_audio_source_id' => $subAudioSourceId] = app(UserJourneysService::class)->getPlayedSubAudioSource();
        $sameAudioPositionId = $audioPositionId === null || $audioPositionId === $subAudioSourceId;
        if ($sameAudioSourceClass && $sameAudioSourceId && ! $skipIdenticalAudioSourceCheck && $sameAudioPositionId) {
            if ($playOnUpdate) {
                $this->togglePlayPause();
            }

            return;
        }
        switch ($audioSourceClass) {
            case RadioStation::class:
                $radioStation = radioStations()->where('id', $audioSourceId)->sole();
                $metadata = $this->getMetadata(RadioStation::class, $radioStation);
                $this->authorizeActions(randomize: false, previous: false, next: false, repeat: false);
                $this->updatePlayerAudioSourceData($audioSourceClass, $audioSourceId, $audioSourceParams, $playOnUpdate);
                $this->updateAudioStream($radioStation->audio_stream, $playOnUpdate, $metadata);
                $this->updateDisplayedAudioDetails(RadioStation::class, $audioSourceId);
                $this->displayRadioBroadcastProgressBar();
                if ($needRadioRailRefresh === true) {
                    $this->emit('rail:radio:stations:refresh');
                }
                break;
            case Podcast::class:
                $podcast = Podcast::findOrFail($audioSourceId);
                $metadata = $this->getMetadata(Podcast::class, $podcast);
                $this->authorizeActions(randomize: false, previous: false, next: false, repeat: false);
                $this->updatePlayerAudioSourceData($audioSourceClass, $audioSourceId, $audioSourceParams, $playOnUpdate);
                $this->updateAudioStream($podcast->audio_stream, $playOnUpdate, $metadata);
                $this->updateDisplayedAudioDetails(Podcast::class, $audioSourceId);
                break;
            case Song::class:
                $song = Song::allTypes()->findOrFail($audioSourceId);
                $metadata = $this->getMetadata(Song::class, $song);
                $this->authorizeActions(randomize: false, previous: false, next: false, repeat: false);
                $this->updatePlayerAudioSourceData($audioSourceClass, $audioSourceId, $audioSourceParams, $playOnUpdate);
                $this->updateAudioStream($song->audio_stream, $playOnUpdate, $metadata);
                $this->updateDisplayedAudioDetails(Song::class, $audioSourceId);
                break;
            case Playlist::class:
                $this->authorizeActions(randomize: true, previous: true, next: true, repeat: true);
                $songs = $audioSourceId === 0
                    ? Auth::user()->favoriteSongs
                    : Playlist::findOrFail($audioSourceId)->songsAllTypes;
                $this->updateAlreadyPlayedAudioSourceListIds($audioPositionId, $songs, 'id');
                $this->updatePlayerAudioSourceData($audioSourceClass, $audioSourceId, $audioSourceParams, $playOnUpdate);
                $this->playNextSourceFromAudioSourcesList($songs, $playOnUpdate, false, true);
                break;
            case Album::class:
                $this->authorizeActions(randomize: true, previous: true, next: true, repeat: true);
                $songs = Album::findOrFail($audioSourceId)->songs;
                $this->updateAlreadyPlayedAudioSourceListIds($audioPositionId, $songs, 'id');
                $this->updatePlayerAudioSourceData($audioSourceClass, $audioSourceId, $audioSourceParams, $playOnUpdate);
                $this->playNextSourceFromAudioSourcesList($songs, $playOnUpdate, false, true);
                break;
            case Program::class:
                $this->authorizeActions(randomize: true, previous: true, next: true, repeat: true);
                $podcasts = Program::findOrFail($audioSourceId)
                    ->podcasts()
                    ->with(['media', 'program'])
                    ->hasAudio()
                    ->orderBy('published_at', $audioSourceParams['order_by'])
                    ->get();
                $this->updateAlreadyPlayedAudioSourceListIds($audioPositionId, $podcasts, 'id');
                $this->updatePlayerAudioSourceData($audioSourceClass, $audioSourceId, $audioSourceParams, $playOnUpdate);
                $this->playNextSourceFromAudioSourcesList($podcasts, $playOnUpdate, false, true);
                break;
            case NewsArticle::class:
                $article = NewsArticle::findOrFail($audioSourceId);
                $metadata = $this->getMetadata(NewsArticle::class, $article);
                $this->authorizeActions(randomize: false, previous: false, next: false, repeat: false);
                $this->updatePlayerAudioSourceData($audioSourceClass, $audioSourceId, $audioSourceParams, $playOnUpdate);
                $this->updateAudioStream($article->audio_stream, $playOnUpdate, $metadata);
                $this->updateDisplayedAudioDetails(NewsArticle::class, $audioSourceId);
                break;
            case Event::class:
                $event = Event::findOrFail($audioSourceId);
                $metadata = $this->getMetadata(Event::class, $event);
                $this->authorizeActions(randomize: false, previous: false, next: false, repeat: false);
                $this->updatePlayerAudioSourceData($audioSourceClass, $audioSourceId, $audioSourceParams, $playOnUpdate);
                $this->updateAudioStream($event->audio_stream, $playOnUpdate, $metadata);
                $this->updateDisplayedAudioDetails(Event::class, $audioSourceId);
                break;
            default:
                throw new Exception('Unrecognized ' . $audioSourceClass . ' audio source.');
        }
    }

    protected function getMetadata(
        string $audioSourceClass,
        RadioStation|Podcast|Song|NewsArticle|Event $modelSource
    ): array {
        $metadata = [];
        switch ($audioSourceClass) {
            case RadioStation::class:
                $lastRadioStationsLiveBroadcasts = lastRadioStationsLiveBroadcasts();
                /** @var RadioStation $radioStation */
                $radioStation = $modelSource;
                $broadcast = $lastRadioStationsLiveBroadcasts->where(
                    'winmedia_radio_station_id',
                    $radioStation->winmedia_id
                )->first();
                $song = $broadcast?->song;
                $podcast = $broadcast?->podcast;
                if ($song) {
                    $metadata = [
                        'title' => $song->title,
                        'artist' => $song->performer,
                        'album' => $song->album,
                        'cover' => $song->cover_thumb,
                        'coverSize' => '300x300',
                        'coverType' => 'image/jpg',
                        'matomoTitle' => 'Station (' . $radioStation->winmedia_id . ') > ' . $radioStation->name,
                    ];
                } else {
                    $metadata = [
                        'title' => $podcast ? $podcast->title : 'En direct',
                        'artist' => $podcast ? $podcast->program->title : $radioStation->name,
                        'album' => '',
                        'cover' => $podcast ? $podcast->getFirstMediaUrl('cover', 'player_mobile') : $radioStation->getFirstMediaUrl('cover', 'player_mobile'),
                        'coverSize' => '337x337',
                        'coverType' => 'image/webp',
                        'matomoTitle' => 'Station (' . $radioStation->winmedia_id . ') > ' . $radioStation->name,
                    ];
                }
                break;
            case Song::class:
                /** @var Song $song */
                $song = $modelSource;
                $metadata = [
                    'title' => $song->title,
                    'artist' => $song->performer,
                    'album' => $song->album,
                    'cover' => $song->cover_thumb,
                    'coverSize' => '300x300',
                    'coverType' => 'image/jpg',
                    'matomoTitle' => 'Song (' . $song->id . ') > ' . $song->performer . ' - ' . $song->title . (Str::length(trim($song->version)) > 0 ? ' - ' . $song->version : ''),
                ];
                break;
            case Podcast::class:
                /** @var Podcast $podcast */
                $podcast = $modelSource;
                $metadata = [
                    'title' => $podcast->title,
                    'artist' => $podcast->program->title,
                    'album' => '',
                    'cover' => $podcast->getFirstMediaUrl('cover', 'player_mobile'),
                    'coverSize' => '337x337',
                    'coverType' => 'image/webp',
                    'matomoTitle' => 'Podcast (' . $podcast->id . ') > ' . $podcast->title . ' - ' . $podcast->published_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y')
                        . ' [Program ' . $podcast->program_id . ' - ' . $podcast->program->title . ']',
                ];
                break;
            case NewsArticle::class:
                /** @var NewsArticle $article */
                $article = $modelSource;
                $subtitle = $article->audio_caption
                    ?: $article->published_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y');
                $metadata = [
                    'title' => $article->title,
                    'artist' => $subtitle,
                    'album' => '',
                    'cover' => $article->getFirstMediaUrl('illustrations', 'player_mobile'),
                    'coverSize' => '337x337',
                    'coverType' => 'image/webp',
                    'matomoTitle' => 'NewsArticle (' . $article->id . ') > ' . $article->title . ' - ' . $article->published_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y'),
                ];
                break;
            case Event::class:
                /** @var Event $event */
                $event = $modelSource;
                $startDate = $event->started_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y');
                $endDate = $event->started_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y')
                    !== $event->ended_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y')
                        ? ' - ' . $event->ended_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y')
                        : '';
                $subtitle = $event->audio_caption ?: $startDate . $endDate;
                $metadata = [
                    'title' => $event->title,
                    'artist' => $subtitle,
                    'album' => '',
                    'cover' => $event->getFirstMediaUrl('cover', 'player_mobile'),
                    'coverSize' => '337x337',
                    'coverType' => 'image/webp',
                    'matomoTitle' => 'Event (' . $event->id . ') > ' . $event->title . ' - ' . $startDate . $endDate,
                ];
                break;
            default:
                $metadata = [];
        }

        return $metadata;
    }

    protected function togglePlayPause(): void
    {
        $this->isPlaying
            ? $this->emit('player:audio:source:pause')
            : $this->emit('player:audio:source:play');
        $this->isPlaying = ! $this->isPlaying;
        app(UserJourneysService::class)->setPlayerPlayingStatus($this->isPlaying);
    }

    protected function authorizeActions(bool $randomize, bool $previous, bool $next, bool $repeat): void
    {
        $this->canRandomize = $randomize;
        $this->canSeekPrevious = $previous;
        $this->canSeekNext = $next;
        $this->canRepeat = $repeat;
    }

    protected function updateAlreadyPlayedAudioSourceListIds(
        ?int $audioPositionId,
        Collection $sources,
        string $pluckId = 'id'
    ): void {
        if ($audioPositionId) {
            $sourceIndex = array_search($audioPositionId, $sources->pluck($pluckId)->toArray()) ?: 0;
            $alreadyPlayedAudioSourceListIds = [];
            for ($i = 0; $i < $sourceIndex; $i++) {
                array_push($alreadyPlayedAudioSourceListIds, $sources[$i]->id);
            }
            $this->alreadyPlayedAudioSourceListIds = $alreadyPlayedAudioSourceListIds;
        } else {
            $this->alreadyPlayedAudioSourceListIds = [];
        }
    }

    protected function updatePlayerAudioSourceData(
        string $audioSourceClass,
        int $audioSourceId,
        array $audioSourceParams,
        bool $playOnUpdate,

    ): void {
        $this->audioSourceClass = $audioSourceClass;
        $this->audioSourceId = $audioSourceId;
        $this->audioSourceParams = $audioSourceParams;
        app(UserJourneysService::class)->setPlayedAudioSource(
            audioSourceClass: $audioSourceClass,
            audioSourceId: $audioSourceId,
            audioSourceParams: $audioSourceParams,
        );
        app(UserJourneysService::class)->setPlayedSubAudioSource(
            subAudioSourceClass: null,
            subAudioSourceId: null
        );
        app(UserJourneysService::class)->setPlayerPlayingStatus($playOnUpdate);
        $this->emit('player:audio:source:updated', $audioSourceClass, $audioSourceId, $audioSourceParams);
    }

    protected function updateAudioStream(array $audioStream, bool $playOnUpdate, array $metadata = []): void
    {
        $this->audioStream = $audioStream;
        $canPreviousNext = $this->canSeekPrevious && $this->canSeekNext;
        $this->emit('player:audio:stream:updated', $audioStream, $playOnUpdate, $metadata, $canPreviousNext);
    }

    public function updateDisplayedAudioDetails(string $audioSourceClass, int $audioSourceId): void
    {
        $this->emitTo(
            'footer.current-audio-details',
            'audio:current:details:update',
            $audioSourceClass,
            $audioSourceId,
            $this->canSeekPrevious && $this->canSeekNext
        );
    }

    /** @throws \Exception */
    public function displayRadioBroadcastProgressBar(): void
    {
        if ($this->audioSourceClass !== RadioStation::class) {
            return;
        }
        $radioStation = radioStations()->where('id', $this->audioSourceId)->sole();
        $broadcast = lastRadioStationsLiveBroadcasts()
            ->where('winmedia_radio_station_id', $radioStation->winmedia_id)
            ->first();
        $this->emit(
            'player:main:radio:progress:force',
            $broadcast?->started_at->toW3CString(),
            $broadcast?->real_duration,
        );
    }

    /** @throws \Exception */
    protected function playNextSourceFromAudioSourcesList(
        Collection $audioSourcesList,
        bool $playOnUpdate,
        bool $stopWhenAudioListIsFinished = false,
        bool $ignoreRandom = false,
    ): void {
        if ($this->randomizeMode) {
            $remainingAudioSourcesToPlay = $audioSourcesList->whereNotIn('id', $this->alreadyPlayedAudioSourceListIds);
        } else {
            $indexCurrent = array_search(last($this->alreadyPlayedAudioSourceListIds), $audioSourcesList->pluck('id')->toArray()) === false
                ? 0
                : (array_search(last($this->alreadyPlayedAudioSourceListIds), $audioSourcesList->pluck('id')->toArray()) + 1);

            $remainingAudioSourcesToPlay = $audioSourcesList->slice($indexCurrent);
        }
        if ($remainingAudioSourcesToPlay->isEmpty()) {
            if ($stopWhenAudioListIsFinished && ! $this->repeatMode) {
                return;
            }
            if (! $this->repeatMode) {
                $selectedRadioId = $this->getSelectedRadioUniverseId();
                $this->emitSelf('player:audio:source:update', RadioStation::class, $selectedRadioId, true);

                return;
            }
            $remainingAudioSourcesToPlay = $audioSourcesList;
            $this->alreadyPlayedAudioSourceListIds = [];
        }
        $nextAudioSourceToPlay = ($this->randomizeMode && ! $ignoreRandom)
            ? $remainingAudioSourcesToPlay->random(1)->first()
            : $remainingAudioSourcesToPlay->first();
        $this->alreadyPlayedAudioSourceListIds = array_merge(
            $this->alreadyPlayedAudioSourceListIds,
            [$nextAudioSourceToPlay->id]
        );
        $metadata = $this->getMetadata($nextAudioSourceToPlay::class, $nextAudioSourceToPlay);
        $this->updatePlayerSubAudioSourceData($nextAudioSourceToPlay::class, $nextAudioSourceToPlay->id);
        $this->updateAudioStream($nextAudioSourceToPlay->audio_stream, $playOnUpdate, $metadata);
        $this->updateDisplayedAudioDetails($nextAudioSourceToPlay::class, $nextAudioSourceToPlay->id);
    }

    protected function getSelectedRadioUniverseId(): int
    {
        return app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
    }

    protected function updatePlayerSubAudioSourceData(
        string $subAudioSourceClass,
        int $subAudioSourceId,
    ): void {
        app(UserJourneysService::class)->setPlayedSubAudioSource($subAudioSourceClass, $subAudioSourceId);
        $this->emit('player:sub:audio:source:updated', $subAudioSourceClass, $subAudioSourceId);
    }

    public function requestPlayPause(): void
    {
        $this->emitSelf(
            'player:audio:source:update',
            $this->audioSourceClass,
            $this->audioSourceId,
            true,
            $this->audioSourceParams
        );
    }

    public function playedAudioSource(): void
    {
        $this->isPlaying = true;
        app(UserJourneysService::class)->setPlayerPlayingStatus(true);
    }

    public function pausedAudioSource(): void
    {
        $this->isPlaying = false;
        app(UserJourneysService::class)->setPlayerPlayingStatus(false);
    }

    /** @throws \Exception */
    public function manageAudioSourceEnd(): void
    {
        switch ($this->audioSourceClass) {
            case Playlist::class:
                $songs = $this->audioSourceId === 0
                    ? Auth::user()->favoriteSongs
                    : Playlist::findOrFail($this->audioSourceId)->songsAllTypes;
                $this->playNextSourceFromAudioSourcesList($songs, true);
                break;
            case Album::class:
                $songs = Album::findOrFail($this->audioSourceId)->songs;
                $this->playNextSourceFromAudioSourcesList($songs, true);
                break;
            case Program::class:
                $podcasts = Program::findOrFail($this->audioSourceId)
                    ->podcasts()
                    ->with(['media', 'program'])
                    ->hasAudio()
                    ->orderBy('published_at', $this->audioSourceParams['order_by'])
                    ->get();
                $this->playNextSourceFromAudioSourcesList($podcasts, true);
                break;
            default:
                $selectedRadioId = $this->getSelectedRadioUniverseId();
                $this->emitSelf('player:audio:source:update', RadioStation::class, $selectedRadioId, true);
                break;
        }
    }

    public function toggleRandomizeMode(): void
    {
        if ($this->canRandomize) {
            $this->randomizeMode = ! $this->randomizeMode;
        }
    }

    /** @throws \Exception */
    public function seekPrevious(): void
    {
        if ($this->canSeekPrevious) {
            switch ($this->audioSourceClass) {
                case Playlist::class:
                    $songs = $this->audioSourceId === 0
                        ? Auth::user()->favoriteSongs
                        : Playlist::findOrFail($this->audioSourceId)->songsAllTypes;
                    $this->playPreviousSourceFromAudioSourcesList($songs);
                    break;
                case Album::class:
                    $songs = Album::findOrFail($this->audioSourceId)->songs;
                    $this->playPreviousSourceFromAudioSourcesList($songs);
                    break;
                case Program::class:
                    $podcasts = Program::findOrFail($this->audioSourceId)
                        ->podcasts()
                        ->with(['media', 'program'])
                        ->hasAudio()
                        ->orderBy('published_at', $this->audioSourceParams['order_by'])
                        ->get();
                    $this->playPreviousSourceFromAudioSourcesList($podcasts);
                    break;
                default:
                    throw new Exception('Seeking previous audio source is forbidden for '
                        . $this->audioSourceClass . '.');
            }
        }
    }

    protected function playPreviousSourceFromAudioSourcesList(Collection $audioSourcesList): void
    {
        if ($this->randomizeMode) {
            $alreadyPlayedAudioSourceListIds = collect($this->alreadyPlayedAudioSourceListIds);
        } else {
            $indexCurrent = array_search(last($this->alreadyPlayedAudioSourceListIds), $audioSourcesList->pluck('id')->toArray()) === false
                ? 0
                : (array_search(last($this->alreadyPlayedAudioSourceListIds), $audioSourcesList->pluck('id')->toArray()) + 1);
            $alreadyPlayedAudioSourceListIds = $audioSourcesList->slice(0, $indexCurrent)->pluck('id');
        }
        // Removing current audio source id
        $alreadyPlayedAudioSourceListIds->pop();
        if ($alreadyPlayedAudioSourceListIds->isEmpty()) {
            return;
        }
        $this->alreadyPlayedAudioSourceListIds = $alreadyPlayedAudioSourceListIds->toArray();
        $previousAudioSourceId = $alreadyPlayedAudioSourceListIds->last();
        $previousAudioSource = $audioSourcesList->firstOrFail('id', $previousAudioSourceId);
        $metadata = $this->getMetadata($previousAudioSource::class, $previousAudioSource);
        $this->updatePlayerSubAudioSourceData($previousAudioSource::class, $previousAudioSource->id);
        $this->updateAudioStream($previousAudioSource->audio_stream, true, $metadata);
        $this->updateDisplayedAudioDetails($previousAudioSource::class, $previousAudioSource->id);
    }

    /** @throws \Exception */
    public function seekNext(): void
    {
        if ($this->canSeekNext) {
            switch ($this->audioSourceClass) {
                case Playlist::class:
                    $songs = $this->audioSourceId === 0
                        ? Auth::user()->favoriteSongs
                        : Playlist::findOrFail($this->audioSourceId)->songsAllTypes;
                    $this->playNextSourceFromAudioSourcesList($songs, true, true);
                    break;
                case Album::class:
                    $songs = Album::findOrFail($this->audioSourceId)->songs;
                    $this->playNextSourceFromAudioSourcesList($songs, true, true);
                    break;
                case Program::class:
                    $podcasts = Program::findOrFail($this->audioSourceId)
                        ->podcasts()
                        ->with(['media', 'program'])
                        ->hasAudio()
                        ->orderBy('published_at', $this->audioSourceParams['order_by'])
                        ->get();
                    $this->playNextSourceFromAudioSourcesList($podcasts, true, true);
                    break;
                default:
                    throw new Exception('Seeking next audio source is forbidden for '
                        . $this->audioSourceClass . '.');
            }
        }
    }

    public function toggleRepeatMode(): void
    {
        if ($this->canRepeat) {
            $this->repeatMode = ! $this->repeatMode;
        }
    }

    public function render(): View
    {
        return view('livewire.footer.audio-player');
    }
}

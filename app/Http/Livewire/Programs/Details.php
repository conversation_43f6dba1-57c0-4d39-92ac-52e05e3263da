<?php

namespace App\Http\Livewire\Programs;

use App\Models\Audio\Podcast;
use App\Models\Radio\Program;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Browser;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\WithPagination;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Details extends Component
{
    use WithPagination;

    public bool $initialized = false;

    public Program $program;

    public Collection $podcasts;

    public Collection $headerPodcasts;

    public int $totalCount = 0;

    protected $podcastsLinks;

    public int $elementsPerPage = 100;

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public ?string $playedSubAudioSourceClass = null;

    public ?int $playedSubAudioSourceId = null;

    public bool $pauseAllPodcasts = false;

    public array $selectedPodcastIds = [];

    public bool $programIsPlaying = false;

    public Collection $headerBackgroundCoverUrls;

    public ?string $firstCoverPath = null;

    public bool $subscribed = false;

    public array $types = [
        'desc' => 'Du plus récent au plus ancien',
        'asc' => 'Du plus ancien au plus récent',
    ];

    public string $orderBy = 'desc';

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:sub:audio:source:updated' => 'updatePlayedSubAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'user:authenticated' => '$refresh',
        'user:unauthenticated' => '$refresh',
    ];

    public function mount(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        [
            'played_sub_audio_source_class' => $subAudioSourceClass,
            'played_sub_audio_source_id' => $subAudioSourceId,
        ] = app(UserJourneysService::class)->getPlayedSubAudioSource();
        $this->setPlayedSubAudioSource($subAudioSourceClass, $subAudioSourceId);
        $this->setPlayingStatus();
        if (Auth::check()) {
            $this->subscribed = Auth::user()->subscribedPrograms->contains($this->program->id);
        }
        $this->podcasts = collect();
        $this->headerPodcasts = collect();
        $this->headerBackgroundCoverUrls = collect();
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    protected function setPlayedSubAudioSource(
        ?string $subAudioSourceClass,
        ?int $subAudioSourceId,
    ): void {
        $this->playedSubAudioSourceClass = $subAudioSourceClass;
        $this->playedSubAudioSourceId = $subAudioSourceId;
    }

    protected function setPlayingStatus(): void
    {
        $this->programIsPlaying = $this->playedAudioSourceClass === Program::class
            && $this->playedAudioSourceId === $this->program->id
            && ! $this->pauseAllPodcasts;
    }

    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'program_details',
            routeParams: ['program' => $this->program],
            livewireComponent: $this
        );
        $this->initialized = true;
    }

    public function toggleSubscribed(): void
    {
        $this->subscribed = ! $this->subscribed;
        Auth::user()->subscribedPrograms()->toggle($this->program->id);
        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'Votre ' . ($this->subscribed ? 'abonnement' : 'désabonnement') . ' a été pris en compte.',
        ]);
    }

    public function toggleOrderByAsc(): void
    {
        $this->orderBy = $this->orderBy === 'asc' ? 'desc' : 'asc';
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId,
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->setPlayingStatus();
    }

    public function updatePlayedSubAudioSource(
        string $subAudioSourceClass,
        int $subAudioSourceId
    ): void {
        $this->setPlayedSubAudioSource($subAudioSourceClass, $subAudioSourceId);
        $this->pauseAllPodcasts = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllPodcasts = true;
        $this->setPlayingStatus();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllPodcasts = false;
        $this->setPlayingStatus();
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->setPodcasts();
        }

        return view('livewire.programs.details', ['links' => $this->podcastsLinks]);
    }

    public function setPodcasts(): void
    {
        $podcasts = $this->program
            ->podcasts()
            ->with(['media', 'program'])
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->hasAudio()
            ->orderBy('published_at', $this->orderBy)
            ->paginate($this->elementsPerPage);
        $this->headerPodcasts = $this->program
            ->podcasts()
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->hasAudio()
            ->orderBy('published_at', $this->orderBy)
            ->take(5)
            ->get();
        $this->totalCount = $this->program
            ->podcasts()
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->hasAudio()
            ->count();
        $this->podcastsLinks = $podcasts->links();
        $this->podcasts = collect($podcasts->items());
        $this->setCoversPatchworkHeaderBackgroundData();
        $this->updateAudioPodcastsStatuses();
    }

    protected function setCoversPatchworkHeaderBackgroundData(): void
    {
        $headerBackgroundCoverUrlProgram = collect([$this->program->getFirstMediaUrl('cover', 'patchwork')]);
        $headerBackgroundCoverUrlPodcasts = $this->headerPodcasts
            ->take(Browser::isDesktop() ? 4 : 2)
            ->map(fn (Podcast $podcast) => $podcast->getFirstMediaUrl('cover', 'patchwork'));

        $this->headerBackgroundCoverUrls = $headerBackgroundCoverUrlProgram->merge($headerBackgroundCoverUrlPodcasts);

        $this->firstCoverPath = $this->program
            ->getFirstMedia('cover')
            ->getPath(Browser::isDesktop() ? 'large' : 'medium');
    }

    protected function updateAudioPodcastsStatuses(): void
    {
        if ($this->pauseAllPodcasts) {
            $this->podcasts = $this->podcasts->map(function (Podcast $podcast) {
                $podcast->selected = in_array($podcast->id, $this->selectedPodcastIds, true);
                $podcast->playing = false;

                return $podcast;
            });

            return;
        }
        $this->selectedPodcastIds = [];
        $this->podcasts = $this->podcasts->map(function (Podcast $podcast) {
            $podcastIsPlayingInProgramMode = $this->playedAudioSourceClass === Program::class
                && $this->playedAudioSourceId === $this->program->id
                && $this->playedSubAudioSourceClass === Podcast::class
                && $this->playedSubAudioSourceId === $podcast->id;
            $podcastIsPlayingInDirectMode = $this->playedAudioSourceClass === Podcast::class
                && $this->playedAudioSourceId === $podcast->id;
            $isPlaying = $podcastIsPlayingInProgramMode || $podcastIsPlayingInDirectMode;
            if ($isPlaying) {
                $this->selectedPodcastIds[] = $podcast->id;
            }
            $podcast->selected = $isPlaying;
            $podcast->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $podcast;
        });
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }
}

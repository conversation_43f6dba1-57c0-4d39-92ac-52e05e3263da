<?php

namespace App\Http\Livewire\Front\JoinUs;

use App\Http\Requests\Contact\JoinUsPageSendMessageRequest;
use App\Models\Logs\LogJoinUsFormMessage;
use App\Notifications\JoinUsFormMessage;
use App\Traits\WithFilepondUploadsCounter;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Notification;
use Illuminate\View\View;
use Livewire\Component;
use Livewire\WithFileUploads;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Form extends Component
{
    use WithFilepondUploadsCounter;
    use WithFileUploads;

    public const APPLICATION_TYPE_OPTIONS = ['Bénévolat', 'Stage', 'Service Civique', 'Emploi', 'Autre'];

    public const MISSION_OPTIONS = [
        'Animateur/trice - Programmateur/trice',
        'Chroniqueur/euse - Journaliste - Commentateur/trice Sportif',
        'Sound Designer - Producteur/trice Sonore',
        'Technicien.ne Son et/ou Vidéo ',
        'Développeur/euse Web - Administrateur/trice Réseau',
        'Communication - Graphiste -  Photographe',
    ];

    public const STUDIO_OPTIONS = ['Nantes', 'Cholet', 'Saint-Nazaire', 'Angers'];

    public const EXPERIENCE_OPTIONS = ['En radio', 'En vidéo', 'En presse', 'En web'];

    public const MUSIC_TYPE_OPTIONS = [
        'Éclectique', 'Metal', 'Francophone', 'Electro',
        'Classique', 'Jazz', 'Hip hop', 'Disco', 'Funk', 'Indé',
        'Pop', 'Punk', 'Reggae', 'Rock', 'Soul', 'Guilty Pleasure',
        'Blues', 'World Music', 'Chanson française',
    ];

    public const LISTENING_TYPE_OPTIONS = ['Musique', 'Podcast', 'Information', 'Création sonore', 'Magazine'];

    public string $uniqId;

    public string $application_type = 'Bénévolat';

    public string $first_name = '';

    public string $last_name = '';

    public string $city = '';

    public string $birth_year = '';

    public string $email = '';

    public string $phone_number = '';

    public string $mission = '';

    public string $studio = '';

    public string $message = '';

    public array $experiences = [];

    public array $listening_types = [];

    public string $listening_sun = 'non';

    public array $favorite_music_types = [];

    /** @var null|string[] */
    public $attachments = [];

    public function setUniqId()
    {
        $this->uniqId = uniqid('join_us_form_');
    }

    public function setDefaultBirthYear()
    {
        $this->birth_year = Date::now()->subYears(12)->format('Y');
    }

    protected function rules()
    {
        $request = new JoinUsPageSendMessageRequest();

        return $request->rules();
    }

    protected function messages()
    {
        $request = new JoinUsPageSendMessageRequest();

        return $request->messages();
    }

    public function mount()
    {
        $this->setDefaultBirthYear();
        $this->setUniqId();
    }

    public function render(): View
    {
        return view('livewire.front.join-us.form');
    }

    public function submit()
    {
        $validated = $this->validate();

        $validatedExceptUploads = Arr::except($validated, ['attachments']);

        $log = LogJoinUsFormMessage::create(['data' => $validatedExceptUploads]);

        $attachmentsTotalSize = 0;

        foreach (data_get($validated, 'attachments') as $attachment) {
            $attachmentsTotalSize += $attachment->getSize();
            $log->addMedia($attachment)->toMediaCollection('attachments');
        }

        $this->sendNotification($validatedExceptUploads, $log->getMedia('attachments'), $attachmentsTotalSize, false);
        $this->sendNotification($validatedExceptUploads, $log->getMedia('attachments'), $attachmentsTotalSize, true);

        $this->dispatchBrowserEvent('toast:success', ['title' => __('Your message has been sent, we have emailed you a copy.')]);

        $this->reset([
            'application_type',
            'first_name',
            'last_name',
            'city',
            'birth_year',
            'email',
            'phone_number',
            'mission',
            'studio',
            'message',
            'experiences',
            'listening_types',
            'listening_sun',
            'favorite_music_types',
            'attachments',
        ]);

        $this->setDefaultBirthYear();
        $this->setUniqId();

        $this->emit('scroll:top', 'window', 0);
    }

    /** @throws \Exception */
    public function sendNotification(array $validated, MediaCollection $attachments, int $attachmentsTotalSize, bool $isCopyToSender)
    {
        $notification = (new JoinUsFormMessage(
            $validated['application_type'],
            $validated['first_name'],
            $validated['last_name'],
            $validated['city'],
            $validated['birth_year'],
            $validated['email'],
            $validated['phone_number'],
            $validated['mission'],
            $validated['studio'],
            $validated['message'],
            $validated['experiences'],
            $validated['listening_sun'],
            $validated['listening_types'],
            $validated['favorite_music_types'],
            $attachments,
            $attachmentsTotalSize,
            $isCopyToSender
        ))->locale(app()->getLocale());

        Notification::route('mail', $isCopyToSender ? $validated['email'] : '<EMAIL>')
            ->notify($notification);
    }
}

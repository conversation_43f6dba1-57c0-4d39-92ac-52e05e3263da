<?php

namespace App\Http\Livewire\News;

use App\Models\Audio\Thematic;
use App\Models\Map\Place;
use App\Models\News\NewsArticle;
use App\Services\Elasticsearch\CustomSearchService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;
use Livewire\Component;
use Livewire\WithPagination;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Index extends Component
{
    use WithPagination;

    public bool $initialized = false;

    public Collection $thematics;

    public Collection $highlightedArticles;

    public Collection $articles;

    public ?Place $place = null;

    public string $placeId = '';

    public ?Thematic $selectedThematic = null;

    public ?int $selectedRadioStationId = null;

    public string $search = '';

    public ?string $customTitle = null;

    public int $elementsPerPage = 21;

    public array $selectedNewsArticleIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllNewsArticles = false;

    protected $listeners = [
        'radio:station:universe:updated' => 'setSelectedRadioStationId',
        'thematic:selected' => 'thematicSelected',
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    public function queryStringWithPagination()
    {
        return [];
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function mount(): void
    {
        $this->setPage($this->page);
        $this->articles = collect();
        $this->highlightedArticles = collect();
        if ($this->placeId) {
            $this->place = Place::find($this->placeId);
        }
    }

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'news', livewireComponent: $this);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStationId($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->thematics = thematics();
        $this->initialized = true;
    }

    public function setSelectedRadioStationId(int $selectedRadioStationId): void
    {
        $this->resetPage();
        $this->selectedRadioStationId = $selectedRadioStationId;
    }

    public function render(): View
    {
        $links = null;
        $this->customTitle = null;
        if ($this->initialized) {
            $this->setHighlightedArticles();
        }
        if ($this->search) {
            if (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_AUTHOR)) {
                $this->customTitle = 'Tous les articles de "' . Str::replaceFirst(CustomSearchService::FILTER_AUTHOR, '', $this->search) . '"';
            } elseif (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_TAGS)) {
                $this->customTitle = 'Tous les articles du tag "#' . Str::replaceFirst(CustomSearchService::FILTER_TAGS, '', $this->search) . '"';
            } elseif (Str::startsWith($this->search, app(CustomSearchService::class)::FILTER_DATE)) {
                $date = Date::createFromFormat('Y-m-d', Str::replaceFirst(CustomSearchService::FILTER_DATE, '', $this->search));
                $this->customTitle = 'Tous les articles du ' . $date->timezone('Europe/Paris')->isoFormat('D MMMM Y');
            }
            $esNews = app(CustomSearchService::class)->searchNews(
                $this->search,
                $this->elementsPerPage,
                $this->elementsPerPage * ($this->page - 1)
            )['hits'];
            $news = array_map(static function ($esNews) {
                return NewsArticle::find($esNews['_source']['id']);
            }, $esNews['hits']);
            $newsResult = (new LengthAwarePaginator(
                $news,
                $esNews['total']['value'],
                $this->elementsPerPage,
                $this->page
            ));

            $links = $newsResult->links();
            $this->articles = collect($newsResult->items());
        } elseif ($this->place) {
            $newsResult = $this->getPlaceArticlesQuery()
                ->paginate($this->elementsPerPage);
            $links = $newsResult->links();
            $this->articles = collect($newsResult->items());
        } elseif ($this->initialized) {
            $newsResult = $this->getArticlesQuery()
                ->whereNotIn('id', $this->highlightedArticles->pluck('id'))
                ->paginate($this->elementsPerPage);
            $links = $newsResult->links();
            $this->articles = collect($newsResult->items());
        }

        $this->updateAudioNewsArticlesStatuses();

        return view('livewire.news.index', [
            'links' => $links,
        ]);
    }

    public function setHighlightedArticles(): void
    {
        $this->highlightedArticles = $this->getArticlesQuery()->limit(2)->get();
    }

    protected function getArticlesQuery(): Builder
    {
        return NewsArticle::with(['media', 'radioStations'])
            ->when(
                $this->selectedThematic,
                fn (Builder $articleQuery) => $articleQuery->where('thematic_id', $this->selectedThematic->id)
            )
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereRelation(
                    'radioStations',
                    fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStationId)
                )->orWhereDoesntHave('radioStations');
            })
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->orderBy('published_at', 'DESC');
    }

    protected function getPlaceArticlesQuery(): Builder
    {
        $placeId = $this->place->id;

        return NewsArticle::with(['media', 'radioStations'])
            ->when(
                $this->selectedThematic,
                fn (Builder $articleQuery) => $articleQuery->where('thematic_id', $this->selectedThematic->id)
            )
            ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->orderBy('published_at', 'DESC');
    }

    public function thematicSelected(?int $thematicId): void
    {
        if ($this->selectedThematic?->id === $thematicId) {
            $this->selectedThematic = null;
        } else {
            $this->selectedThematic = $this->thematics->where('id', $thematicId)->first();
        }
        $this->resetPage();
    }

    protected function updateAudioNewsArticlesStatuses(): void
    {
        if ($this->pauseAllNewsArticles) {
            $this->articles = $this->articles->map(function (NewsArticle $article) {
                $article->selected = in_array($article->id, $this->selectedNewsArticleIds, true);
                $article->playing = false;

                return $article;
            });
            $this->highlightedArticles = $this->highlightedArticles->map(function (NewsArticle $article) {
                $article->selected = in_array($article->id, $this->selectedNewsArticleIds, true);
                $article->playing = false;

                return $article;
            });

            return;
        }
        $this->selectedNewsArticleIds = [];
        $this->articles = $this->articles->map(function (NewsArticle $article) {
            $isPlaying = $article::class === $this->playedAudioSourceClass
                && $article->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedNewsArticleIds[] = $article->id;
            }
            $article->selected = $isPlaying;
            $article->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $article;
        });
        $this->highlightedArticles = $this->highlightedArticles->map(function (NewsArticle $article) {
            $isPlaying = $article::class === $this->playedAudioSourceClass
                && $article->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedNewsArticleIds[] = $article->id;
            }
            $article->selected = $isPlaying;
            $article->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $article;
        });
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllNewsArticles = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllNewsArticles = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllNewsArticles = false;
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }
}

<?php

namespace App\Http\Livewire\News;

use App\Models\News\NewsArticle;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Rail extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = true;

    public ?string $title = null;

    public string $railName;

    public Collection $newsArticles;

    public ?string $showAllRoute = null;

    public array $showAllParams = [];

    public array $selectedNewsArticleIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllNewsArticles = false;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function init(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->updateAudioNewsArticlesStatuses();
        $this->initialized = true;
    }

    public function render(): View
    {
        return view('livewire.news.rail');
    }

    protected function updateAudioNewsArticlesStatuses(): void
    {
        if ($this->pauseAllNewsArticles) {
            $this->newsArticles = $this->newsArticles->map(function (NewsArticle $newsArticle) {
                $newsArticle->selected = in_array($newsArticle->id, $this->selectedNewsArticleIds, true);
                $newsArticle->playing = false;

                return $newsArticle;
            });

            return;
        }
        $this->selectedNewsArticleIds = [];
        $this->newsArticles = $this->newsArticles->map(function (NewsArticle $newsArticle) {
            $isPlaying = $newsArticle::class === $this->playedAudioSourceClass
                && $newsArticle->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedNewsArticleIds[] = $newsArticle->id;
            }
            $newsArticle->selected = $isPlaying;
            $newsArticle->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $newsArticle;
        });
        $this->emit('rail:load', ['railName' => $this->railName]);
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllNewsArticles = false;
        $this->updateAudioNewsArticlesStatuses();
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllNewsArticles = true;
        $this->updateAudioNewsArticlesStatuses();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllNewsArticles = false;
        $this->updateAudioNewsArticlesStatuses();
    }
}

<?php

namespace App\Http\Livewire\Nav;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class Profile extends Component
{
    public bool $active = false;

    protected $listeners = [
        'user:authenticated' => '$refresh',
        'user:unauthenticated' => '$refresh',
        'nav:route:loaded' => 'routeLoaded',
    ];

    public function routeLoaded(string $routeKey): void
    {
        $this->active = in_array($routeKey, ['profile_information', 'profile_params'], true);
    }

    public function render(): View
    {
        return view('livewire.nav.profile');
    }
}

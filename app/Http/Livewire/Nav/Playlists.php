<?php

namespace App\Http\Livewire\Nav;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Playlists extends Component
{
    public bool $initialized = false;

    public Collection $playlists;

    public ?string $selectedTabKey = null;

    public ?int $selectedPlaylistId = null;

    protected $listeners = [
        'user:authenticated' => '$refresh',
        'user:unauthenticated' => '$refresh',
        'nav:route:loaded' => 'routeLoaded',
        'playlist:created' => '$refresh',
    ];

    public function init(): void
    {
        $this->initialized = true;
    }

    public function routeLoaded(string $routeKey, array $params = []): void
    {
        $this->selectedTabKey = $routeKey;
        $this->selectedPlaylistId = data_get($params, 'bindings.playlist.id');
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->playlists = Auth::check() ? Auth::user()->playlists()->get() : collect();
        }

        return view('livewire.nav.playlists');
    }
}

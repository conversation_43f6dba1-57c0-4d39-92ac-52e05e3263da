<?php

namespace App\Http\Livewire\Nav;

use App\Models\Map\Place;
use App\Models\Radio\RadioStation;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class RadioStationUniverseSelector extends Component
{
    public ?RadioStation $selectedRadioStation = null;

    public ?Collection $radioStations = null;

    protected $listeners = [
        'player:audio:source:updated' => 'updateRadioStationUniverseFromEvent',
        'profil:params:updated' => 'setUpdatedSelectedRadioStation',
        'geolocation:position:found:refresh' => 'browserLocationFoundRefresh',
    ];

    /** @throws \Exception */
    public function init(): void
    {
        $this->radioStations = radioStations();
    }

    public function setUpdatedSelectedRadioStation(): void
    {
        if (! Auth::user()?->settings->use_geolocation) {
            $defaultStreamedRadioStationId = app(UserJourneysService::class)->getSelectedDefaultSettingRadioStationId() ?? radioStations()->first();
            $this->updateRadioStationUniverseFromSettingUpdate($defaultStreamedRadioStationId);
        } else {
            $this->emit('geolocation:position:request', 'refresh');
        }
    }

    public function updateRadioStationUniverseFromEvent(string $audioSourceClass, int $audioSourceId): void
    {
        if ($audioSourceClass !== RadioStation::class) {
            return;
        }
        $this->setRadioStationUniverse($audioSourceId);
    }

    protected function setRadioStationUniverse(int $radioStationId): void
    {
        $previousSelectedRadioStation = $this->selectedRadioStation;
        $this->selectedRadioStation = $this->radioStations->where('id', $radioStationId)->sole();
        if ($this->selectedRadioStation?->id !== $previousSelectedRadioStation?->id && $previousSelectedRadioStation !== null) {
            $this->emit('radio:station:universe:updated', $this->selectedRadioStation->id);
        }
        app(UserJourneysService::class)->setSelectedRadioStationUniverse($this->selectedRadioStation);
    }

    public function updateRadioStationUniverseFromDropDown(int $radioStationId): void
    {
        $playerIsPlaying = app(UserJourneysService::class)->getPlayerPlayingStatus();
        if ($playerIsPlaying) {
            $this->setRadioStationUniverse($radioStationId);

            return;
        }
        $this->emitTo(
            'footer.audio-player',
            'player:audio:source:update',
            RadioStation::class,
            $radioStationId,
            false
        );
    }

    public function updateRadioStationUniverseFromSettingUpdate(int $radioStationId): void
    {
        $playerIsPlaying = app(UserJourneysService::class)->getPlayerPlayingStatus();
        if ($playerIsPlaying) {
            $this->setRadioStationUniverse($radioStationId);
            $this->emit('rail:radio:stations:refresh');

            return;
        }
        $this->emitTo(
            'footer.audio-player',
            'player:audio:source:update',
            RadioStation::class,
            $radioStationId,
            false,
            [],
            null,
            true
        );
    }

    /** @throws \Exception */
    public function browserLocationFoundRefresh(?float $browserLatitude = null, ?float $browserLongitude = null): void
    {
        // Location is found without coordinates
        if (! $browserLatitude || ! $browserLongitude) {
            app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId(null);
            $defaultStreamedRadioStation = radioStations()->first();
            $this->updateRadioStationUniverseFromSettingUpdate($defaultStreamedRadioStation->id);

            return;
        }
        $closestRadioStation = $this->getClosestRadioStationFromUser($browserLatitude, $browserLongitude);
        app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId($closestRadioStation->id);
        $this->updateRadioStationUniverseFromSettingUpdate($closestRadioStation->id);
    }

    protected function getClosestRadioStationFromUser(float $browserLatitude, float $browserLongitude): RadioStation
    {
        $locatableRadioStations = radioStations()
            ->load([
                'contentLocations' => function ($query): void {
                    $query->where('location_type', Place::class);
                    $query->with(['location' => function (MorphTo $morphTo) {
                        $morphTo->morphWith([
                            Place::class => ['currentPoint'],
                        ]);
                    }]);
                },
            ])
            ->filter(fn (RadioStation $station): bool => (bool) $station->point());

        foreach ($locatableRadioStations as $locatableRadioStation) {
            $position = $locatableRadioStation->point();

            $distanceBetweenBrowserAndRadio = $this->getDistanceBetweenTwoCoords(
                $browserLatitude,
                $browserLongitude,
                $position->getLatitude(),
                $position->getLongitude()
            );

            // @phpstan-ignore-next-line
            $locatableRadioStation->distance = $distanceBetweenBrowserAndRadio;
        }

        $closestRadioStation = $locatableRadioStations->where('distance', $locatableRadioStations->min('distance'))->first();

        // @phpstan-ignore-next-line
        return $closestRadioStation->distance <= 70 ? $closestRadioStation : $locatableRadioStations->first();
    }

    protected function getDistanceBetweenTwoCoords(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $radianInKm = 6371; // km
        $distanceLat = $this->toRad($lat2 - $lat1);
        $distanceLon = $this->toRad($lon2 - $lon1);
        $lat1 = $this->toRad($lat1);
        $lat2 = $this->toRad($lat2);
        $buffer = sin($distanceLat / 2)
            * sin($distanceLat / 2)
            + sin($distanceLon / 2)
            * sin($distanceLon / 2)
            * cos($lat1) * cos($lat2);
        $toConvert = 2 * atan2(sqrt($buffer), sqrt(1 - $buffer));

        return $radianInKm * $toConvert;
    }

    protected function toRad(float $value): float
    {
        return $value * M_PI / 180;
    }

    public function render(): View
    {
        return view('livewire.nav.radio-station-universe-selector');
    }
}

<?php

namespace App\Http\Livewire\Nav;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class Search extends Component
{
    public bool $active = false;

    protected $listeners = ['nav:route:loaded' => 'routeLoaded'];

    public function navToSearchResults(): void
    {
        $this->active = true;
        $this->emitTo('router', 'nav:to', 'search_results');
    }

    public function routeLoaded(string $routeKey): void
    {
        $this->active = $routeKey === 'search_results';
    }

    public function render(): View
    {
        return view('livewire.nav.search');
    }
}

<?php

namespace App\Http\Livewire\Nav;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class Tabs extends Component
{
    public array $navTabs = [
        [
            'key' => 'browse',
            'icon' => 'headphone',
            'label' => 'Parcourir',
            'desktop' => true,
            'mobile' => true,
            'active_with_route_keys' => ['browse'],
        ],
        [
            'key' => 'map',
            'icon' => 'compass',
            'label' => 'Se balader',
            'desktop' => true,
            'mobile' => true,
            'active_with_route_keys' => ['map'],
        ],
        [
            'key' => 'podcasts',
            'icon' => 'mic-light',
            'label' => 'Podcasts',
            'desktop' => true,
            'mobile' => true,
            'active_with_route_keys' => ['podcasts', 'program_details', 'podcast_details'],
        ],
        [
            'key' => 'playlists',
            'icon' => 'library',
            'label' => 'Playlists',
            'desktop' => true,
            'mobile' => true,
            'active_with_route_keys' => ['playlists', 'playlist_details', 'favorite_songs'],
        ],
        [
            'key' => 'events',
            'icon' => 'calendar',
            'label' => 'Agenda',
            'desktop' => true,
            'mobile' => false,
            'active_with_route_keys' => ['events', 'event_details'],
        ],
        [
            'key' => 'programs',
            'icon' => 'clock',
            'label' => 'Grille d\'antenne',
            'desktop' => true,
            'mobile' => false,
            'active_with_route_keys' => ['programs'],
        ],
    ];

    public ?string $currentRouteKey = null;

    protected $listeners = ['nav:route:loaded' => 'routeLoaded'];

    public function navToTab(string $routeKey): void
    {
        $this->currentRouteKey = $routeKey;
        $this->emitTo('router', 'nav:to', $routeKey);
    }

    public function routeLoaded(string $routeKey): void
    {
        $this->currentRouteKey = $routeKey;
    }

    public function render(): View
    {
        return view('livewire.nav.tabs');
    }
}

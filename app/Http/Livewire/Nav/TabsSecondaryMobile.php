<?php

namespace App\Http\Livewire\Nav;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class TabsSecondaryMobile extends Component
{
    public array $navTabs;

    public ?string $selectedTabKey = null;

    protected $listeners = ['nav:route:loaded' => 'routeLoaded'];

    public function mount()
    {
        $this->navTabs[] = [
            'key' => 'browse',
            'route' => route('app.browse.index'),
            'icon' => 'headphone',
            'label' => 'Parcourir',
            'spa' => true,
        ];
        $this->navTabs[] = [
            'key' => 'map',
            'route' => route('app.map.index'),
            'icon' => 'compass',
            'label' => 'Se balader',
            'spa' => true,
        ];
        $this->navTabs[] = [
            'key' => 'podcasts',
            'route' => route('app.podcasts.index'),
            'icon' => 'mic-light',
            'label' => 'Podcasts',
            'spa' => true,
        ];
        $this->navTabs[] = [
            'key' => 'playlists',
            'route' => route('app.playlists.index'),
            'icon' => 'library',
            'label' => 'Playlists',
            'spa' => true,
        ];
        $this->navTabs[] = [
            'key' => 'events',
            'route' => route('app.events.index'),
            'icon' => 'calendar',
            'label' => 'Agenda',
            'spa' => true,
        ];
        $this->navTabs[] = [
            'key' => 'programs',
            'route' => route('app.programs.index'),
            'icon' => 'clock',
            'label' => 'Grille d\'antenne',
            'spa' => true,
        ];
    }

    public function navToTab(string $routeKey): void
    {
        $this->selectedTabKey = $routeKey;
        $this->emitTo('router', 'nav:to', $routeKey);
    }

    public function routeLoaded(string $routeKey): void
    {
        $this->selectedTabKey = $routeKey;
    }

    public function render(): View
    {
        return view('livewire.nav.tabs-secondary-mobile');
    }
}

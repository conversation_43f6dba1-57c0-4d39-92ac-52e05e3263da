<?php

namespace App\Http\Livewire\Browse;

use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Playlists extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = false;

    public Collection $playlists;

    public int $selectedRadioStationId;

    protected $listeners = ['radio:station:universe:updated' => 'setSelectedRadioStation'];

    public function mount(): void
    {
        $this->playlists = collect();
    }

    public function init(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        $this->initialized = true;
    }

    public function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $this->selectedRadioStationId = $selectedRadioStationId;
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->playlists = browsePlaylistsByStation($this->selectedRadioStationId);
            $this->contentLoaded = true;
        }

        return view('livewire.browse.playlists');
    }
}

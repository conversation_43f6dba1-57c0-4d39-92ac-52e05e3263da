<?php

namespace App\Http\Livewire\Browse;

use App\Models\Radio\RadioStation;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Songs extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = false;

    public Collection $songs;

    public RadioStation $selectedRadioStation;

    protected $listeners = [
        'radio:station:universe:updated' => 'setSelectedRadioStation',
    ];

    public function mount(): void
    {
        $this->songs = collect();
    }

    public function init(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        $this->initialized = true;
    }

    public function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $selectedRadioStationId)->firstOrFail();
    }

    /** @throws \Exception */
    public function render(): View
    {
        if ($this->initialized) {
            $this->songs = browseSongsByStation($this->selectedRadioStation->winmedia_id);
            $this->contentLoaded = true;
        }

        return view('livewire.browse.songs');
    }
}

<?php

namespace App\Http\Livewire\Browse;

use App\Services\Seo\SeoMetaService;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class Index extends Component
{
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'browse', livewireComponent: $this);
    }

    public function render(): View
    {
        return view('livewire.browse.index');
    }
}

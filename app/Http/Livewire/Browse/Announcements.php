<?php

namespace App\Http\Livewire\Browse;

use App\Models\Announcements\Announcement;
use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\Map\Point;
use App\Models\News\NewsArticle;
use App\Models\Radio\Program;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Intervention\Image\Facades\Image;
use Livewire\Component;

class Announcements extends Component
{
    public bool $initialized = false;

    public int $selectedRadioStationId;

    public Collection $announcements;

    public array $selectedAnnouncementIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllAnnouncements = false;

    protected $listeners = [
        'radio:station:universe:updated' => 'updateSelectedRadioStation',
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function mount(): void
    {
        $this->announcements = collect();
    }

    public function init(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->initialized = true;
        $this->emit('rail:load', ['railName' => 'browse-announcements']);
    }

    protected function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $this->selectedRadioStationId = $selectedRadioStationId;
    }

    protected function setAnnouncements(): void
    {
        $this->announcements = browseAnnouncementsByStation($this->selectedRadioStationId)
            ->map(function (Announcement $announcement) {
                // Colors
                $backgroundRgbColor = $this->getCardBackgroundColor($announcement);
                $textColor = $this->getTextColorAccordingToBackgroundColor(
                    $backgroundRgbColor[0],
                    $backgroundRgbColor[1],
                    $backgroundRgbColor[2],
                    $backgroundRgbColor[3],
                );
                $announcement->backgroundRgbColor = implode(',', $backgroundRgbColor);
                $announcement->textColor = $textColor;
                // Route
                $announcement->routeKey = match ($announcement->announceable::class) {
                    NewsArticle::class => 'news_details',
                    Event::class => 'event_details',
                    Program::class => 'program_details',
                    Podcast::class => 'podcast_details',
                    Playlist::class => 'playlist_details',
                    default => null,
                };
                $announcement->href = match ($announcement->announceable::class) {
                    NewsArticle::class => route('app.news.show', $announcement->announceable),
                    Event::class => route('app.event.show', $announcement->announceable),
                    Program::class => route('app.program.show', $announcement->announceable),
                    Podcast::class => route('app.podcast.show', $announcement->announceable),
                    Playlist::class => route('app.playlist.show', $announcement->announceable),
                    Point::class => route('app.map.index'),
                    default => null,
                };
                $routeBindingKey = match ($announcement->announceable::class) {
                    NewsArticle::class => 'article',
                    Event::class => 'event',
                    Program::class => 'program',
                    Podcast::class => 'podcast',
                    Playlist::class => 'playlist',
                    default => null,
                };
                $announcement->routeParams = [
                    'bindings' => [
                        $routeBindingKey => [
                            'model' => $announcement->announceable::class,
                            'id' => $announcement->announceable->id,
                        ],
                    ],
                ];

                return $announcement;
            });
    }

    protected function getCardBackgroundColor(Announcement $announcement): array
    {
        // Return default SUN yellow #FFCD00
        if (! $announcement->displayable_media) {
            return [255, 205, 0, 1.0];
        }
        $illustrationPath = $announcement->displayable_media->getPath('announcement');
        if (File::exists($illustrationPath)) {
            $image = Image::make($illustrationPath);

            return $image->resize(1, 1)->limitColors(1)->pickColor(0, 0);
        } else {
            return [255, 205, 0, 1.0];
        }
    }

    protected function getTextColorAccordingToBackgroundColor($red, $green, $blue, $alpha): string
    {
        $brightness = $red * 0.299 + $green * 0.587 + $blue * 0.114 + (1 - $alpha) * 255;

        return $brightness > 186 ? '#1c1b1b' : '#ffffff';
    }

    public function updateSelectedRadioStation(int $selectedRadioStationId): void
    {
        $this->setSelectedRadioStation($selectedRadioStationId);
        $this->setAnnouncements();
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->setAnnouncements();
            $this->updateAudioAnnouncementsStatuses();
        }

        return view('livewire.browse.announcements');
    }

    protected function updateAudioAnnouncementsStatuses(): void
    {
        if ($this->pauseAllAnnouncements) {
            $this->announcements = $this->announcements->map(function (Announcement $announcement) {
                $announcement->selected = in_array($announcement->id, $this->selectedAnnouncementIds, true);
                $announcement->playing = false;

                return $announcement;
            });

            return;
        }
        $this->selectedAnnouncementIds = [];
        $this->announcements = $this->announcements->map(function (Announcement $announcement) {
            $isPlaying = $announcement->announceable_type === $this->playedAudioSourceClass
                && $announcement->announceable_id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedAnnouncementIds[] = $announcement->id;
            }
            $announcement->selected = $isPlaying;
            $announcement->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $announcement;
        });
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllAnnouncements = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllAnnouncements = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllAnnouncements = false;
    }
}

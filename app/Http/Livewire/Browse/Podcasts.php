<?php

namespace App\Http\Livewire\Browse;

use App\Models\Radio\RadioStation;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Podcasts extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = false;

    public Collection $podcasts;

    public int $selectedRadioStationId;

    protected $listeners = ['radio:station:universe:updated' => 'setSelectedRadioStation'];

    public function mount(): void
    {
        $this->podcasts = collect();
    }

    public function init(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        $this->initialized = true;
    }

    public function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $radioStation = RadioStation::findOrFail($selectedRadioStationId);
        $this->selectedRadioStationId = $radioStation->id;
    }

    /** @throws \Exception */
    public function render(): View
    {
        if ($this->initialized) {
            $this->podcasts = browsePodcastsByStation($this->selectedRadioStationId);
            $this->contentLoaded = true;
        }

        return view('livewire.browse.podcasts');
    }
}

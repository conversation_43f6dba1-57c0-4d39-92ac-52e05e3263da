<?php

namespace App\Http\Livewire\Browse;

use App\Models\Radio\RadioStation;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Events extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = false;

    public Collection $events;

    public RadioStation $radioStation;

    protected $listeners = ['radio:station:universe:updated' => 'setSelectedRadioStation'];

    public function mount(): void
    {
        $this->events = collect();
    }

    /** @throws \Exception */
    public function init(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        $this->initialized = true;
        $this->emit('rail:load', ['railName' => 'browse-events']);
    }

    public function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $radioStation = RadioStation::findOrFail($selectedRadioStationId);
        $this->radioStation = $radioStation;
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->events = browseEventsByStation($this->radioStation->id);
            $this->contentLoaded = true;
        }

        return view('livewire.browse.events');
    }
}

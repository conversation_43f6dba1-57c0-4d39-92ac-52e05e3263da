<?php

namespace App\Http\Livewire\Browse;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ToInspireYou extends Component
{
    public function navToDedicace()
    {
        if (! Auth::check()) {
            $this->emit('modal:show', 'login', ['destinationRouteKey' => 'dedicace']);
        } else {
            $this->emitTo('router', 'nav:to', 'dedicace');
        }
    }

    public function render(): View
    {
        return view('livewire.browse.to-inspire-you');
    }

    public function init(): void
    {
        $this->emit('rail:load', ['railName' => 'to-inspire-you-rail']);
    }
}

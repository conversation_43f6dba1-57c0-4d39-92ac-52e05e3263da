<?php

namespace App\Http\Livewire\Browse;

use App\Models\Radio\RadioStation;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class NewsArticles extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = false;

    public Collection $newsArticles;

    public int $selectedRadioStationId;

    public string $selectedRadioStationName;

    protected $listeners = ['radio:station:universe:updated' => 'setSelectedRadioStation'];

    public function mount(): void
    {
        $this->newsArticles = collect();
    }

    /** @throws \Exception */
    public function init(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        $this->initialized = true;
        $this->emit('rail:load', ['railName' => 'news-articles-browse']);
    }

    public function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $radioStation = RadioStation::findOrFail($selectedRadioStationId);
        $this->selectedRadioStationId = $radioStation->id;
        $this->selectedRadioStationName = $radioStation->name;
    }

    /** @throws \Exception */
    public function render(): View
    {
        if ($this->initialized) {
            $this->newsArticles = browseNewsArticlesByStation($this->selectedRadioStationId);
            $this->contentLoaded = true;
        }

        return view('livewire.browse.news-articles');
    }
}

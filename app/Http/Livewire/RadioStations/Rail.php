<?php

namespace App\Http\Livewire\RadioStations;

use App\Models\Radio\RadioStation;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class Rail extends Component
{
    public bool $initialized = false;

    public ?int $highlightedRadioStationId = null;

    public array $highlightedRadioStationBroadcast = [];

    public ?int $playingRadioStationId = null;

    public array $radioStationBroadcasts;

    public bool $isDark = true;

    public bool $isMain = true;

    public bool $pauseAllRadioStations = false;

    public ?int $initialSelectedRadioStationId = null;

    public ?int $initialSelectedWebradioId = null;

    protected $listeners = [
        'radio:station:universe:updated' => 'updateHighlightedRadioStationId',
        'player:audio:source:updated' => 'updateAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'radio:broadcast:updated' => 'setRadioStationBroadcasts',
        'rail:radio:stations:refresh' => 'init',
    ];

    /** @throws \Exception */
    public function init(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayingRadioStationId($audioSourceClass, $audioSourceId);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->initialSelectedRadioStationId = app(UserJourneysService::class)->getSelectedDefaultSettingRadioStationId();
        $this->initialSelectedWebradioId = app(UserJourneysService::class)->getSelectedDefaultSettingWebradioId();
        $this->setHighlightedRadioStationId($selectedRadioStationId);
        $this->initialized = true;
    }

    protected function setPlayingRadioStationId(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playingRadioStationId = $audioSourceClass === RadioStation::class ? $audioSourceId : null;
    }

    /** @throws \Exception */
    public function setHighlightedRadioStationId(int $radioStationId): void
    {
        $this->highlightedRadioStationId = $radioStationId;
        $this->setRadioStationBroadcasts();
    }

    /** @throws \Exception */
    public function setRadioStationBroadcasts(): void
    {
        $radioStationBroadcasts = radioStations()
            ->load(['contentLocations'])
            ->map(function (RadioStation $radioStation) {
                $liveBroadcasting = lastRadioStationsLiveBroadcasts()->where(
                    'winmedia_radio_station_id',
                    $radioStation->winmedia_id
                )->first();
                $song = $liveBroadcasting?->song;
                $podcast = $liveBroadcasting?->podcast;

                $currentCover = null;
                $currentTitle = null;
                $currentPerformer = null;
                if ($song) {
                    $currentCover = $song->cover_thumb;
                    $currentTitle = $song->title;
                    $currentPerformer = $song->performerRelationship?->name ?: $song->performer;
                } elseif ($podcast) {
                    $currentCover = $podcast->getFirstMediaUrl('cover', 'card');
                    $currentTitle = $podcast->title;
                    $currentPerformer = '🔴 En direct';
                }

                return [
                    'radio_station_id' => $radioStation->id,
                    'radio_station_name' => $radioStation->name,
                    'radio_station_color' => $radioStation->color,
                    'radio_station_localized' => $radioStation->point() !== null,
                    'current_song_cover' => $currentCover ?: $radioStation->getFirstMediaUrl('cover', 'card'),
                    'current_song_title' => $currentTitle ?: '🔴 En direct',
                    'current_song_performer' => $currentPerformer ?: $radioStation->label,
                    'current_song_label' => $song ? ($song->labelRelationship?->name ?: $song->publisher) : null,
                    'current_song_year' => $song?->yearFourDigit,
                    'song_performer_id' => $song ? $song->performerRelationship?->id : null,
                    'song_album_id' => $song ? $song->albumRelationship?->id : null,
                    'song_place_label_id' => $song ? $song->labelRelationship?->place()?->id : null,
                    'program_id' => $podcast?->program_id,
                    'current_song_real_duration' => $liveBroadcasting?->real_duration,
                    'current_song_started_at' => $liveBroadcasting?->started_at->toW3CString(),
                    'is_playing' => $radioStation->id === $this->playingRadioStationId
                        && ! $this->pauseAllRadioStations
                        && app(UserJourneysService::class)->getPlayerPlayingStatus(),
                    'dedication_user_cover_thumb' => $liveBroadcasting?->dedicationUser?->getFirstMediaUrl(
                        'profile_picture',
                        'dedication_thumb_front_card'
                    ),
                    'dedication_user_name' => $liveBroadcasting?->dedicationUser?->username,
                ];
            });

        if ($this->initialSelectedRadioStationId !== null) {
            $firstPart = $radioStationBroadcasts
                ->where('radio_station_id', '=', $this->initialSelectedRadioStationId)
                ->values();
            $secondPart = $radioStationBroadcasts
                ->where('radio_station_localized', '=', false)
                ->where('radio_station_id', '=', $this->initialSelectedWebradioId)
                ->values();
            $thirdPart = $radioStationBroadcasts
                ->where('radio_station_localized', '=', false)
                ->where('radio_station_id', '!=', $this->initialSelectedWebradioId)
                ->values();
            $fourthPart = $radioStationBroadcasts
                ->where('radio_station_localized', '=', true)
                ->where('radio_station_id', '!=', $this->initialSelectedRadioStationId)
                ->values();

            $radioStationBroadcastsOrdered = collect($firstPart)->merge($secondPart)->merge($thirdPart)->merge($fourthPart);
        } else {
            $radioStationBroadcastsOrdered = $radioStationBroadcasts;
        }

        $highlightedRadioStationBroadcast = $radioStationBroadcastsOrdered
            ->where('radio_station_id', $this->highlightedRadioStationId)
            ->first();
        if (! $highlightedRadioStationBroadcast) {
            $this->highlightedRadioStationBroadcast = $radioStationBroadcastsOrdered->first();
        } else {
            $this->highlightedRadioStationBroadcast = $highlightedRadioStationBroadcast;
        }

        $this->radioStationBroadcasts = $radioStationBroadcastsOrdered
            ->where('radio_station_id', '!=', $this->highlightedRadioStationId)
            ->values()
            ->toArray();
    }

    /** @throws \Exception */
    public function updateHighlightedRadioStationId(int $radioStationId): void
    {
        $this->setHighlightedRadioStationId($radioStationId);
    }

    /** @throws \Exception */
    public function updateAudioSource(string $audioSourceClass, int $audioSourceId): void
    {
        $this->setPlayingRadioStationId($audioSourceClass, $audioSourceId);
        $this->setRadioStationBroadcasts();
    }

    /** @throws \Exception */
    public function playerHasPaused(): void
    {
        ['played_audio_source_class' => $audioSourceClass] = app(UserJourneysService::class)->getPlayedAudioSource();
        if ($audioSourceClass === RadioStation::class) {
            $this->pauseAllRadioStations = true;
            $this->setRadioStationBroadcasts();
        }
    }

    /** @throws \Exception */
    public function playerIsResuming(): void
    {
        ['played_audio_source_class' => $audioSourceClass] = app(UserJourneysService::class)->getPlayedAudioSource();
        if ($audioSourceClass === RadioStation::class) {
            $this->pauseAllRadioStations = false;
            $this->setRadioStationBroadcasts();
        }
    }

    public function render(): View
    {
        if ($this->initialized) {
            $this->emit('rail:load', ['railName' => 'browse-radio-stations']);
            $this->updateHighlightedRadioProgressBar();
        }

        return view('livewire.radio-stations.rail');
    }

    protected function updateHighlightedRadioProgressBar(): void
    {
        if (! $this->highlightedRadioStationBroadcast) {
            return;
        }
        $this->emit(
            'player:radio_station_rail:audio:progress:init',
            '#radio-progress-bar',
            '#radio-progression-timer',
            $this->highlightedRadioStationBroadcast['current_song_started_at'],
            $this->highlightedRadioStationBroadcast['current_song_real_duration'],
        );
    }
}

<?php

namespace App\Http\Livewire\Profile;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class Password extends Component
{
    public bool $showForm = false;

    public ?string $current_password = null;

    public ?string $new_password = null;

    public ?string $new_password_confirmation = null;

    public bool $currentPasswordVisible = false;

    public bool $newPasswordVisible = false;

    public bool $newPasswordConfirmationVisible = false;

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function submit(): void
    {
        $validated = $this->validate();
        Auth::user()->update(['password' => Hash::make($validated['new_password'])]);
        $this->dispatchBrowserEvent('toast:success', ['title' => 'Votre mot de passe a été mis à jour.']);
        $this->reset();
    }

    public function render(): View
    {
        return view('livewire.profile.password');
    }

    public function rules(): array
    {
        return [
            'current_password' => [
                'required',
                function ($attr, $value, $fail) {
                    if (! Hash::check($value, Auth::user()->password)) {
                        $fail('Le mot de passe actuel est incorrect.');
                    }
                },
            ],
            'new_password' => ['required', \Illuminate\Validation\Rules\Password::defaults()],
            'new_password_confirmation' => [
                'required',
                function ($attr, $value, $fail) {
                    if ($value !== $this->new_password) {
                        $fail('La confirmation du nouveau mot de passe est incorrecte.');
                    }
                },
            ],
        ];
    }
}

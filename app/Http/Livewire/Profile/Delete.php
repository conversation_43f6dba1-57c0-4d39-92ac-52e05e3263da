<?php

namespace App\Http\Livewire\Profile;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Livewire\Component;

class Delete extends Component
{
    public bool $showForm = false;

    public ?string $current_password = null;

    public bool $currentPasswordVisible = false;

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function submit(): void
    {
        $this->validate();
        $user = Auth::user();
        Auth::logout();
        $user->delete();
        $this->dispatchBrowserEvent('toast:success', ['title' => 'Votre compte a bien été supprimé.']);
        $this->emit('user:unauthenticated');
        $this->emitTo('router', 'nav:to', 'browse');
    }

    public function render(): View
    {
        return view('livewire.profile.delete');
    }

    public function rules(): array
    {
        return [
            'current_password' => [
                'required',
                function ($attr, $value, $fail) {
                    if (! Hash::check($value, Auth::user()->password)) {
                        $fail('Le mot de passe actuel est incorrect.');
                    }
                },
            ],
        ];
    }
}

<?php

namespace App\Http\Livewire\Profile;

use App\Actions\Fortify\UpdateUserProfileInformation;
use App\Services\Seo\SeoMetaService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Livewire\Component;

class Information extends Component
{
    public string $username;

    public string $email;

    public ?string $birth_date = null;

    public ?string $address = null;

    public ?string $city = null;

    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'profile_information', livewireComponent: $this);
        $this->username = Auth::user()->username;
        $this->email = Auth::user()->email;
        $this->birth_date = Auth::user()->birth_date?->toW3cString();
        $this->address = Auth::user()->address;
        $this->city = Auth::user()->city;
        $this->emit('datepicker:init');
    }

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function submit(): void
    {
        $validated = $this->validate();
        Auth::user()->update([
            'username' => $validated['username'],
            'email' => $validated['email'],
            'birth_date' => data_get($validated, 'birth_date') ? Date::parse(data_get($validated, 'birth_date'))->timezone('Europe/Paris')->format('Y-m-d') : null,
            'address' => data_get($validated, 'address'),
            'city' => data_get($validated, 'city'),
        ]);
        $this->dispatchBrowserEvent('toast:success', ['title' => 'Vos informations de profil ont été mises à jour.']);
    }

    public function render(): View
    {
        return view('livewire.profile.information');
    }

    /** @throws \Okipa\MediaLibraryExt\Exceptions\CollectionNotFound */
    protected function rules(): array
    {
        return app(UpdateUserProfileInformation::class)->rules(Auth::user(), false);
    }
}

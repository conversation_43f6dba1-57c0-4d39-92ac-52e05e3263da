<?php

namespace App\Http\Livewire\Profile;

use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Tabs extends Component
{
    public array $navTabs = [
        ['key' => 'profile_information', 'label' => 'Informations'],
        ['key' => 'profile_params', 'label' => 'Paramètres'],
    ];

    public ?string $selectedTabKey = null;

    protected $listeners = [
        'nav:route:loaded' => 'routeLoaded',
        'profile:logout:confirmed' => 'confirmedLogout',
    ];

    public function navToTab(string $routeKey): void
    {
        $this->emitTo('router', 'nav:to', $routeKey);
    }

    public function routeLoaded(string $routeKey): void
    {
        $this->selectedTabKey = $routeKey;
    }

    public function logout(): void
    {
        $this->dispatchBrowserEvent('popin:confirm', [
            'html' => 'Êtes-vous sûr de vouloir vous déconnecter ?',
            'onConfirm' => "Livewire.emit('profile:logout:confirmed')",
        ]);
    }

    public function confirmedLogout(): void
    {
        /** @var \App\Models\Users\User $user */
        $user = Auth::user();
        Auth::logout();
        app(UserJourneysService::class)->migrateAllUserJourneyDataFromAuthUserToSession($user->id);
        $this->dispatchBrowserEvent('userLogout');
        $this->dispatchBrowserEvent('toast:success', ['title' => 'À bientôt ' . $user->username . ' !']);
        $this->emit('user:unauthenticated');
        $this->emitTo('router', 'nav:to', 'browse');
    }

    public function render(): View
    {
        return view('livewire.profile.tabs');
    }
}

<?php

namespace App\Http\Livewire\Profile;

use App\Services\Users\UsersService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class Picture extends Component
{
    protected $listeners = [
        'profile:picture:updated' => '$refresh',
        'profile:picture:destroy:confirmed' => 'destroyProfilePictureConfirmed',
    ];

    public function destroyProfilePicture(): void
    {
        $this->dispatchBrowserEvent('popin:confirm', [
            'html' => 'Êtes-vous sûr de vouloir supprimer votre photo de profil ?',
            'onConfirm' => "Livewire.emit('profile:picture:destroy:confirmed')",
        ]);
    }

    public function destroyProfilePictureConfirmed(): void
    {
        app(UsersService::class)->setDefaultAvatar(Auth::user());
        $this->dispatchBrowserEvent('toast:success', ['title' => 'Votre photo de profil a été supprimée.']);
    }

    public function render(): View
    {
        return view('livewire.profile.picture');
    }
}

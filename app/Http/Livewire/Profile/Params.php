<?php

namespace App\Http\Livewire\Profile;

use App\Enums\ColorModesEnum;
use App\Http\Livewire\Nav\Tabs;
use App\Models\Radio\RadioStation;
use App\Models\UserSettings\UserSetting;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Enum;
use Livewire\Component;

class Params extends Component
{
    public UserSetting $userSetting;

    public bool $use_flac;

    public ?bool $dark_mode;

    public string $dark_mode_enum;

    public bool $use_geolocation;

    public string $default_home_page;

    public array $eligibleHomePages;

    public int $default_radio_station_id;

    public int $default_webradio_id;

    public array $eligibleRadioStations;

    public array $eligibleWebradios;

    public string $theme;

    public function mount(): void
    {
        $this->userSetting = Auth::user()->settings;
        $this->use_flac = $this->userSetting->use_flac;
        $this->dark_mode = $this->userSetting->dark_mode;
        $this->dark_mode_enum = $this->userSetting->dark_mode_enum->value;
        $this->use_geolocation = $this->userSetting->use_geolocation;
        $this->default_home_page = $this->userSetting->default_home_page;
        $this->eligibleHomePages = array_combine(
            collect(app(Tabs::class)->navTabs)->pluck('key')->toArray(),
            collect(app(Tabs::class)->navTabs)->pluck('label')->toArray()
        );
        $radioStations = radioStations()->load(['contentLocations']);
        $radioStationsLocalized = $radioStations->filter(
            static fn (RadioStation $item): bool => $item->point() !== null
        );
        $webradios = $radioStations->filter(
            static fn (RadioStation $item): bool => $item->point() === null
        );
        $this->eligibleRadioStations = array_combine(
            $radioStationsLocalized->pluck('id')->toArray(),
            $radioStationsLocalized->pluck('name')->toArray()
        );
        $this->eligibleWebradios = array_combine(
            $webradios->pluck('id')->toArray(),
            $webradios->pluck('name')->toArray()
        );
        $this->default_radio_station_id = $this->userSetting->default_radio_station_id ?? $radioStationsLocalized->first()->id;
        $this->default_webradio_id = $this->userSetting->default_webradio_id ?? $webradios->first()->id;
        if (! $this->use_geolocation) {
            app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId($this->default_radio_station_id);
        }
        app(UserJourneysService::class)->setSelectedDefaultSettingWebradioId($this->default_webradio_id);
        $this->userSetting->update([
            'default_radio_station_id' => $this->default_radio_station_id,
            'default_webradio_id' => $this->default_webradio_id,
        ]);
    }

    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'profile_params', livewireComponent: $this);
    }

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated(string $propertyName): void
    {
        $this->validateOnly($propertyName);
        $this->userSetting->update([$propertyName => $this->{$propertyName}]);
        if ($propertyName === 'use_geolocation' && ! $this->use_geolocation) {
            app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId($this->default_radio_station_id);
        }
        if ($propertyName === 'default_radio_station_id' && ! $this->use_geolocation) {
            app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId($this->default_radio_station_id);
        }
        if ($propertyName === 'default_webradio_id') {
            app(UserJourneysService::class)->setSelectedDefaultSettingWebradioId($this->default_webradio_id);
        }

        if ($propertyName === 'dark_mode_enum') {
            $this->theme = (Auth::user()->settings->dark_mode_enum->value);
            $this->dispatchBrowserEvent('themeChanged', ['userTheme' => $this->theme]);
        }

        $this->dispatchBrowserEvent('toast:success', ['title' => 'Vos paramètres ont été mis à jour.']);

        $this->emit('profil:params:updated');
    }

    public function render(): View
    {
        return view('livewire.profile.params');
    }

    public function rules(): array
    {
        $radioStations = radioStations()->load(['contentLocations']);

        return [
            'use_flac' => ['required', 'bool'],
            'dark_mode' => ['required', 'bool'],
            'dark_mode_enum' => ['required', new Enum(ColorModesEnum::class)],
            'use_geolocation' => ['required', 'bool'],
            'default_home_page' => [
                'required',
                'string',
                Rule::in(Arr::pluck(app(Tabs::class)->navTabs, 'key')),
            ],
            'default_radio_station_id' => [
                'required',
                'integer',
                Rule::in(Arr::pluck($radioStations->filter(
                    static fn (RadioStation $item): bool => $item->point() !== null
                ), 'id')),
            ],
            'default_webradio_id' => [
                'required',
                'integer',
                Rule::in(Arr::pluck($radioStations->filter(
                    static fn (RadioStation $item): bool => $item->point() === null
                ), 'id')),
            ],
        ];
    }
}

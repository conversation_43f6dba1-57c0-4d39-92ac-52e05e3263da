<?php

namespace App\Http\Livewire\Traits;

use Illuminate\Database\Eloquent\Builder;

trait WithCustomSearchLivewireTable
{
    public function applySearch(): Builder
    {
        if ($this->searchIsEnabled() && $this->hasSearch()) {
            $searchableColumns = $this->getSearchableColumns();

            if ($searchableColumns->count()) {
                $this->setBuilder($this->getBuilder()->where(function ($query) use ($searchableColumns) {
                    foreach ($searchableColumns as $index => $column) {
                        if ($column->hasSearchCallback()) {
                            ($column->getSearchCallback())($query, $this->getSearch());
                        } else {
                            $query->{$index === 0 ? 'where' : 'orWhere'}(
                                $column->getColumn(),
                                config('database.default') === 'pgsql' ? 'ilike' : 'like',
                                '%' . $this->getSearch() . '%'
                            );
                        }
                    }
                }));
            }
        }

        return $this->getBuilder();
    }
}

<?php

namespace App\Http\Livewire\Traits;

use Illuminate\Support\Facades\RateLimiter;

trait HasRateLimiting
{
    public function throttle(): bool
    {
        $rateLimiterKey = $this->rateLimiterKey();
        RateLimiter::hit($rateLimiterKey);
        if (RateLimiter::tooManyAttempts($rateLimiterKey, 5)) {
            $this->dispatchBrowserEvent('toast:error', [
                'title' => 'Vous avez dépassé le nombre d\'essais autorisés. Veuillez rééssayer dans '
                    . $this->getRemainingBlockedSeconds() . ' secondes.',
            ]);

            return false;
        }

        return true;
    }

    public function getRemainingBlockedSeconds(): int
    {
        return RateLimiter::availableIn($this->rateLimiterKey());
    }

    public function clearThrottle(): void
    {
        RateLimiter::clear($this->rateLimiterKey());
    }

    abstract public function rateLimiterKey(): string;
}

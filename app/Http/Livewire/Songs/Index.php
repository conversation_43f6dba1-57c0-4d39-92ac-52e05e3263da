<?php

namespace App\Http\Livewire\Songs;

use App\Models\Audio\Song;
use App\Models\Radio\LiveBroadcasting;
use App\Models\Radio\RadioStation;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use Livewire\Component;

class Index extends Component
{
    public bool $initialized = false;

    public Collection $songsNews;

    public Collection $songsNewsGO;

    public Collection $songsDedicated;

    public RadioStation $selectedRadioStation;

    protected $listeners = [
        'radio:station:universe:updated' => 'setSelectedRadioStation',
    ];

    public function mount(): void
    {
        $this->songsNews = collect();
        $this->songsNewsGO = collect();
        $this->songsDedicated = collect();
    }

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'songs', livewireComponent: $this);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        $this->initialized = true;
    }

    public function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $selectedRadioStationId)->firstOrFail();
    }

    /** @throws \Exception */
    public function render(): View
    {
        if ($this->initialized) {
            $this->setSongs();
        }

        return view('livewire.songs.index');
    }

    /** @throws \Exception */
    public function setSongs(): void
    {
        $this->songsNews = Song::musicWinmediaType()
            ->with(['performerRelationship', 'albumRelationship'])
            ->where('_station', config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE', '%/' . $this->selectedRadioStation->winmedia_id . '/%')
            ->news()
            ->orderByDesc('imedia')
            ->limit(12)
            ->get();

        $this->songsNewsGO = Song::musicWinmediaType()
            ->with(['performerRelationship', 'albumRelationship'])
            ->where('_station', config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE', '%/' . $this->selectedRadioStation->winmedia_id . '/%')
            ->news()
            ->localGO()
            ->orderByDesc('imedia')
            ->limit(12)
            ->get();

        $this->songsDedicated = LiveBroadcasting::with(['song.performerRelationship', 'song.albumRelationship', 'song.labelRelationship', 'dedicationUser.media'])
            ->where('winmedia_radio_station_id', $this->selectedRadioStation->winmedia_id)
            ->whereNotNull('song_id')
            ->whereNotNull('dedication_user_id')
            ->orderByDesc('started_at')
            ->limit(12)
            ->get();
    }
}

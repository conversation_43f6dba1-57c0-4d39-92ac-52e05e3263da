<?php

namespace App\Http\Livewire\Songs;

use App\Models\Audio\Song;
use App\Models\Radio\LiveBroadcasting;
use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Rail extends Component
{
    public bool $initialized = false;

    public bool $contentLoaded = true;

    public bool $firstInit = true;

    public ?string $title = null;

    public ?string $titleIconStart = null;

    public ?string $titleIconEnd = null;

    public string $railName;

    public Collection $songs;

    public ?string $showAllRoute = null;

    public array $showAllParams = [];

    public array $selectedSongIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllSongs = false;

    public bool $showDedicaceAuthorPill = true;

    public bool $dedicationDisabled = false;

    public ?array $inputDedicatableSongWinmediaIds = null;

    public array $dedicatableSongWinmediaIds = [];

    public RadioStation $selectedRadioStation;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    public function mount(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);

        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
    }

    //
    public function init(): void
    {
        $this->initialized = true;
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $selectedRadioStationId)->firstOrFail();
        $this->emitSelf('songs:dedicatable:update');
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \JsonException
     */
    public function setDedicatableSongIds(): void
    {
        if (! Auth::check()) {
            return;
        }
        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::user()->id,
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
        }
        $songWinmediaIds = $this->songs->pluck('imedia')->toArray();
        if (count(array_filter($songWinmediaIds)) > 0) {
            $this->dedicatableSongWinmediaIds = app(DedicationService::class)->verifySongsEligibility(
                $this->selectedRadioStation->winmedia_id,
                $songWinmediaIds
            );
        } else {
            $this->dedicatableSongWinmediaIds = [];
        }
    }

    public function render(): View
    {
        if ($this->initialized) {
            if ($this->inputDedicatableSongWinmediaIds !== null) {
                $this->dedicatableSongWinmediaIds = $this->inputDedicatableSongWinmediaIds;
            } else {
                $this->setDedicatableSongIds();
            }

            $this->updateSongStatuses();
            $this->emit('rail:refresh');
        }

        return view('livewire.songs.rail');
    }

    protected function updateSongStatuses(): void
    {
        if (isset($this->showAllParams['type']) && $this->showAllParams['type'] === 'dedicated' && $this->firstInit) {
            $this->songs = $this->songs->map(function (LiveBroadcasting $liveBroadcasting) {
                /** @var Song $song */
                $song = $liveBroadcasting->song;
                $song->dedicationUser = $liveBroadcasting->dedicationUser;

                return $song;
            });
            $this->firstInit = false;
        }

        if ($this->pauseAllSongs) {
            $this->songs = $this->songs->map(function (Song $song) {
                $song->selected = in_array($song->id, $this->selectedSongIds, true);
                $song->playing = false;
                $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);

                return $song;
            });

            return;
        }

        $this->selectedSongIds = [];

        $this->songs = $this->songs->map(function (Song $song) {
            $isPlaying = $this->playedAudioSourceClass === Song::class
                && $this->playedAudioSourceId === $song->id;
            if ($isPlaying) {
                $this->selectedSongIds[] = $song->id;
            }
            $song->selected = $isPlaying;
            $song->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
            $song->dedicatable = in_array($song->imedia, $this->dedicatableSongWinmediaIds, true);

            return $song;
        });

        $this->emit('rail:load', ['railName' => $this->railName]);
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllSongs = false;
        $this->updateSongStatuses();
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllSongs = true;
        $this->updateSongStatuses();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllSongs = false;
        $this->updateSongStatuses();
    }
}

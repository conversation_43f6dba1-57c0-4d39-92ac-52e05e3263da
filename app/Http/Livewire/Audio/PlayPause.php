<?php

namespace App\Http\Livewire\Audio;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class PlayPause extends Component
{
    public string $audioSourceClass;

    public int $audioSourceId;

    public array $audioSourceParams = [];

    public bool $isPlaying = false;

    public bool $largeMode = false;

    public ?string $color = null;

    public ?int $audioPositionId = null;

    public function playPause(): void
    {
        $this->isPlaying = ! $this->isPlaying;
        $this->emitTo(
            'footer.audio-player',
            'player:audio:source:update',
            $this->audioSourceClass,
            $this->audioSourceId,
            true,
            $this->audioSourceParams,
            $this->audioPositionId,
        );
    }

    public function render(): View
    {
        return view('livewire.audio.play-pause');
    }
}

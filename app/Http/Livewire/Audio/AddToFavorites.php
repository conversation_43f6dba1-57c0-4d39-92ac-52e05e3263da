<?php

namespace App\Http\Livewire\Audio;

use App\Models\Audio\Song;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class AddToFavorites extends Component
{
    public int $songId;

    public ?int $songFavoriteUsersCount = null;

    public bool $isFavorite = false;

    public bool $dropdownMode = false;

    public bool $largeMode = false;

    public function toggleFavorite(): void
    {
        if (! Auth::check()) {
            $this->emit('modal:show', 'login');

            return;
        }
        $currentFavoriteStatus = $this->isFavorite;
        $this->isFavorite = ! $this->isFavorite;
        $song = Song::findOrFail($this->songId);
        switch ($currentFavoriteStatus) {
            case true:
                if (Auth::user()->favoriteSongs->contains($song)) {
                    Auth::user()->favoriteSongs()->detach($song);
                    lastRadioStationsLiveBroadcasts(true);
                    $this->emit('songs:favorites:updated', 'remove');
                }
                $this->dispatchBrowserEvent('toast:success', [
                    'title' => '« ' . $song->title . ' » a été retiré de vos titres aimés.',
                ]);
                break;
            case false:
                if (! Auth::user()->favoriteSongs->contains($song)) {
                    Auth::user()->favoriteSongs()->attach($song);
                    lastRadioStationsLiveBroadcasts(true);
                    $this->emit('songs:favorites:updated', 'add');
                }
                $this->dispatchBrowserEvent('toast:success', [
                    'title' => '« ' . $song->title . '» a été ajouté à vos titres aimés.',
                ]);
                break;
        }
    }

    public function render(): View
    {
        return view('livewire.audio.add-to-favorites');
    }
}

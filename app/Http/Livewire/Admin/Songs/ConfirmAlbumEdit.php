<?php

namespace App\Http\Livewire\Admin\Songs;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class ConfirmAlbumEdit extends Component
{
    protected $listeners = [
        'songs:album:edit:confirmed' => 'isConfirmed',
        'songs:album:edit:dismissed' => 'isDismiss',
    ];

    public bool $initialized = false;

    public bool $album_edit = false;

    public bool $confirmed = false;

    public int $songs = 0;

    public function init(): void
    {
        $this->initialized = true;
    }

    public function render(): View
    {
        return view('livewire.admin.songs.confirm-album-edit');
    }

    public function toggleSwitch($checked)
    {
        if ($checked) {
            $this->dispatchBrowserEvent('popin:confirm', [
                'html' => 'En activant cette option, les ' . $this->songs . ' musiques de cet album seront modifiées pour prendre en compte le changement.',
                'onConfirm' => "Livewire.emit('songs:album:edit:confirmed')",
                'onDismiss' => "Livewire.emit('songs:album:edit:dismissed')",
            ]);
        }
    }

    public function isConfirmed()
    {
        $this->confirmed = true;
    }

    public function isDismiss()
    {
        $this->confirmed = false;
        $this->album_edit = false;
    }
}

<?php

namespace App\Http\Livewire\Admin\Songs;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class ConfirmPerformerEdit extends Component
{
    protected $listeners = [
        'songs:performer:edit:confirmed' => 'isConfirmed',
        'songs:performer:edit:dismissed' => 'isDismiss',
    ];

    public bool $initialized = false;

    public bool $performer_edit = false;

    public bool $confirmed = false;

    public int $songs = 0;

    public function init(): void
    {
        $this->initialized = true;
    }

    public function render(): View
    {
        return view('livewire.admin.songs.confirm-performer-edit');
    }

    public function toggleSwitch($checked)
    {
        if ($checked) {
            $this->dispatchBrowserEvent('popin:confirm', [
                'html' => 'En activant cette option, les ' . $this->songs . ' musiques de cette interprète seront modifiées pour prendre en compte le changement.',
                'onConfirm' => "Livewire.emit('songs:performer:edit:confirmed')",
                'onDismiss' => "Livewire.emit('songs:performer:edit:dismissed')",
            ]);
        }
    }

    public function isConfirmed()
    {
        $this->confirmed = true;
    }

    public function isDismiss()
    {
        $this->confirmed = false;
        $this->performer_edit = false;
    }
}

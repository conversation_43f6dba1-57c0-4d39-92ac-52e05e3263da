<?php

namespace App\Http\Livewire\Admin\Map;

use App\Models\Map\Place;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Livewire\Component;

class ContentLocation extends Component
{
    public $content = null;

    public string $name = 'place';

    public string|array $class = [];

    public string $placeSelectLabel = 'Lieu';

    public ?int $selectedPlace = null;

    public bool $createNewPlace = false;

    public bool $required = false;

    private array $old = [];

    public function mount(Request $request, $content = null): void
    {
        $this->old = $request->old($this->name, []);
        if ($this->old['create_new_place'] ?? false) {
            $this->createNewPlace = true;
        }

        $this->content = $content;
        $this->selectedPlace = $content?->place()?->id;
    }

    public function render(Request $request): View
    {
        $componentId = \uniqid('content-location-');

        if ($this->createNewPlace) {
            $this->dispatchBrowserEvent('leaflet-geocode-map:init', [
                'rootElementId' => $componentId,
            ]);
        } else {
            $placeOptions = Place::select(['id', 'name', 'addr_city', 'addr_zip'])
                ->orderBy('name')
                ->get()
                ->map(fn ($place) => [
                    'value' => $place->id,
                    'label' => $place->name . (
                        ($place->addr_city || $place->addr_zip)
                        ? (
                            ' ('
                            . $place->addr_city
                            . ($place->addr_city && $place->addr_zip ? ' ' : '')
                            . $place->addr_zip
                            . ')'
                        )
                        : ''
                    ),
                ])
                ->values()
                ->toJson();

            $this->dispatchBrowserEvent('tags:init', [
                'rootElementId' => $componentId,
            ]);
        }

        $variables = [
            'class' => \is_string($this->class) ? $this->class : \implode(' ', $this->class),
            'componentId' => $componentId,
            'place' => $this->content?->place(),
            'old' => $this->old,
        ];

        if (isset($placeOptions)) {
            $variables['placeOptions'] = $placeOptions;
        }

        return view('livewire.admin.map.content-location', $variables);
    }
}

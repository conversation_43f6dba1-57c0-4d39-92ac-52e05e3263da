<?php

namespace App\Http\Livewire\Admin\Announcements;

use App\Models\Announcements\Announcement;
use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\Map\Point;
use App\Models\News\NewsArticle;
use App\Models\Radio\Program;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class AnnounceableSelector extends Component
{
    public ?Announcement $announcement = null;

    public bool $initialized = false;

    public string $announceable_type = '';

    public array $announceableIdOptions = [];

    public string $announceable_id = '';

    public ?string $announceableImagePath = null;

    public ?string $announceableTitle = null;

    public ?string $announceableDescription = null;

    public function mount(): void
    {
        if ($this->announcement) {
            $this->updatedAnnounceableType($this->announcement->announceable::class);
            $this->updatedAnnounceableId($this->announcement->announceable->id);
        }
    }

    public function updatedAnnounceableType(string $announceableType): void
    {
        $this->announceable_type = $announceableType;
        $this->announceableIdOptions = match ($this->announceable_type) {
            NewsArticle::class => NewsArticle::where('active', true)
                ->orderBy('title')
                ->get()
                ->map(fn ($news) => ['value' => $news->id, 'label' => $news->title])
                ->values()
                ->toArray(),
            Event::class => Event::where('active', true)
                ->orderBy('title')
                ->get()
                ->map(fn ($event) => ['value' => $event->id, 'label' => $event->title])
                ->values()
                ->toArray(),
            Program::class => Program::orderBy('title')
                ->get()
                ->map(fn ($program) => ['value' => $program->id, 'label' => $program->title])
                ->values()
                ->toArray(),
            Podcast::class => Podcast::orderBy('published_at', 'desc')
                ->get()
                ->map(fn ($podcast) => [
                    'value' => $podcast->id,
                    'label' => $podcast->title . ' (' . $podcast->published_at->timezone('Europe/Paris')->format('d/m/Y H:i') . ')',
                ])
                ->values()
                ->toArray(),
            Playlist::class => Playlist::where('active', true)
                ->where('user_id', null)
                ->whereHas('media', function ($query) {
                    $query->where('collection_name', 'cover');
                })
                ->orderBy('published_at', 'desc')
                ->get()
                ->map(fn ($playlist) => ['value' => $playlist->id, 'label' => $playlist->title])
                ->values()
                ->toArray(),
            Point::class => Point::with('place')->get()
                ->map(fn ($point) => ['value' => $point->id, 'label' => $point->place?->name])
                ->values()
                ->toArray(),
            default => []
        };
        $this->reset('announceable_id', 'announceableImagePath', 'announceableTitle', 'announceableDescription');
    }

    public function updatedAnnounceableId(int|string $announceableId): void
    {
        if (! $announceableId) {
            $this->reset('announceable_id', 'announceableImagePath', 'announceableTitle', 'announceableDescription');

            return;
        }

        $this->announceable_id = (string) $announceableId;
        $announceable = app($this->announceable_type)->findOrFail($this->announceable_id);
        $this->announceableImagePath = match ($announceable::class) {
            NewsArticle::class => $announceable->getFirstMediaUrl('illustrations', 'thumb'),
            Event::class,
            Program::class => $announceable->getFirstMediaUrl('cover', 'thumb'),
            Podcast::class => $announceable->getFirstMediaUrl('cover', 'thumb'),
            Playlist::class => $announceable->getFirstMediaUrl('cover', 'thumb'),
            Point::class => $this->announcement?->getFirstMediaUrl('cover', 'thumb'),
            default => null,
        };

        $this->announceableTitle = match ($announceable::class) {
            NewsArticle::class => $announceable->title,
            Event::class => $announceable->title,
            Program::class => $announceable->title,
            Podcast::class => $announceable->title . ' (' . $announceable->published_at->timezone('Europe/Paris')->format('d/m/Y H:i') . ')',
            Playlist::class => $announceable->title,
            Point::class => $announceable->place?->name,
            default => null,
        };

        $this->announceableDescription = $announceable->description;
    }

    public function init(): void
    {
        $this->initialized = true;
    }

    public function render(): View
    {
        $this->emit('masonry:grid:init');
        $this->emit('announceable:refresh:autocomplete', [
            'announceable_type' => $this->announceable_type,
            'announceableIdOptions' => $this->announceableIdOptions,
            'announceable_id' => $this->announceable_id,
        ]);

        return view('livewire.admin.announcements.announceable-selector');
    }
}

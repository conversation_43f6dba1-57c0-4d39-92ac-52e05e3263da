<?php

namespace App\Http\Livewire\Admin\Programs;

use App\Models\Radio\Program;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Livewire\Component;

class RecurrencesSubPrograms extends Component
{
    public bool $initialized = false;

    public Collection $programSubPrograms;

    public ?Collection $recurrenceSubPrograms = null;

    public array $sub_programs;

    public bool $hasErrors;

    public function mount(): void
    {
        $oldRecurrenceSubPrograms = old('sub_programs');
        if ($oldRecurrenceSubPrograms) {
            $this->sub_programs = $oldRecurrenceSubPrograms;

            return;
        }
        $this->sub_programs = $this->recurrenceSubPrograms
            ? $this->recurrenceSubPrograms->map(fn (Program $program) => [
                'id' => $program->id,
                'local_time' => Date::createFromFormat('H:i:s', $program->pivot->local_time)->toDateTimeString(),
            ])->toArray()
            : [];
    }

    public function init(): void
    {
        $this->initialized = true;
    }

    public function addRecurrenceSubProgramLine(): void
    {
        $this->sub_programs[] = ['id' => null, 'local_time' => Date::now()->toW3cString()];
    }

    public function deleteRecurrenceSubProgramLine(int $key): void
    {
        unset($this->sub_programs[$key]);
        $this->emit('datetimepicker:init');
    }

    public function render(): View
    {
        $this->emit('datetimepicker:init');

        return view('livewire.admin.programs.recurrences-sub-programs');
    }
}

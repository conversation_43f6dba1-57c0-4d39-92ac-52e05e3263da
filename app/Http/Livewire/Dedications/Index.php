<?php

namespace App\Http\Livewire\Dedications;

use App\Models\Audio\Playlist;
use App\Models\Audio\Song;
use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\View\View;
use Livewire\Component;

class Index extends Component
{
    public bool $initialized = false;

    public Collection $favoriteSongs;

    public Collection $privatePlaylists;

    public Collection $suggestionsSongs;

    public Collection $publicPlaylists;

    public RadioStation $selectedRadioStation;

    public bool $dedicationDisabled = false;

    public ?string $userVerificationMessage = null;

    public array $dedicatableFavoriteSongsWinmediaIds = [];

    protected $listeners = [
        'radio:station:universe:updated' => 'setSelectedRadioStation',
        'playlist:created' => 'setPlaylists',
        'dedicacation:validated' => 'dedicacationValidated',
        'songs:favorites:updated' => '$refresh',
    ];

    public function mount(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);

        $this->favoriteSongs = collect();
        $this->publicPlaylists = collect();
        $this->suggestionsSongs = collect();
        $this->privatePlaylists = collect();
    }

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'dedicace', livewireComponent: $this);

        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::id(),
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
            $this->userVerificationMessage = $verifyUserEligibilityResponse['message'];
        }

        $this->initialized = true;
    }

    /** @throws \Exception */
    public function dedicacationValidated(): void
    {
        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::id(),
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
            $this->userVerificationMessage = $verifyUserEligibilityResponse['message'];
        }
    }

    public function setSelectedRadioStation(int $selectedRadioStationId): void
    {
        $this->selectedRadioStation = RadioStation::where('id', $selectedRadioStationId)->firstOrFail();
    }

    /** @throws \Exception */
    public function render(): View
    {
        if ($this->initialized) {
            $this->setPlaylists();
            $this->setFavoriteSongs();
            $this->setSuggestionsSongs();
        }

        return view('livewire.dedications.index');
    }

    /** @throws \Exception */
    public function setPlaylists(): void
    {
        $this->publicPlaylists = Playlist::with(['media', 'thematic', 'songs'])
            ->where('user_id', null)
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereRelation(
                    'radioStations',
                    fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStation->id)
                )->orWhereDoesntHave('radioStations');
            })
            ->where('active', true)
            ->where('published_at', '<=', Date::now())
            ->where(function (Builder $subWhereQuery) {
                $subWhereQuery->whereNull('unpublished_at')
                    ->orWhere('unpublished_at', '>=', Date::now());
            })
            ->latest('published_at')
            ->get()
            ->take(12);

        $this->privatePlaylists = Auth::user()
            ? Auth::user()->playlists()
                ->with(['media', 'songs'])
                ->latest()
                ->get()
                ->take(12)
            : collect();
    }

    /** @throws \Exception */
    public function setFavoriteSongs(): void
    {
        $favoriteWinmediaSongsIds = Auth::user()
            ->favoriteSongs()
            ->limit(500)
            ->pluck('imedia')
            ->toArray();
        if (count(array_filter($favoriteWinmediaSongsIds)) > 0) {
            //$favoriteSongsIdsOrdered = implode(',', $favoriteSongsIds);

            $this->dedicatableFavoriteSongsWinmediaIds = app(DedicationService::class)->verifySongsEligibility(
                $this->selectedRadioStation->winmedia_id,
                $favoriteWinmediaSongsIds,
            );

            if (count($this->dedicatableFavoriteSongsWinmediaIds) === 0) {
                $this->favoriteSongs = Auth::user()
                    ->favoriteSongs()
                    ->with(['performerRelationship', 'albumRelationship'])
                    ->limit(30)
                    ->get();
            } else {

                $favoritesSongsEligible = Song::whereIn('imedia', $this->dedicatableFavoriteSongsWinmediaIds)
                    ->inRandomOrder()
                    //->orderByRaw("FIELD(imedia, $favoriteSongsIdsOrdered)")
                    ->with(['performerRelationship', 'albumRelationship'])
                    ->limit(30)
                    ->get();

                if (count($this->dedicatableFavoriteSongsWinmediaIds) < 30) {
                    $moreFavoriteSongs = Auth::user()
                        ->favoriteSongs()
                        ->whereNotIn('imedia', $this->dedicatableFavoriteSongsWinmediaIds)
                        ->with(['performerRelationship', 'albumRelationship'])
                        ->limit(30 - count($this->dedicatableFavoriteSongsWinmediaIds))
                        ->get();

                    $this->favoriteSongs = $favoritesSongsEligible->merge($moreFavoriteSongs);
                } else {
                    $this->favoriteSongs = $favoritesSongsEligible;
                }
            }
        }
    }

    /** @throws \Exception */
    public function setSuggestionsSongs(): void
    {
        $songWinmediaIds = app(DedicationService::class)->verifySongsEligibility($this->selectedRadioStation->winmedia_id);
        $songIdsOrdered = implode(',', $songWinmediaIds);
        $this->suggestionsSongs = count($songWinmediaIds) > 0
            ? Song::whereIn('imedia', $songWinmediaIds)
                ->with(['performerRelationship', 'albumRelationship'])
                ->limit(30)
                ->orderByRaw(config('database.default') === 'pgsql'
                    ? "array_position(ARRAY[$songIdsOrdered], imedia)"
                    : "FIELD(imedia, $songIdsOrdered)")
                ->get(['id', 'imedia', 'performer', 'title', 'version'])
            : collect();
    }
}

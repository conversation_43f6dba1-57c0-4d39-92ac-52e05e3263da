<?php

declare(strict_types=1);

namespace App\Http\Livewire\Map;

use App\Models\Map\Place;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use App\Services\Map\MapService;
use App\Services\Map\MapStateService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Auth\AuthManager;
use Illuminate\View\View;
use InvalidArgumentException;
use Livewire\Component;

class Map extends Component
{
    public string $mapContainerId;

    public ?string $fullscreenContainerId = null;

    public ?int $stationWinmediaId = null;

    public ?int $activePlaceId = null;

    public ?float $longitude = null;

    public ?float $latitude = null;

    public ?float $zoom = null;

    protected $queryString = [
        'stationWinmediaId' => ['as' => 'station', 'except' => null],
        'activePlaceId' => ['as' => 'place', 'except' => null],
        'longitude' => ['as' => 'lng', 'except' => null],
        'latitude' => ['as' => 'lat', 'except' => null],
        'zoom' => ['except' => null],
    ];

    protected $listeners = [
        'map:place:selected' => 'whenPlaceSelected',
        'map:view-state' => 'whenViewStateChanged',
        'radio:station:universe:updated' => 'whenRadioStationUpdated',
    ];

    public function mount(
        MapStateService $stateService,
        ?string $mapContainerId = null,
        ?string $fullscreenContainerId = null,
    ): void {
        if (\is_string($mapContainerId) && empty(\trim($mapContainerId))) {
            throw new InvalidArgumentException(
                "Identifier of the map container element can't be empty."
            );
        }
        if (\is_string($fullscreenContainerId) && empty(\trim($fullscreenContainerId))) {
            throw new InvalidArgumentException(
                "Identifier of the element used for fullscreen feature can't be empty."
            );
        }

        $this->mapContainerId = $mapContainerId ?? \uniqid('map-');
        $this->fullscreenContainerId = $fullscreenContainerId;

        if ($radioStation = $stateService->getRadioStation()) {
            $this->stationWinmediaId = $radioStation->winmedia_id;
        }
        if ($place = $stateService->getActivePlace()) {
            $this->activePlaceId = $place->id;
        }
        if ($coordinates = $stateService->getCoordinates()) {
            $this->longitude = $coordinates[0];
            $this->latitude = $coordinates[1];
        }
        if ($zoom = $stateService->getZoom()) {
            $this->zoom = $zoom;
        }
    }

    public function init(MapService $mapService, AuthManager $authManager, UserJourneysService $journeysService): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'map', livewireComponent: $this);

        $radioStationId = $journeysService->getSelectedRadioStationUniverseId();
        $radioStation = RadioStation::find($radioStationId);
        $this->stationWinmediaId = $radioStation?->winmedia_id;

        $stateService = $mapService->state();
        $stateService->setRadioStation($radioStation);

        $settings = [
            'mapContainerId' => $this->mapContainerId,
            'searchMode' => $stateService->getSearchMode(),
        ];

        if ($this->fullscreenContainerId) {
            $settings['fullscreenContainerId'] = $this->fullscreenContainerId;
        }

        if ($this->activePlaceId) {
            $settings['activePlaceId'] = $this->activePlaceId;
        }

        if (isset($this->longitude, $this->latitude)) {
            $settings['position'] = [$this->longitude, $this->latitude];
        } elseif ($point = $radioStation?->point()) {
            $settings['position'] = $point->getCoordinates();
        }

        if (isset($this->zoom)) {
            $settings['zoom'] = $this->zoom;
        }

        // Paramétrage du fond de carte.
        if ($radioStation->winmedia_id === 11) {
            $settings['mapStyle'] = ['name' => 'junior', 'theme' => 'light'];
        } else {
            $settings['mapStyle'] = ['name' => 'default', 'theme' => 'both'];
        }

        if ($user = $authManager->user()) {
            \assert($user instanceof User);

            $settings['colorScheme'] = $user->settings->dark_mode_enum->value;
        }

        $this->dispatchBrowserEvent('map:init', [
            'settings' => $settings,
            'content' => [
                'type' => 'FeatureCollection',
                'features' => $mapService->loadPlaces()->toGeoJson(),
            ],
        ]);

        $this->emit('map:init');
    }

    public function whenPlaceSelected(MapStateService $stateService, ?int $placeId): void
    {
        $place = $placeId ? Place::find($placeId) : null;
        $this->activePlaceId = $place?->id;
        $stateService->setActivePlace($place)->save();
        $this->skipRender();
    }

    public function whenRadioStationUpdated(
        MapService $mapService,
        RadioStation $radioStation,
    ): void {
        $this->stationWinmediaId = $radioStation->winmedia_id;
        $mapService->state()->setRadioStation($radioStation);

        $settings = [];

        if ($point = $radioStation->point()) {
            $settings['position'] = $point->getCoordinates();
            $settings['zoom'] = 11;
        }

        // Paramétrage du fond de carte.
        if ($radioStation->winmedia_id === 11) {
            $settings['mapStyle'] = ['name' => 'junior', 'theme' => 'light'];
        } else {
            $settings['mapStyle'] = ['name' => 'default', 'theme' => 'both'];
        }

        $this->dispatchBrowserEvent('map:draw', [
            'settings' => $settings,
            'content' => [
                'type' => 'FeatureCollection',
                'features' => $mapService->loadPlaces()->toGeoJson(),
            ],
        ]);

        $this->emit('map:station:changed', $radioStation->id);

        $this->skipRender();
    }

    public function whenViewStateChanged(MapStateService $stateService, array $viewState): void
    {
        $this->longitude = \round((float) $viewState['longitude'], 6);
        $this->latitude = \round((float) $viewState['latitude'], 6);
        $this->zoom = \round((float) $viewState['zoom'], 6);

        $stateService->setCoordinates($this->longitude, $this->latitude);
        $stateService->setZoom($this->zoom);
        $stateService->save();

        $this->skipRender();
    }

    public function render(): View
    {
        return view('livewire.map.map');
    }
}

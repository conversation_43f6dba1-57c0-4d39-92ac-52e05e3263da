<?php

namespace App\Http\Livewire\Map;

use App\Services\Map\MapSearchMode;
use App\Services\Map\MapService;
use App\Services\Map\MapStateService;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;
use Livewire\Component;

class SearchForm extends Component
{
    public string $search = '';

    public string $mode = 'all';

    public bool $hideSearchModeSelector = false;

    protected $queryString = [
        'search' => ['except' => ''],
        'mode' => ['except' => 'all'],
    ];

    public function rules(): array
    {
        return [
            'mode' => [Rule::enum(MapSearchMode::class)],
        ];
    }

    public function mount(MapStateService $stateService): void
    {
        $this->search = $stateService->getSearch() ?? '';
        $this->mode = $stateService->getSearchMode()->value;
    }

    public function search(MapService $mapService): void
    {
        $mapService->state()->setSearch($this->search);

        $data = [
            'settings' => [
                'searchMode' => $this->mode,
            ],
            'content' => [
                'type' => 'FeatureCollection',
                'features' => $mapService->loadPlaces()->toGeoJson(),
            ],
        ];

        $this->dispatchBrowserEvent('map:draw', $data);

        if ($this->search) {
            $this->emit('map:search:performed', $this->mode, $this->search);
        } else {
            $this->emit('map:search:reset');
        }
    }

    public function setMode(MapService $mapService, string $mode): void
    {
        $this->mode = $mode;

        try {
            $this->validateOnly('mode');
        } catch (ValidationException) {
            abort(404);
        }

        $mapService->state()->setSearchMode(MapSearchMode::from($this->mode));

        $data = [
            'settings' => [
                'searchMode' => $this->mode,
            ],
            'content' => [
                'type' => 'FeatureCollection',
                'features' => $mapService->loadPlaces()->toGeoJson(),
            ],
        ];

        $this->dispatchBrowserEvent('map:draw', $data);

        $this->emit('map:mode:changed', $this->mode);
    }

    public function clear(MapService $mapService): void
    {
        $mapService->state()->setSearch($this->search = '');

        $data = [
            'settings' => [
                'searchMode' => $this->mode,
            ],
            'content' => [
                'type' => 'FeatureCollection',
                'features' => $mapService->loadPlaces()->toGeoJson(),
            ],
        ];

        $this->dispatchBrowserEvent('map:draw', $data);

        $this->emit('map:search:reset');
    }

    public function render(): View
    {
        return view('livewire.map.search-form', [
            'searchModes' => MapSearchMode::list(),
        ]);
    }
}

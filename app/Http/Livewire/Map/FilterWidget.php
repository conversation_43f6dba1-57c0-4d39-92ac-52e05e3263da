<?php

namespace App\Http\Livewire\Map;

use App\Models\Audio\Thematic;
use App\Services\Map\MapFilterType;
use App\Services\Map\MapService;
use App\Services\Map\MapStateService;
use Illuminate\View\View;
use Livewire\Component;

class FilterWidget extends Component
{
    public array $thematics = [];

    private bool $show = false;

    protected $queryString = [
        'thematics' => ['except' => []],
    ];

    public function mount(MapStateService $stateService): void
    {
        $this->thematics = $stateService->getFilters(MapFilterType::Thematic);
    }

    public function getHasFiltersEnabledProperty(): bool
    {
        return ! empty(\array_filter($this->thematics));
    }

    public function getHasFiltersDisabledProperty(): bool
    {
        return ! empty(\array_filter($this->thematics, fn ($value) => (bool) $value === false));
    }

    public function toggleAllThematic(MapService $mapService, bool $enable): void
    {

        $this->show = true;

        $stateService = $mapService->state();
        $enable ?
            $stateService->enableAllFilters(MapFilterType::Thematic) :
            $stateService->disableAllFilters(MapFilterType::Thematic);

        $this->thematics = $stateService->getFilters(MapFilterType::Thematic);

        $data = [
            'settings' => [],
            'content' => [
                'type' => 'FeatureCollection',
                'features' => $mapService->loadPlaces()->toGeoJson(),
            ],
        ];

        $this->dispatchBrowserEvent('map:draw', $data);

        $this->emit(
            'map:filter:changed',
            MapFilterType::Thematic->value
        );
    }

    public function toggleThematic(MapService $mapService, int $thematicId): void
    {
        $this->show = true;

        $stateService = $mapService->state();
        $stateService->toggleFilter(MapFilterType::Thematic, $thematicId);
        $this->thematics = $stateService->getFilters(MapFilterType::Thematic);

        $data = [
            'settings' => [],
            'content' => [
                'type' => 'FeatureCollection',
                'features' => $mapService->loadPlaces()->toGeoJson(),
            ],
        ];

        $this->dispatchBrowserEvent('map:draw', $data);

        $this->emit(
            'map:filter:changed',
            MapFilterType::Thematic->value,
            $thematicId,
            $stateService->isFilterEnabled(MapFilterType::Thematic, $thematicId)
        );
    }

    public function render(): View
    {
        $thematicList = [];
        foreach (Thematic::all(['id', 'title']) as $thematic) {
            $thematicList[$thematic->id] = $thematic->title;
        }

        return view('livewire.map.filter-widget', [
            'show' => $this->show,
            'thematicList' => $thematicList,
        ]);
    }
}

<?php

namespace App\Http\Livewire\Map;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\News\NewsArticle;
use App\Models\Performers\Album;
use App\Services\Map\MapService;
use DateTimeImmutable;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;
use Livewire\Component;

class TimeWidget extends Component
{
    public ?string $period = null;

    public int $value = 1;

    public int $minValue = 0;

    public bool $disabled = false;

    protected $queryString = [
        'period' => ['except' => null],
    ];

    protected $listeners = [
        'map:search:performed' => 'disableTimeline',
        'map:search:reset' => 'enableTimeline',
    ];

    private ?MapService $mapService = null;

    public function boot(MapService $mapService): void
    {
        $this->mapService = $mapService;
    }

    public function mount(): void
    {
        $currentYear = Date::now()->year;

        if ($minYear = $this->getOldestContentYear()) {
            $this->minValue = $minYear - $currentYear;
        }

        if ($period = $this->mapService->state()->getCustomPeriod()) {
            $year = (int) $period[0]->format('Y');
            $this->value = $year - $currentYear;
            $this->period = $period[0]->format('Y');
        } else {
            $this->value = 1;
            $this->period = null;
        }

        if ($this->mapService->state()->doesActiveSearchExist()) {
            $this->disabled = true;
        }
    }

    public function hydrate(): void
    {
        // Émission de l'évènement "input-range-markers:init" depuis le hook
        // "hydrate" de façon à ce qu'il soit bien déclenché pour chaque action.
        $this->dispatchBrowserEvent('input-range-markers:init', [
            'selector' => '.map-time-widget',
        ]);
    }

    public function init(): void
    {
        // Just to trigger the "input-range-markers:init" event, see hydrate().
        $this->skipRender();
    }

    public function updatedValue(): void
    {
        $since = $until = null;
        $currentYear = Date::now()->year;

        if ($this->currentPeriodExpected()) {
            $this->period = null;
            $this->mapService->state()->forgetCustomPeriod();
        } else {
            $this->period = (string) ($currentYear + $this->value);
            $this->mapService->state()->setCustomPeriod(
                $since = new DateTimeImmutable($this->period . '-01-01 00:00:00'),
                $until = new DateTimeImmutable($this->period . '-12-31 23:59:59'),
            );
        }

        $data = [
            'settings' => [],
            'content' => [
                'type' => 'FeatureCollection',
                'features' => $this->mapService->loadPlaces()->toGeoJson(),
            ],
        ];

        $this->dispatchBrowserEvent('map:draw', $data);

        $this->emit('map:period:changed', $since, $until);
    }

    public function disableTimeline(): void
    {
        // We set the period to null to remove it from the query string.
        $this->period = null;
        $this->disabled = true;
    }

    public function enableTimeline(): void
    {
        // If necessary, we compute the period again to restore it
        // in the query string.
        if (! $this->currentPeriodExpected()) {
            $this->period = (string) (Date::now()->year + $this->value);
        }
        $this->disabled = false;
    }

    public function render(): View
    {
        return view('livewire.map.time-widget', [
            'currentYear' => Date::now()->year,
        ]);
    }

    private function currentPeriodExpected(): bool
    {
        return $this->value === 1;
    }

    private function getOldestContentYear(): ?int
    {
        $years = [
            (int) Event::query()
                ->join('content_location', function (JoinClause $join) {
                    $join->where('content_location.content_type', '=', Event::class)
                        ->on('content_location.content_id', '=', 'events.id');
                })
                ->where('active', true)
                ->min(DB::raw('extract(year from started_at)')),
            (int) NewsArticle::query()
                ->join('content_location', function (JoinClause $join) {
                    $join->where('content_location.content_type', '=', NewsArticle::class)
                        ->on('content_location.content_id', '=', 'news_articles.id');
                })
                ->where('active', true)
                ->min(DB::raw('extract(year from published_at)')),
            (int) Playlist::query()
                ->join('content_location', function (JoinClause $join) {
                    $join->where('content_location.content_type', '=', Playlist::class)
                        ->on('content_location.content_id', '=', 'playlists.id');
                })
                ->where('active', true)
                ->where(function (EloquentBuilder $query): void {
                    $query->whereNull('unpublished_at')
                        ->orWhere('unpublished_at', '>=', Date::now());
                })
                ->min(DB::raw('extract(year from published_at)')),
            (int) Podcast::query()
                ->join('content_location', function (JoinClause $join) {
                    $join->where('content_location.content_type', '=', Podcast::class)
                        ->on('content_location.content_id', '=', 'podcasts.id');
                })
                ->where('active', true)
                ->min(DB::raw('extract(year from published_at)')),
            (int) Album::query()
                ->join('content_location', function (JoinClause $join) {
                    $join->where('content_location.content_type', '=', Album::class)
                        ->on('content_location.content_id', '=', 'albums.id');
                })
                ->min(DB::raw('extract(year from published_at)')),
        ];

        $years = \array_filter($years);
        if ($years) {
            return \min($years);
        }

        return null;
    }
}

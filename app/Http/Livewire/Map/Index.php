<?php

namespace App\Http\Livewire\Map;

use App\Services\Map\MapConfiguration;
use App\Services\Map\MapStateService;
use Illuminate\Auth\AuthManager;
use Illuminate\View\View;
use Livewire\Component;

class Index extends Component
{
    public bool $mapInitialized = false;

    protected $listeners = [
        'map:init' => 'whenMapInitialized',
    ];

    public function mount(
        MapStateService $stateService,
        ?MapConfiguration $config = null,
    ): void {
        if ($config) {
            $stateService->reset($config);
        } else {
            $stateService->forget();
        }
    }

    public function whenMapInitialized(): void
    {
        $this->mapInitialized = true;
    }

    public function render(AuthManager $authManager): View
    {
        return view('livewire.map.index', [
            'authenticated' => $authManager->check(),
        ]);
    }
}

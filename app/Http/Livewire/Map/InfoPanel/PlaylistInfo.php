<?php

namespace App\Http\Livewire\Map\InfoPanel;

use App\Models\Audio\Playlist;
use App\Models\Audio\Song;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use App\Services\Dedication\DedicationService;
use App\Services\Users\UserJourneysService;
use Illuminate\Auth\AuthManager;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;
use Intervention\Image\Facades\Image;
use Livewire\Component;
use Livewire\WithPagination;

/**
 * Implémentation inspirée du composant App\Http\Livewire\Playlists\Details.
 */
class PlaylistInfo extends Component
{
    use WithPagination {
        setPage as private _setPage;
    }

    private const PAGE_LENGTH = 20;

    public int $playlistId = 0;

    public bool $isCurrentSource = false;

    public bool $isPlaying = false;

    public ?int $currentSongId = null;

    public ?bool $dedicationDisabled = null;

    /** @var int[]|null */
    public ?array $dedicatableSongIds = null;

    protected $listeners = [
        'player:audio:source:updated' => 'whenAudioSourceUpdated',
        'player:sub:audio:source:updated' => 'whenSubAudioSourceUpdated',
        'player:audio:source:paused' => 'whenPlayerPaused',
        'player:audio:source:played' => 'whenPlayerResumed',
        'radio:station:universe:updated' => 'whenRadioStationUpdated',
        'songs:favorites:updated' => '$refresh',
        'user:authenticated' => '$refresh',
    ];

    private ?Playlist $playlist = null;

    private ?RadioStation $selectedRadioStation = null;

    private ?User $user = null;

    public function boot(AuthManager $authManager, UserJourneysService $journeysService): void
    {
        $this->user = $authManager->user();
        $this->selectedRadioStation = RadioStation::find(
            $journeysService->getSelectedRadioStationUniverseId()
        );
    }

    public function mount(UserJourneysService $journeysService, ?Playlist $playlist = null): void
    {
        if ($playlist) {
            $this->playlist = $playlist;
            $this->playlistId = $playlist->id;
        }

        $audioSource = $journeysService->getPlayedAudioSource();
        $subAudioSource = $journeysService->getPlayedSubAudioSource();

        $this->setPlayingStatus(
            $journeysService->getPlayerPlayingStatus(),
            $audioSource['played_audio_source_class'],
            $audioSource['played_audio_source_id'],
            $subAudioSource['played_sub_audio_source_class'],
            $subAudioSource['played_sub_audio_source_id'],
        );
    }

    public function whenRadioStationUpdated(): void
    {
        $this->dedicationDisabled = null;
        $this->dedicatableSongIds = null;
    }

    public function whenAudioSourceUpdated(string $audioSourceClass, int $audioSourceId): void
    {
        $this->setPlayingStatus(true, $audioSourceClass, $audioSourceId);
    }

    public function whenSubAudioSourceUpdated(string $audioSourceClass, int $audioSourceId): void
    {
        $this->setPlayingStatus(
            isPlayerRunning: true,
            subAudioSourceClass: $audioSourceClass,
            subAudioSourceId: $audioSourceId,
        );
    }

    public function whenPlayerPaused(): void
    {
        $this->setPlayingStatus(false);
    }

    public function whenPlayerResumed(): void
    {
        if ($this->isCurrentSource && ! $this->isPlaying) {
            $this->setPlayingStatus(true);
        }
    }

    public function setPage($page, $pageName = 'page'): void
    {
        $this->dedicatableSongIds = null;

        $this->_setPage($page, $pageName);
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }

    public function render(DedicationService $dedicationService): View
    {
        $songs = $this
            ->loadPlaylist()
            ->songsAllTypes()
            ->with(['performerRelationship', 'albumRelationship'])
            ->paginate(self::PAGE_LENGTH);

        $durations = $this->playlist->songsAllTypes()->pluck('duration');
        $numberOfSongs = $durations->count();
        $totalDuration = $durations->sum();

        // Dedication
        if ($this->user !== null) {
            if ($this->dedicationDisabled === null) {
                $userEligibilityResponse = $dedicationService->verifyUserEligibility(
                    $this->user->id,
                    $this->selectedRadioStation->winmedia_id
                );

                $this->dedicationDisabled = ! $userEligibilityResponse['success'];
            }

            if ($this->dedicatableSongIds === null) {
                if ($songs->isNotEmpty()) {
                    $this->dedicatableSongIds = $dedicationService->verifySongsEligibility(
                        $this->selectedRadioStation->winmedia_id,
                        $songs->getCollection()->pluck('imedia')->all()
                    );
                } else {
                    $this->dedicatableSongIds = [];
                }
            }
        } else {
            $this->dedicationDisabled = true;
            $this->dedicatableSongIds = [];
        }

        // Header image
        $firstCoverUrl = $this->playlist->getFirstMediaUrl('cover', 'patchwork');
        if ($firstCoverUrl) {
            $firstCoverPath = $this->playlist->getFirstMediaPath('cover', 'patchwork');
        } elseif ($songs->isNotEmpty()) {
            $firstCoverPath = Storage::path(
                'public/winmedia/pochettes_thumbnail/' . $songs->getCollection()->first()?->id . '.jpg'
            );
        }

        if (isset($firstCoverPath) && File::exists($firstCoverPath)) {
            $image = Image::make($firstCoverPath);
            $dominantColor = $image->resize(1, 1)->limitColors(1)->pickColor(0, 0);
            $dominantColor = implode(',', $dominantColor);
        }

        return view('livewire.map.info-panel.playlist', [
            'headerBgColorRgb' => $dominantColor ?? null,
            'headerBgImageUrl' => $firstCoverUrl,
            'numberOfSongs' => $numberOfSongs,
            'pageLength' => self::PAGE_LENGTH,
            'playlist' => $this->loadPlaylist(),
            'songs' => $songs,
            'totalDuration' => $totalDuration,
            'user' => $this->user,
        ]);
    }

    private function loadPlaylist(): Playlist
    {
        if ($this->playlist === null) {
            $this->playlist = Playlist::findOrFail($this->playlistId);
        }

        return $this->playlist;
    }

    private function setPlayingStatus(
        bool $isPlayerRunning,
        ?string $audioSourceClass = null,
        ?int $audioSourceId = null,
        ?string $subAudioSourceClass = null,
        ?int $subAudioSourceId = null,
    ): void {
        if ($audioSourceClass && $audioSourceId) {
            if ($audioSourceClass === Playlist::class && $audioSourceId === $this->playlistId) {
                $this->isCurrentSource = true;
                $this->isPlaying = $isPlayerRunning;
                $this->currentSongId = $subAudioSourceId;
            } else {
                $this->isCurrentSource = false;
                $this->isPlaying = false;
                $this->currentSongId = null;
            }
        } elseif ($this->isCurrentSource && $subAudioSourceClass === Song::class) {
            $this->isPlaying = $isPlayerRunning;
            $this->currentSongId = $subAudioSourceId;
        } elseif ($this->isCurrentSource) {
            $this->isPlaying = $isPlayerRunning;
        } else {
            $this->isPlaying = false;
            $this->currentSongId = null;
        }
    }
}

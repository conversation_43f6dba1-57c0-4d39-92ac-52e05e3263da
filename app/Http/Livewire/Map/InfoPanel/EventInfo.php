<?php

namespace App\Http\Livewire\Map\InfoPanel;

use App\Models\Events\Event;
use App\Services\Users\UserJourneysService;
use Illuminate\Support\Facades\File;
use Illuminate\View\View;
use Intervention\Image\Facades\Image;
use Livewire\Component;

/**
 * Implémentation inspirée du composant App\Http\Livewire\Events\Details.
 */
class EventInfo extends Component
{
    public int $eventId = 0;

    public bool $isCurrentSource = false;

    public bool $isPlaying = false;

    protected $listeners = [
        'player:audio:source:updated' => 'whenAudioSourceUpdated',
        'player:audio:source:paused' => 'whenPlayerPaused',
        'player:audio:source:played' => 'whenPlayerResumed',
    ];

    private ?Event $event = null;

    public function mount(UserJourneysService $journeysService, ?Event $event = null): void
    {
        if ($event) {
            $this->event = $event;
            $this->eventId = $event->id;
        }

        $audioSource = $journeysService->getPlayedAudioSource();
        $this->setPlayingStatus(
            $journeysService->getPlayerPlayingStatus(),
            $audioSource['played_audio_source_class'],
            $audioSource['played_audio_source_id'],
        );
    }

    public function whenAudioSourceUpdated(string $audioSourceClass, int $audioSourceId): void
    {
        $this->setPlayingStatus(true, $audioSourceClass, $audioSourceId);
    }

    public function whenPlayerPaused(): void
    {
        $this->setPlayingStatus(false);
    }

    public function whenPlayerResumed(): void
    {
        if ($this->isCurrentSource && ! $this->isPlaying) {
            $this->setPlayingStatus(true);
        }
    }

    public function render(): View
    {
        $coverUrl = $this->loadEvent()->getFirstMediaUrl('cover', 'background_gradient');

        if (File::exists($coverUrl)) {
            $image = Image::make($coverUrl);
            $dominantColor = $image->resize(1, 1)->limitColors(1)->pickColor(0, 0);
            $dominantColor = implode(',', $dominantColor);
        }

        return view('livewire.map.info-panel.event', [
            'event' => $this->loadEvent(),
            'headerBgColorRgb' => $dominantColor ?? null,
            'headerBgImageUrl' => $coverUrl,
        ]);
    }

    private function loadEvent(): Event
    {
        if ($this->event === null) {
            $this->event = Event::findOrFail($this->eventId);
        }

        return $this->event;
    }

    private function setPlayingStatus(
        bool $isPlayerRunning,
        ?string $audioSourceClass = null,
        ?int $audioSourceId = null,
    ): void {
        if ($audioSourceClass && $audioSourceId) {
            if ($audioSourceClass === Event::class && $audioSourceId === $this->eventId) {
                $this->isCurrentSource = true;
                $this->isPlaying = $isPlayerRunning;
            } else {
                $this->isCurrentSource = false;
                $this->isPlaying = false;
            }
        } elseif ($this->isCurrentSource) {
            $this->isPlaying = $isPlayerRunning;
        } else {
            $this->isPlaying = false;
        }
    }
}

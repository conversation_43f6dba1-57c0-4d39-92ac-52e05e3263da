<?php

namespace App\Http\Livewire\Map\InfoPanel;

use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use App\Services\Dedication\DedicationService;
use App\Services\Users\UserJourneysService;
use Illuminate\Auth\AuthManager;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\File;
use Illuminate\View\View;
use Intervention\Image\Facades\Image;
use Livewire\Component;

/**
 * Implémentation inspirée du composant App\Http\Livewire\Podcasts\Details.
 */
class PodcastInfo extends Component
{
    public int $podcastId = 0;

    public ?string $audioSourceClass = null;

    public ?int $audioSourceId = null;

    public bool $playerIsRunning = false;

    public ?bool $dedicationDisabled = null;

    /** @var int[]|null */
    public ?array $dedicatableSongIds = null;

    public bool $hasUserSubscribed = false;

    protected $listeners = [
        'player:audio:source:updated' => 'whenAudioSourceUpdated',
        'player:audio:source:paused' => 'whenPlayerPaused',
        'player:audio:source:played' => 'whenPlayerResumed',
        'radio:station:universe:updated' => 'whenRadioStationUpdated',
        'songs:favorites:updated' => '$refresh',
        'user:authenticated' => 'whenUserAuthenticated',
    ];

    private ?Podcast $podcast = null;

    private ?RadioStation $selectedRadioStation = null;

    private ?User $user = null;

    public function boot(AuthManager $authManager, UserJourneysService $journeysService): void
    {
        $this->user = $authManager->user();
        $this->selectedRadioStation = RadioStation::find(
            $journeysService->getSelectedRadioStationUniverseId()
        );
    }

    public function mount(UserJourneysService $journeysService, ?Podcast $podcast = null): void
    {
        if ($podcast) {
            $this->podcast = $podcast->loadMissing(['authors', 'program', 'songs']);
            $this->podcastId = $podcast->id;
        }

        $audioSource = $journeysService->getPlayedAudioSource();
        $this->audioSourceClass = $audioSource['played_audio_source_class'];
        $this->audioSourceId = $audioSource['played_audio_source_id'];
        $this->playerIsRunning = $journeysService->getPlayerPlayingStatus();

        if ($this->user) {
            $this->whenUserAuthenticated();
        }
    }

    public function hydrate(): void
    {
        // Émission de l'évènement "rail:load" depuis le hook "hydrate"
        // de façon à ce qu'il soit bien déclenché pour chaque action.
        $this->emit('rail:load', ['railName' => 'map-info-panel-program-podcasts-rail']);
    }

    public function init(): void
    {
        // Just to trigger the "rail:load" event, see hydrate().
        $this->skipRender();
    }

    public function whenRadioStationUpdated(): void
    {
        $this->dedicationDisabled = null;
        $this->dedicatableSongIds = null;
    }

    public function whenAudioSourceUpdated(string $audioSourceClass, int $audioSourceId): void
    {
        $this->audioSourceClass = $audioSourceClass;
        $this->audioSourceId = $audioSourceId;
        $this->playerIsRunning = true;
    }

    public function whenPlayerPaused(): void
    {
        $this->playerIsRunning = false;
    }

    public function whenPlayerResumed(): void
    {
        $this->playerIsRunning = true;
    }

    public function whenUserAuthenticated(): void
    {
        $this->hasUserSubscribed = $this->user->subscribedPrograms->contains($this->loadPodcast()->program_id);
    }

    public function isCurrentAudioSource(Podcast|Song $source): bool
    {
        return $source::class === $this->audioSourceClass
            && $source->id === $this->audioSourceId;
    }

    public function isPlayedAudioSource(Podcast|Song $source): bool
    {
        return $this->playerIsRunning && $this->isCurrentAudioSource($source);
    }

    public function toggleSubscription(): void
    {
        $this->hasUserSubscribed = ! $this->hasUserSubscribed;
        $this->user->subscribedPrograms()->toggle($this->loadPodcast()->program_id);
        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'Votre ' . ($this->hasUserSubscribed ? 'abonnement' : 'désabonnement') . ' a été pris en compte.',
        ]);
    }

    public function render(DedicationService $dedicationService): View
    {
        $podcast = $this->loadPodcast();

        $latestProgramPodcasts = Podcast::with(['media', 'program'])
            ->where('program_id', $podcast->program_id)
            ->where('published_at', '<=', Date::now())
            ->where('active', true)
            ->hasAudio()
            ->latest('published_at')
            ->limit(10)
            ->get();

        $podcastSongs = $podcast->songs()
            ->with(['performerRelationship', 'albumRelationship'])
            ->get();

        // Dedication
        if ($this->user) {
            if ($this->dedicationDisabled === null) {
                $userEligibilityResponse = $dedicationService->verifyUserEligibility(
                    $this->user->id,
                    $this->selectedRadioStation->winmedia_id
                );

                $this->dedicationDisabled = ! $userEligibilityResponse['success'];
            }

            if ($this->dedicatableSongIds === null) {
                if ($podcastSongs->isNotEmpty()) {
                    $this->dedicatableSongIds = $dedicationService->verifySongsEligibility(
                        $this->selectedRadioStation->winmedia_id,
                        $podcastSongs->pluck('imedia')->all()
                    );
                } else {
                    $this->dedicatableSongIds = [];
                }
            }
        } else {
            $this->dedicationDisabled = true;
            $this->dedicatableSongIds = [];
        }

        $coverUrl = $podcast->getFirstMediaUrl('cover', 'background_gradient');
        if (File::exists($coverUrl)) {
            $image = Image::make($coverUrl);
            $dominantColor = $image->resize(1, 1)->limitColors(1)->pickColor(0, 0);
            $dominantColor = implode(',', $dominantColor);
        }

        return view('livewire.map.info-panel.podcast', [
            'headerBgColorRgb' => $dominantColor ?? null,
            'headerBgImageUrl' => $coverUrl,
            'isCurrentAudioSource' => $this->isCurrentAudioSource(...),
            'isPlayedAudioSource' => $this->isPlayedAudioSource(...),
            'latestProgramPodcasts' => $latestProgramPodcasts,
            'podcast' => $podcast,
            'podcastSongs' => $podcastSongs,
            'user' => $this->user,
        ]);
    }

    private function loadPodcast(): Podcast
    {
        if ($this->podcast === null) {
            $this->podcast = Podcast::query()
                ->with(['authors', 'program', 'songs'])
                ->findOrFail($this->podcastId);
        }

        return $this->podcast;
    }
}

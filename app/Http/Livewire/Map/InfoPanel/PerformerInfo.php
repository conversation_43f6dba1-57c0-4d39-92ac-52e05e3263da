<?php

namespace App\Http\Livewire\Map\InfoPanel;

use App\Models\Audio\Song;
use App\Models\Audio\SongHistory;
use App\Models\Performers\Album;
use App\Models\Performers\Performer;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use App\Services\Dedication\DedicationService;
use App\Services\Users\UserJourneysService;
use Illuminate\Auth\AuthManager;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\View\View;
use Intervention\Image\Facades\Image;
use Livewire\Component;

/**
 * Implémentation inspirée du composant App\Http\Livewire\Performers\Details.
 */
class PerformerInfo extends Component
{
    public int $performerId = 0;

    public ?string $audioSourceClass = null;

    public ?int $audioSourceId = null;

    public bool $playerIsRunning = false;

    public ?bool $dedicationDisabled = null;

    /** @var int[]|null */
    public ?array $dedicatableSongIds = null;

    protected $listeners = [
        'player:audio:source:updated' => 'whenAudioSourceUpdated',
        'player:audio:source:paused' => 'whenPlayerPaused',
        'player:audio:source:played' => 'whenPlayerResumed',
        'radio:station:universe:updated' => 'whenRadioStationUpdated',
        'songs:favorites:updated' => '$refresh',
        'user:authenticated' => '$refresh',
    ];

    private ?Performer $performer = null;

    private ?RadioStation $selectedRadioStation = null;

    private ?User $user = null;

    public function boot(AuthManager $authManager, UserJourneysService $journeysService): void
    {
        $this->user = $authManager->user();
        $this->selectedRadioStation = RadioStation::find(
            $journeysService->getSelectedRadioStationUniverseId()
        );
    }

    public function mount(UserJourneysService $journeysService, ?Performer $performer = null): void
    {
        if ($performer) {
            $this->performer = $performer->loadMissing(['albums.songs', 'albums.contentLocations']);
            $this->performerId = $performer->id;
        }

        $audioSource = $journeysService->getPlayedAudioSource();
        $this->audioSourceClass = $audioSource['played_audio_source_class'];
        $this->audioSourceId = $audioSource['played_audio_source_id'];
        $this->playerIsRunning = $journeysService->getPlayerPlayingStatus();
    }

    public function hydrate(): void
    {
        // Émission de l'évènement "rail:load" depuis le hook "hydrate"
        // de façon à ce qu'il soit bien déclenché pour chaque action.
        $this->emit('rail:load', ['railName' => 'map-info-panel-performer-albums-rail']);
    }

    public function init(): void
    {
        // Just to trigger the "rail:load" event, see hydrate().
        $this->skipRender();
    }

    public function whenRadioStationUpdated(): void
    {
        $this->dedicationDisabled = null;
        $this->dedicatableSongIds = null;
    }

    public function whenAudioSourceUpdated(string $audioSourceClass, int $audioSourceId): void
    {
        $this->audioSourceClass = $audioSourceClass;
        $this->audioSourceId = $audioSourceId;
        $this->playerIsRunning = true;
    }

    public function whenPlayerPaused(): void
    {
        $this->playerIsRunning = false;
    }

    public function whenPlayerResumed(): void
    {
        $this->playerIsRunning = true;
    }

    public function isCurrentAudioSource(Album|Song $source): bool
    {
        return $source::class === $this->audioSourceClass
            && $source->id === $this->audioSourceId;
    }

    public function render(DedicationService $dedicationService): View
    {
        $performer = $this->loadPerformer();

        $topStreamedSongs = SongHistory::with(['song.performerRelationship', 'song.albumRelationship'])
            ->select('imedia')
            ->addSelect(DB::raw('count(*) as stream_count'))
            ->whereRelation('song', 'performer', $this->performer->name)
            ->groupBy('imedia')
            ->orderByDesc('stream_count')
            ->limit(5)
            ->get()
            ->pluck('song');

        // Dedication
        if ($this->user) {
            if ($this->dedicationDisabled === null) {
                $userEligibilityResponse = $dedicationService->verifyUserEligibility(
                    $this->user->id,
                    $this->selectedRadioStation->winmedia_id
                );

                $this->dedicationDisabled = ! $userEligibilityResponse['success'];
            }

            if ($this->dedicatableSongIds === null) {
                if ($topStreamedSongs->isNotEmpty()) {
                    $this->dedicatableSongIds = $dedicationService->verifySongsEligibility(
                        $this->selectedRadioStation->winmedia_id,
                        $topStreamedSongs->pluck('imedia')->all()
                    );
                } else {
                    $this->dedicatableSongIds = [];
                }
            }
        } else {
            $this->dedicationDisabled = true;
            $this->dedicatableSongIds = [];
        }

        $coverUrl = $performer->cover_thumb;
        if (File::exists($coverUrl)) {
            $image = Image::make($coverUrl);
            $dominantColor = $image->resize(1, 1)->limitColors(1)->pickColor(0, 0);
            $dominantColor = implode(',', $dominantColor);
        }

        return view('livewire.map.info-panel.performer', [
            'headerBgColorRgb' => $dominantColor ?? null,
            'headerBgImageUrl' => $coverUrl,
            'isCurrentAudioSource' => $this->isCurrentAudioSource(...),
            'performer' => $performer,
            'topStreamedSongs' => $topStreamedSongs,
            'user' => $this->user,
        ]);
    }

    private function loadPerformer(): Performer
    {
        if ($this->performer === null) {
            $this->performer = Performer::query()
                ->with(['albums.songs'])
                ->findOrFail($this->performerId);
        }

        return $this->performer;
    }
}

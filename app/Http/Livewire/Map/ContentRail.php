<?php

namespace App\Http\Livewire\Map;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\Map\Place;
use App\Models\News\NewsArticle;
use App\Models\Performers\Performer;
use App\Services\Map\MapSearchMode;
use App\Services\Map\MapService;
use App\Services\Map\MapStateService;
use App\Services\Users\UserJourneysService;
use Illuminate\Support\Collection;
use Illuminate\View\View;
use Livewire\Component;

class ContentRail extends Component
{
    public bool $hidden = true;

    public ?string $contentType = null;

    public ?int $placeId = null;

    public ?string $title = null;

    public ?string $showAllRoute = null;

    public ?string $audioSourceClass = null;

    public ?int $audioSourceId = null;

    public bool $playerIsRunning = false;

    protected $queryString = [
        'contentType' => [
            'as' => 'rail_type',
            'except' => null,
        ],
        'placeId' => [
            'as' => 'rail_place',
            'except' => null,
        ],
    ];

    /**
     * L'évènement "map:list:hide", absent du tableau ci-dessous, est géré
     * côté navigateur. Il provoque cependant le déclenchement de l'évènement
     * "map:list:hidden" qui est quant à lui bien pris en charge côté ici même.
     */
    protected $listeners = [
        'map:filter:changed' => '$refresh',
        'map:list:hidden' => 'hide',
        'map:list:show' => 'update',
        'map:list:shown' => 'show',
        'map:mode:changed' => 'whenModeChanged',
        'map:period:changed' => '$refresh',
        'map:search:performed' => 'whenSearchPerformed',
        'map:search:reset' => '$refresh',
        'map:station:changed' => '$refresh',
        'player:audio:source:paused' => 'whenPlayerPaused',
        'player:audio:source:played' => 'whenPlayerResumed',
        'player:audio:source:updated' => 'whenAudioSourceUpdated',
    ];

    private ?Collection $contents = null;

    private bool $firstRender = false;

    public function mount(MapStateService $stateService, UserJourneysService $journeysService): void
    {
        $this->firstRender = true;

        if ($params = $stateService->getRailParams()) {
            $this->hidden = \Browser::isMobile() && $stateService->getInfoPanelParams();
            $this->contentType = $params['content_type'];
            $this->placeId = $params['place_id'];
        }

        $audioSource = $journeysService->getPlayedAudioSource();
        $this->audioSourceClass = $audioSource['played_audio_source_class'];
        $this->audioSourceId = $audioSource['played_audio_source_id'];
        $this->playerIsRunning = $journeysService->getPlayerPlayingStatus();
    }

    public function hydrate(): void
    {
        // Émission de l'évènement "rail:load" depuis le hook "hydrate"
        // de façon à ce qu'il soit bien déclenché pour chaque action.
        $this->emit('rail:load', ['railName' => 'map-content-rail']);
    }

    public function init(): void
    {
        // Just to trigger some events, see hydrate() as well.
        $this->emit('map:list:init');
        $this->skipRender();
    }

    public function update(MapStateService $stateService, string $contentType, int $placeId): void
    {
        $this->hidden = false;
        $this->contentType = $contentType;
        $this->placeId = $placeId;

        $stateService->setRailParams($contentType, $placeId)->save();
    }

    public function show(): void
    {
        $this->hidden = false;
    }

    public function hide(): void
    {
        $this->hidden = true;
    }

    public function whenModeChanged(string $mode): void
    {
        // Quand le mode nouvellement sélectionné ne correspond pas aux contenus
        // listés, on cache le rail.
        if ($mode !== MapSearchMode::All->value && $mode !== $this->contentType) {
            $this->hidden = true;
        }
    }

    public function whenSearchPerformed(string $mode, string $search): void
    {
        // Quand le mode actuel ne correspond pas aux contenus listés,
        // on ne met pas à jour le rendu du rail.
        if ($mode !== MapSearchMode::All->value && $mode !== $this->contentType) {
            $this->skipRender();
        }
    }

    public function whenAudioSourceUpdated(string $audioSourceClass, int $audioSourceId): void
    {
        $this->audioSourceClass = $audioSourceClass;
        $this->audioSourceId = $audioSourceId;
        $this->playerIsRunning = true;
    }

    public function whenPlayerPaused(): void
    {
        $this->playerIsRunning = false;
    }

    public function whenPlayerResumed(): void
    {
        $this->playerIsRunning = true;
    }

    public function isCurrentAudioSource(Event|NewsArticle|Performer|Playlist|Podcast $source): bool
    {
        return $source::class === $this->audioSourceClass
            && $source->id === $this->audioSourceId;
    }

    public function render(MapService $mapService): View
    {
        return view('livewire.map.content-rail', [
            'contents' => $this->loadContents($mapService),
            'firstRender' => $this->firstRender,
            'isCurrentAudioSource' => $this->isCurrentAudioSource(...),
            'place' => Place::find($this->placeId),
        ]);
    }

    private function loadContents(MapService $mapService): Collection
    {
        if (! $this->contents) {
            if (! $this->placeId) {
                $this->contents = new Collection();
            } else {
                $this->contents = match ($this->contentType) {
                    'event' => $mapService->loadEvents($this->placeId),
                    'news' => $mapService->loadNews($this->placeId),
                    'performer' => $mapService->loadPerformers($this->placeId),
                    'playlist' => $mapService->loadPlaylists($this->placeId),
                    'podcast' => $mapService->loadPodcasts($this->placeId),
                    default => new Collection(),
                };
            }
        }

        return $this->contents;
    }
}

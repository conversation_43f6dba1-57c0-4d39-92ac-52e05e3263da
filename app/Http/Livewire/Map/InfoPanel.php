<?php

namespace App\Http\Livewire\Map;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\News\NewsArticle;
use App\Models\Performers\Performer;
use App\Services\Map\MapStateService;
use Browser;
use Illuminate\Database\Eloquent\Model;
use Illuminate\View\View;
use InvalidArgumentException;
use Livewire\Component;
use LogicException;

/**
 * Implémentation inspirée des composants Details, tel que par exemple :
 * - App\Http\Livewire\Events\Details
 * - App\Http\Livewire\News\Details
 */
class InfoPanel extends Component
{
    public bool $hidden = true;

    public ?string $targetType = null;

    public ?int $targetId = null;

    /** @var Event|NewsArticle|Performer|Playlist|Podcast|null */
    private ?Model $target = null;

    protected $queryString = [
        'targetType' => [
            'as' => 'info_type',
            'except' => null,
        ],
        'targetId' => [
            'as' => 'info_id',
            'except' => null,
        ],
    ];

    /**
     * L'évènement "map:info:hide", absent du tableau ci-dessous, est géré
     * côté navigateur. Il provoque cependant le déclenchement de l'évènement
     * "map:info:hidden" qui est quant à lui bien pris en charge côté ici même.
     */
    protected $listeners = [
        'card:click' => 'update',
        'map:info:hidden' => 'hide',
        'map:info:show' => 'update',
        'map:info:shown' => 'show',
    ];

    public function mount(MapStateService $stateService): void
    {
        if ($params = $stateService->getInfoPanelParams()) {
            $this->hidden = false;
            $this->targetType = $params['target_type'];
            $this->targetId = $params['target_id'];
            $this->loadModel();
        }
    }

    public function init(): void
    {
        $this->emit('map:info:init');
        $this->skipRender();
    }

    public function update(MapStateService $stateService, string $targetType, int $targetId): void
    {
        $this->hidden = false;
        $this->targetType = $targetType;
        $this->targetId = $targetId;

        $stateService->setInfoPanelParams($targetType, $targetId)->save();

        $this->emit('scroll:top', '#map-info-panel-content', 0);

        if (Browser::isMobile()) {
            $this->emit('map:list:hide');
        }
    }

    public function show(): void
    {
        $this->hidden = false;
    }

    public function hide(): void
    {
        $this->hidden = true;
    }

    public function render(): View
    {
        $this->loadModel();

        return view('livewire.map.info-panel.root', [
            'childKey' => $this->computeChildKey(),
            'target' => $this->target,
        ]);
    }

    private function loadModel(): void
    {
        if ($this->targetType) {
            $this->target = match ($this->targetType) {
                'event' => Event::find($this->targetId),
                'news' => NewsArticle::find($this->targetId),
                'performer' => Performer::find($this->targetId),
                'playlist' => Playlist::find($this->targetId),
                'podcast' => Podcast::find($this->targetId),
                default => throw new InvalidArgumentException(
                    "Unsupported \"$this->targetType\" target type."
                ),
            };
        }
    }

    private function computeChildKey(): ?string
    {
        if ($this->target) {
            $id = $this->target->getKey();
            $type = match ($this->target::class) {
                Event::class => 'event',
                NewsArticle::class => 'news',
                Performer::class => 'performer',
                Playlist::class => 'playlist',
                Podcast::class => 'podcast',
                default => throw new LogicException(\sprintf(
                    'Unsupported object type (%s).', $this->target::class
                )),
            };

            return \sprintf('map-info-panel:%s:%s', $type, $id);
        }

        return null;
    }
}

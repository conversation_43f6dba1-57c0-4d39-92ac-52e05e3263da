<?php

namespace App\Http\Livewire\Modals;

use App\Actions\Fortify\CreateNewUser;
use App\Http\Livewire\Traits\HasRateLimiting;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class CreateAccount extends Component
{
    use HasRateLimiting;

    public ?string $email = null;

    public ?string $username = null;

    public ?string $password = null;

    public ?string $address = null;

    public ?string $city = null;

    public ?string $birth_date = null;

    public bool $data_consent = false;

    public bool $passwordVisible = false;

    protected bool $firstRendering = true;

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function createAccount(): void
    {
        if (! $this->throttle()) {
            return;
        }
        $validated = $this->validate();
        $user = app(CreateNewUser::class)->create($validated);
        $this->clearThrottle();
        $this->dispatchBrowserEvent('popin:success', [
            'html' => 'Bonjour ' . $user->username . '.<br><b>Merci de valider votre compte avec le mail envoyé sur votre adresse email ' . $user->email . '</b>',
        ]);
        $this->emit('modal:close');
    }

    public function rateLimiterKey(): string
    {
        return 'create_account:' . $this->email . request()->ip();
    }

    public function togglePasswordVisibility(): void
    {
        $this->passwordVisible = ! $this->passwordVisible;
    }

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function render(): View
    {
        if ($this->firstRendering) {
            $this->dispatchBrowserEvent('password_strength_meter:init');
            $this->firstRendering = false;
        }

        return view('livewire.modals.create-account');
    }

    protected function rules(): array
    {
        return app(CreateNewUser::class)->rules();
    }
}

<?php

namespace App\Http\Livewire\Modals;

use App\Models\Audio\Playlist;
use App\Services\Elasticsearch\PlaylistIndexService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Livewire\Component;

class CreatePlaylist extends Component
{
    public string $title = '';

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function createPlaylist(): void
    {
        $validated = $this->validate();
        $playlist = Playlist::create(['user_id' => Auth::id(), 'title' => $validated['title']]);
        app(PlaylistIndexService::class)->updateOrCreate($playlist->loadMissing(['media', 'songs']));
        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'La playlist « ' . $playlist->title . ' » a été créée.',
        ]);
        $this->close();
        $this->emitTo('nav.playlists', 'playlist:created');
        $this->emitTo('playlists.index', 'playlist:created');
    }

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function cancel(): void
    {
        $this->close();
    }

    public function render(): View
    {
        return view('livewire.modals.create-playlist');
    }

    protected function rules(): array
    {
        return [
            'title' => [
                'required',
                'string',
                'max:255',
                Rule::unique(Playlist::class, 'title')->where('user_id', Auth::id()),
            ],
        ];
    }
}

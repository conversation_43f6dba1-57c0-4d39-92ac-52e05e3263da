<?php

namespace App\Http\Livewire\Modals;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class Share extends Component
{
    public ?int $itemId = null;

    public ?string $shareType = null;

    public bool $autoscroll = true;

    public function cancel(): void
    {
        $this->close();
    }

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function render(): View
    {
        $itemId = $this->itemId;

        switch ($this->shareType) {
            case 'album':
                $typeTitle = 'l\'album';
                $height = '600px';
                break;

            case 'emission':
                $typeTitle = 'l\'émission';
                $height = '600px';
                break;

            case 'playlist':
                $typeTitle = 'la playlist';
                $height = '600px';
                break;

            case 'podcast':
                $typeTitle = 'le podcast';
                $height = '100%';
                break;

            case 'song':
                $typeTitle = 'le morceau';
                $height = '100%';
                break;

            case 'article':
                $typeTitle = 'l\'article';
                $height = '100%';
                break;

            default:
                abort(404);
        }

        return view('livewire.modals.share-modal', compact('height', 'itemId', 'typeTitle'));
    }
}

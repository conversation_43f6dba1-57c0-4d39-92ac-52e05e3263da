<?php

namespace App\Http\Livewire\Modals;

use App\Http\Livewire\Traits\HasRateLimiting;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Password;
use Livewire\Component;

class ForgottenPassword extends Component
{
    use HasRateLimiting;

    public ?string $email = null;

    protected array $rules = ['email' => ['required', 'string', 'email']];

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function sendPasswordResetLink(): void
    {
        if (! $this->throttle()) {
            return;
        }
        $validated = $this->validate();
        $status = Password::broker(config('fortify.passwords'))->sendResetLink(['email' => $validated['email']]);
        $success = $status === Password::RESET_LINK_SENT;
        $this->dispatchBrowserEvent($success ? 'toast:success' : 'toast:error', ['title' => __($status)]);
        if ($success) {
            $this->clearThrottle();
            $this->emit('modal:show', 'login');
        }
    }

    public function rateLimiterKey(): string
    {
        return 'forgotten_password:' . $this->email . request()->ip();
    }

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function render(): View
    {
        return view('livewire.modals.forgotten-password');
    }
}

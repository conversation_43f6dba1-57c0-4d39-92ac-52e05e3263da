<?php

namespace App\Http\Livewire\Modals;

use App\Http\Livewire\Traits\HasRateLimiting;
use App\Models\Users\User;
use Illuminate\Contracts\View\View;
use Livewire\Component;

class ResendVerificationMail extends Component
{
    use HasRateLimiting;

    public ?string $email = null;

    protected array $rules = ['email' => ['required', 'string', 'email']];

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function sendMailVerificationLink(): void
    {
        if (! $this->throttle()) {
            return;
        }
        $validated = $this->validate();
        $user = User::whereEmail($validated['email'])->first();
        if ($user) {
            $this->clearThrottle();
            $user->sendEmailVerificationNotification();
            $this->dispatchBrowserEvent('toast:success', ['title' => 'Le mail de validation de votre compte a bien été envoyé sur l\'adresse email ' . $validated['email']]);
            $this->close();
        } else {
            $this->dispatchBrowserEvent('toast:error', ['title' => 'Nous ne trouvons pas d\'utilisateur associé à cette adresse email.']);
        }
    }

    public function rateLimiterKey(): string
    {
        return 'verification_mail:' . $this->email . request()->ip();
    }

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function render(): View
    {
        return view('livewire.modals.resend-verification-mail');
    }
}

<?php

namespace App\Http\Livewire\Modals;

use App\Models\Users\User;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;
use Livewire\TemporaryUploadedFile;
use Livewire\WithFileUploads;

class ChangeProfilePicture extends Component
{
    use WithFileUploads;

    public TemporaryUploadedFile|string|null $profile_picture = null;

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function submit(): void
    {
        $validated = $this->validate();
        Auth::user()
            ->addMedia($validated['profile_picture'])
            ->preservingOriginal()
            ->toMediaCollection('profile_picture');
        $this->close();
        $this->emit('profile:picture:updated');
        $this->dispatchBrowserEvent('toast:success', ['title' => 'Votre photo de profil a <PERSON>té mise à jour']);
    }

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function render(): View
    {
        return view('livewire.modals.change-profile-picture');
    }

    /** @throws \Okipa\MediaLibraryExt\Exceptions\CollectionNotFound */
    protected function rules(): array
    {
        return ['profile_picture' => app(User::class)->getMediaValidationRules('profile_picture')];
    }
}

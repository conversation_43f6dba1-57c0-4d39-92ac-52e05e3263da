<?php

namespace App\Http\Livewire\Modals;

use App\Models\Radio\RadioStation;
use App\Models\UserSettings\UserSetting;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

class ChooseDefaultRadioStation extends Component
{
    public bool $initialized = false;

    public UserSetting $userSetting;

    public bool $use_geolocation = false;

    public int $default_radio_station_id;

    public int $default_webradio_id;

    public array $eligibleRadioStations;

    public array $eligibleWebradios;

    /** @throws \Exception */
    public function mount(): void
    {
        $this->userSetting = Auth::user()->settings;
        $this->use_geolocation = $this->userSetting->use_geolocation;
        $radioStationsLocalized = radioStations()->filter(function (RadioStation $radioStation) {
            return $radioStation->point() !== null;
        });
        $webradios = radioStations()->filter(function (RadioStation $radioStation) {
            return $radioStation->point() === null;
        });
        $this->eligibleRadioStations = array_combine(
            $radioStationsLocalized->pluck('id')->toArray(),
            $radioStationsLocalized->pluck('name')->toArray()
        );
        $this->eligibleWebradios = array_combine(
            $webradios->pluck('id')->toArray(),
            $webradios->pluck('name')->toArray()
        );
        $this->default_radio_station_id = $this->userSetting->default_radio_station_id ?? $radioStationsLocalized->first()->id;
        $this->default_webradio_id = $this->userSetting->default_webradio_id ?? $webradios->first()->id;
        $this->userSetting->update([
            'default_radio_station_id' => $this->default_radio_station_id,
            'default_webradio_id' => $this->default_webradio_id,
        ]);
        app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId($this->default_radio_station_id);
        app(UserJourneysService::class)->setSelectedDefaultSettingWebradioId($this->default_webradio_id);
    }

    /** @throws \Illuminate\Http\Client\RequestException */
    public function init(): void
    {
        $this->initialized = true;
    }

    /** @throws \Illuminate\Http\Client\RequestException */
    public function submit(): void
    {
        $this->userSetting->update([
            'default_radio_station_id' => $this->default_radio_station_id,
            'default_webradio_id' => $this->default_webradio_id,
            'use_geolocation' => $this->use_geolocation,
        ]);
        app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId($this->default_radio_station_id);
        app(UserJourneysService::class)->setSelectedDefaultSettingWebradioId($this->default_webradio_id);
        $this->emit('profil:params:updated');
        $this->close();
    }

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function render(): View
    {
        return view('livewire.modals.choose-default-radio-station');
    }
}

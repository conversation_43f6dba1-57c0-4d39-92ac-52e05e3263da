<?php

namespace App\Http\Livewire\Modals;

use Illuminate\Contracts\View\View;
use Livewire\Component;

class Cropper extends Component
{
    public string $imageUrl;

    public float $aspectRatio;

    public float $centerX;

    public float $centerY;

    public float $zoom;

    public float $naturalWidth;

    public float $naturalHeight;

    public float $returnFilePondPercentX;

    public float $returnFilePondPercentY;

    public float $returnFilePondZoom;

    public float $returnFilePondAreaRatio;

    public function close(): void
    {
        $this->emit('cropper:end', $this->returnFilePondPercentX, $this->returnFilePondPercentY, $this->returnFilePondZoom, $this->returnFilePondAreaRatio);
        $this->emit('modal:close');
    }

    public function render(): View
    {
        return view('livewire.modals.cropper');
    }
}

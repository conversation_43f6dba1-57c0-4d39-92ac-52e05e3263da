<?php

namespace App\Http\Livewire\Modals;

use App\Models\Audio\Song;
use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\Validation\Rule;
use Livewire\Component;

class DedicationValidation extends Component
{
    public bool $initialized = false;

    public ?string $dedicationMessage = '';

    public string $selectedRadioName;

    public int $selectedRadioWinmediaId;

    public ?string $selectedTimestamp = '';

    public int $songId;

    public Collection $timestampSlots;

    /** @throws \Exception */
    public function mount(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $selectedRadioStation = radioStations()->firstWhere('id', $selectedRadioStationId);
        if (! $selectedRadioStation) {
            $this->dispatchBrowserEvent('popin:error', [
                'html' => 'Une erreur imprévue est survenue. Veuillez rééssayer ultérieurement.',
            ]);
            $this->emit('event:track:dedicace', 'error');
            $this->close();

            return;
        }
        $this->selectedRadioName = $selectedRadioStation->name;
        $this->selectedRadioWinmediaId = $selectedRadioStation->winmedia_id;
    }

    /** @throws \Illuminate\Http\Client\RequestException */
    public function init(): void
    {
        $this->updateTimestampSlots();

        $this->initialized = true;
    }

    public function close(bool $cancelAfterValidated = false): void
    {
        if (! $cancelAfterValidated) {
            $this->emit('event:track:dedicace', 'canceled');
        }
        $this->emit('modal:close');
    }

    /** @throws \Illuminate\Http\Client\RequestException */
    public function updateTimestampSlots(): void
    {
        // FABRICE > HACK pour dev ##########
        /*$testTimestamps = [
            '2021-12-06 10:25:00',
            '2021-12-06 10:30:00',
            '2021-12-07 12:15:00',
        ];*/
        // ENDHACK

        $selectedSong = Song::find($this->songId);

        $this->timestampSlots = array_reduce(
            //$testTimestamps,
            app(DedicationService::class)->getTimestampSlots($selectedSong->imedia, $this->selectedRadioWinmediaId),
            static function (Collection $options, string $datetimeSlot) {
                $datetime = Date::parse($datetimeSlot);

                return $options->put($datetime->isoFormat('X'), $datetime->isoFormat('D MMM H\hmm'));
            },
            new Collection()
        );

        $this->selectedTimestamp = $this->timestampSlots->keys()->first();
    }

    /** @throws \Illuminate\Http\Client\RequestException */
    public function updateRadioStation(int $radioStationId): void
    {
        $selectedRadioStation = RadioStation::where('winmedia_id', $radioStationId)->first();
        $this->selectedRadioName = $selectedRadioStation->name;
        $this->selectedRadioWinmediaId = $radioStationId;
        $this->updateTimestampSlots();
    }

    public function updateTimestamp(string $timestamp): void
    {
        $this->selectedTimestamp = $timestamp;
    }

    /** @throws \Illuminate\Http\Client\RequestException */
    public function submit(): void
    {
        if ($this->initialized) {
            $this->validate();

            $selectedSong = Song::find($this->songId);

            $response = app(DedicationService::class)->validate(
                Auth::id(),
                $this->selectedRadioWinmediaId,
                $selectedSong->imedia,
                $this->selectedTimestamp,
                $this->dedicationMessage
            );

            if (! $response['success']) {
                $this->dispatchBrowserEvent('popin:error', ['html' => $response['message']]);

                return;
            }

            $this->emit('event:track:dedicace', 'validated');
            $this->dispatchBrowserEvent('popin:success', ['html' => $response['message']]);
            $this->emit('dedicacation:validated');
            $this->close(true);
        }
    }

    public function render(): View
    {
        $selectedSong = Song::find($this->songId);

        return view('livewire.modals.dedication-validation', compact('selectedSong'));
    }

    protected function rules(): array
    {
        return [
            'selectedRadioWinmediaId' => ['required', 'int', Rule::exists(RadioStation::class, 'winmedia_id')],
            'selectedTimestamp' => ['required', 'string', Rule::in($this->timestampSlots->keys())],
            //'dedicationMessage' => ['nullable','string'],
        ];
    }
}

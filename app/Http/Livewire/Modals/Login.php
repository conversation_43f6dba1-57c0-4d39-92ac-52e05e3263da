<?php

namespace App\Http\Livewire\Modals;

use App\Http\Livewire\Traits\HasRateLimiting;
use App\Models\Users\User;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Livewire\Component;

class Login extends Component
{
    use HasRateLimiting;

    public ?string $email = null;

    public ?string $password = null;

    public bool $remember = false;

    public bool $passwordVisible = false;

    public ?string $destinationRouteKey = null;

    protected array $rules = [
        'email' => ['required', 'string'],
        'password' => ['required', 'string'],
        'remember' => ['required', 'boolean'],
    ];

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    public function rateLimiterKey(): string
    {
        return 'login:' . $this->email . request()->ip();
    }

    public function login(): void
    {
        if (! $this->throttle()) {
            return;
        }
        $validated = $this->validate();
        $loggedOutSessionId = Session::getId();
        if (
            Auth::attempt(
                ['email' => $validated['email'], 'password' => $validated['password']],
                $validated['remember']
            )
        ) {
            $this->clearThrottle();
            if (Auth::user()->hasVerifiedEmail()) {
                app(UserJourneysService::class)->migrateAllUserJourneyDataFromSessionToAuthUser($loggedOutSessionId);
                $user = User::where('email', $validated['email'])->sole();
                $this->dispatchBrowserEvent('toast:success', ['title' => 'Bienvenue ' . $user->username . '.']);
                $this->emit('user:authenticated');
                app(UserJourneysService::class)->setSelectedDefaultSettingWebradioId(Auth::user()?->settings->default_webradio_id);
                if (! Auth::user()?->settings->use_geolocation) {
                    app(UserJourneysService::class)->setSelectedDefaultSettingRadioStationId(Auth::user()?->settings->default_radio_station_id);
                }
                if (Auth::user()?->settings->default_radio_station_id === null) {
                    $this->emit('modal:show', 'choose-default-radio-station');
                } else {
                    $this->emit('profil:params:updated');
                    $this->emit('modal:close');
                }
                $this->dispatchBrowserEvent('userLogin', ['userTheme' => Auth::user()?->settings->dark_mode_enum->value]);
                if ($this->destinationRouteKey) {
                    $this->emitTo('router', 'nav:to', $this->destinationRouteKey);
                }
            } else {
                Auth::logout();
                $this->emit('modal:show', 'resend-verification-mail');
            }
        } else {
            $this->dispatchBrowserEvent('toast:error', ['title' => 'Identifiants incorrects.']);
        }
    }

    public function togglePasswordVisibility(): void
    {
        $this->passwordVisible = ! $this->passwordVisible;
    }

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function render(): View
    {
        return view('livewire.modals.login');
    }
}

<?php

namespace App\Http\Livewire\Modals;

use App\Models\Audio\Playlist;
use App\Models\Audio\Song;
use App\Services\Elasticsearch\PlaylistIndexService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Livewire\Component;

class AddSongToPlaylist extends Component
{
    public ?int $songId = null;

    public Song $song;

    public array $playlists;

    public string $playlist_id = '';

    public function mount(): void
    {
        $this->song = Song::findOrFail($this->songId);
        $this->playlists = Auth::user()->playlists()->pluck('title', 'id')->toArray();
    }

    public function addToPlaylist(): void
    {
        $validated = $this->validate();
        /** @var \App\Models\Audio\Playlist $playlist */
        $playlist = Auth::user()->playlists()->findOrFail($validated['playlist_id']);
        $songs = $playlist->songs()->get();
        if ($songs->where('id', $this->songId)->first()) {
            $this->dispatchBrowserEvent('toast:error', [
                'title' => 'Ce titre est déjà dans la playlist « ' . $playlist->title . ' ».',
            ]);

            return;
        }
        $songIdsWithPivot = $songs->add(new Song(['id' => $this->songId]))
            ->mapWithKeys(fn (Song $song, int $index) => [$song->id => ['index' => $index]])
            ->toArray();
        $playlist->songs()->sync($songIdsWithPivot);
        app(PlaylistIndexService::class)->updateOrCreate($playlist->loadMissing(['media', 'songs']));
        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'Le titre a été ajouté à la playlist « ' . $playlist->title . ' ».',
        ]);
        $this->emit('playlist:updated');
        $this->close();
    }

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function cancel(): void
    {
        $this->close();
    }

    public function render(): View
    {
        return view('livewire.modals.add-song-to-playlist');
    }

    /** @throws \Illuminate\Validation\ValidationException */
    public function updated($propertyName): void
    {
        $this->validateOnly($propertyName);
    }

    protected function rules(): array
    {
        return ['playlist_id' => ['required', 'integer', Rule::exists(Playlist::class, 'id')]];
    }
}

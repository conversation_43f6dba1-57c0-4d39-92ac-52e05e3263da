<?php

namespace App\Http\Livewire\Modals;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\Map\Place;
use App\Models\News\NewsArticle;
use App\Models\Performers\Performer;
use App\Services\Map\MapFilterType;
use App\Services\Map\MapStateService;
use Illuminate\Routing\UrlGenerator;
use Illuminate\View\View;
use Livewire\Component;

class ShareMap extends Component
{
    public string $activeTab = 'link';

    public bool $includePeriod = true;

    public bool $includeActivePlace = true;

    public bool $centerOnActivePlace = false;

    public bool $includeStation = true;

    public bool $includeRail = true;

    public bool $includeInfo = true;

    public bool $includeZoom = true;

    public function close(): void
    {
        $this->emit('modal:close');
    }

    public function render(MapStateService $stateService, UrlGenerator $urlGenerator): View
    {
        $data = [];
        $query = [];
        $coordinates = [];

        if ($station = $stateService->getRadioStation()) {
            $data['stationName'] = $station->name;
            if ($this->includeStation) {
                $query['station'] = $station->winmedia_id;
            }
        }

        if ($stateService->doesActiveSearchExist()) {
            $query += [
                'search' => $stateService->getSearch(),
                'mode' => $stateService->getSearchMode(),
            ];
        } elseif ($stateService->doesSearchModeSelected()) {
            $query['mode'] = $stateService->getSearchMode();
        }

        if ($thematics = $stateService->getFilters(MapFilterType::Thematic)) {
            $query['thematics'] = $thematics;
        }

        if ($this->includePeriod && ($period = $stateService->getCustomPeriod())) {
            $query['period'] = $period[0]->format('Y');
        }

        if ($place = $stateService->getActivePlace()) {
            $data['activePlace'] = $place;
            if ($this->includeActivePlace) {
                $query['place'] = $place->id;
                if ($this->centerOnActivePlace) {
                    $coordinates = $place->currentPoint->getCoordinates();
                }
            }
        }

        if ($railParams = $stateService->getRailParams()) {
            $data['railContentTypeLabel'] = $this->getContentTypeLabel($railParams['content_type']);
            $data['railPlace'] = Place::find($railParams['place_id']);

            if ($this->includeRail) {
                $query += [
                    'rail_type' => $railParams['content_type'],
                    'rail_place' => $railParams['place_id'],
                ];
            }
        }

        if ($infoPanelParams = $stateService->getInfoPanelParams()) {
            $target = $this->loadEntity($infoPanelParams['target_type'], $infoPanelParams['target_id']);
            $data['infoPanelTarget'] = $target;
            $data['infoPanelTargetLabel'] = $this->getEntityLabel($target);

            if ($this->includeInfo) {
                $query += [
                    'info_type' => $infoPanelParams['target_type'],
                    'info_id' => $infoPanelParams['target_id'],
                ];
            }
        }

        if (! $coordinates) {
            $coordinates = $stateService->getCoordinates();
        }
        if ($coordinates) {
            $query += [
                'lng' => $coordinates[0],
                'lat' => $coordinates[1],
            ];
        }

        if ($this->includeZoom && $stateService->getZoom()) {
            $query['zoom'] = $stateService->getZoom();
        }

        if ($this->activeTab === 'iframe') {
            $query['shareType'] = 'map';
            $iframeUrl = $urlGenerator->route('sharing.index', $query);
            $textareaContent = $this->getIframeSnippet($iframeUrl);
            $copyConfirmMessage = 'Le code a été copié.';
        } else {
            $textareaContent = $urlGenerator->route('app.map.index', $query);
            $copyConfirmMessage = 'Le lien a été copié.';
        }

        $data += [
            'hasStation' => (bool) $stateService->getRadioStation(),
            'hasPeriod' => (bool) $stateService->getCustomPeriod(),
            'hasActivePlace' => (bool) $stateService->getActivePlace(),
            'hasRail' => (bool) $stateService->getRailParams(),
            'hasInfoPanel' => (bool) $stateService->getInfoPanelParams(),
            'textareaContent' => $textareaContent,
            'copyConfirmMessage' => $copyConfirmMessage,
        ];

        return view('livewire.modals.share-map', $data);
    }

    protected function loadEntity($type, $id): Event|NewsArticle|Performer|Playlist|Podcast|null
    {
        return match ($type) {
            'event' => Event::find($id),
            'news' => NewsArticle::find($id),
            'performer' => Performer::find($id),
            'playlist' => Playlist::find($id),
            'podcast' => Podcast::find($id),
            default => null,
        };
    }

    protected function getEntityLabel(Event|NewsArticle|Performer|Playlist|Podcast $object): string
    {
        return match ($object::class) {
            Event::class,
            NewsArticle::class,
            Playlist::class,
            Podcast::class => $object->title ?? '',
            Performer::class => $object->name ?? '',
            default => '',
        };
    }

    protected function getContentTypeLabel(string $type): string
    {
        return match ($type) {
            'event' => 'évènement',
            'news' => 'actualité',
            'performer' => 'artiste',
            'playlist' => 'playlist',
            'podcast' => 'podcast',
            default => 'contenu',
        };
    }

    protected function getIframeSnippet(string $url): string
    {
        return \sprintf('<iframe src="%s" width="100%%" height="800px" loading="lazy" style="border-style: none;"></iframe>', $url);
    }
}

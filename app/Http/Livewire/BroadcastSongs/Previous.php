<?php

namespace App\Http\Livewire\BroadcastSongs;

use App\Models\Radio\LiveBroadcasting;
use App\Models\Radio\RadioStation;
use App\Services\Dedication\DedicationService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\View\View;
use Livewire\Component;

class Previous extends Component
{
    public bool $initialized = false;

    public RadioStation $selectedRadioStation;

    public ?string $selectedDate = null;

    public Collection $liveBroadcasts;

    public array $selectedBroadcastIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllBroadcastSongs = false;

    public bool $dedicationDisabled = false;

    public array $dedicatableSongWinmediaIds = [];

    protected $listeners = [
        'radio:station:universe:updated' => 'updateSelectedRadio',
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
        'songs:dedicatable:update' => 'setDedicatableSongIds',
        'songs:favorites:updated' => '$refresh',
        'user:authenticated' => '$refresh',
        'user:unauthenticated' => '$refresh',
    ];

    /** @throws \Exception */
    public function mount(): void
    {
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStation($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
    }

    /** @throws \Exception */
    protected function setSelectedRadioStation(int $radioStationId): void
    {
        $this->selectedRadioStation = radioStations()->firstOrFail('id', $radioStationId);
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function init(): void
    {
        $this->selectedDate = Date::now('Europe/Paris')->format('Y-m-d H:i');
        $this->setLiveBroadcasts();
        $this->initialized = true;
        $this->emitSelf('songs:dedicatable:update');
    }

    public function setLiveBroadcasts(): void
    {
        $selectedDate = Date::parse($this->selectedDate, 'Europe/Paris')->timezone('UTC');
        $this->liveBroadcasts = LiveBroadcasting::with(['song.performerRelationship', 'song.albumRelationship', 'song.labelRelationship', 'dedicationUser.media', 'song.favoriteUsers'])
            ->whereBetween('started_at', [
                $selectedDate->subMinutes(120)->startOfMinute(),
                $selectedDate->addMinutes(5)->endOfMinute(),
            ])
            ->where('winmedia_radio_station_id', $this->selectedRadioStation->winmedia_id)
            ->whereNotNull('song_id')
            ->orderByDesc('started_at')
            ->get();
        $this->updateLiveBroadcastStatuses();
    }

    protected function updateLiveBroadcastStatuses(): void
    {
        if ($this->pauseAllBroadcastSongs) {
            $this->liveBroadcasts = $this->liveBroadcasts->map(function (LiveBroadcasting $liveBroadcast) {
                $liveBroadcast->selected = in_array($liveBroadcast->id, $this->selectedBroadcastIds, true);
                $liveBroadcast->playing = false;
                $liveBroadcast->dedicatable = in_array(
                    $liveBroadcast->song->imedia,
                    $this->dedicatableSongWinmediaIds,
                    true
                );

                return $liveBroadcast;
            });

            return;
        }
        $this->selectedBroadcastIds = [];
        $this->liveBroadcasts = $this->liveBroadcasts->map(function (LiveBroadcasting $liveBroadcast) {
            $isPlaying = $this->playedAudioSourceClass === $liveBroadcast->song::class
                && $liveBroadcast->song->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedBroadcastIds[] = $liveBroadcast->id;
            }
            $liveBroadcast->selected = $isPlaying;
            $liveBroadcast->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
            $liveBroadcast->dedicatable = in_array($liveBroadcast->song->imedia, $this->dedicatableSongWinmediaIds, true);

            return $liveBroadcast;
        });
    }

    /** @throws \Exception */
    public function updateSelectedRadio(int $radioStationId): void
    {
        $this->setSelectedRadioStation($radioStationId);
        $this->setLiveBroadcasts();
        $this->emitSelf('songs:dedicatable:update');
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \JsonException
     */
    public function setDedicatableSongIds(): void
    {
        if (! Auth::check()) {
            $this->updateLiveBroadcastStatuses();

            return;
        }
        $verifyUserEligibilityResponse = app(DedicationService::class)->verifyUserEligibility(
            Auth::user()->id,
            $this->selectedRadioStation->winmedia_id
        );
        if (! (bool) $verifyUserEligibilityResponse['success']) {
            $this->dedicationDisabled = true;
        }
        $songWinmediaIds = $this->liveBroadcasts->pluck('song.imedia')->unique()->values()->toArray();
        if (count(array_filter($songWinmediaIds)) > 0) {
            $this->dedicatableSongWinmediaIds = app(DedicationService::class)->verifySongsEligibility(
                $this->selectedRadioStation->winmedia_id,
                $songWinmediaIds
            );
        } else {
            $this->dedicatableSongWinmediaIds = [];
        }
        $this->updateLiveBroadcastStatuses();
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllBroadcastSongs = false;
        $this->updateLiveBroadcastStatuses();
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllBroadcastSongs = true;
        $this->updateLiveBroadcastStatuses();
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllBroadcastSongs = false;
        $this->updateLiveBroadcastStatuses();
    }

    public function updatedSelectedDate(): void
    {
        $this->setLiveBroadcasts();
        $this->emitSelf('songs:dedicatable:update');
    }

    public function render(): View
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'previous_broadcast_songs', livewireComponent: $this);
        $this->emit('datetimepicker:init');

        return view('livewire.broadcast-songs.previous');
    }
}

<?php

namespace App\Http\Livewire\MobilePlayer;

use App\Models\Audio\Song;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Auth;
use Livewire\Component;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class CurrentAudioDetails extends Component
{
    public bool $initialized = false;

    public ?int $playingRadioStationId = null;

    public ?string $audioSourceClass;

    public ?string $audioSourceId;

    public ?string $cover = null;

    public ?string $title = null;

    public ?string $subtitle = null;

    public ?string $details = null;

    public ?string $additionalDetails = null;

    public array $binding = [];

    public ?string $dedicationUserCoverThumb = null;

    public ?string $dedicationUserName = null;

    public bool $canAddToFavorites = false;

    public bool $canAddToPlaylist = false;

    public bool $songIsInFavorites = false;

    public ?int $songFavoriteUsersCount = null;

    protected $listeners = [
        'audio:current:details:updated' => 'setPlayedAudioDetails',
        'user:authenticated' => '$refresh',
        'user:unauthenticated' => '$refresh',
        'songs:favorites:updated' => 'setSongFavoriteStatus',
    ];

    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function setPlayedAudioDetails(
        string $audioSourceClass,
        string $audioSourceId,
        string $cover,
        string $title,
        string $subtitle,
        ?string $details,
        ?string $additionalDetails,
        ?string $dedicationUserCoverThumb,
        ?string $dedicationUserName,
        bool $canAddToFavorites = false,
        bool $canAddToPlaylist = false,
        array $binding = [],
        ?int $songFavoriteUsersCount = null
    ): void {
        $this->audioSourceClass = $audioSourceClass;
        $this->audioSourceId = $audioSourceId;
        $this->cover = $cover;
        $this->title = $title;
        $this->subtitle = $subtitle;
        $this->details = $details;
        $this->additionalDetails = $additionalDetails;
        $this->binding = $binding;
        $this->dedicationUserCoverThumb = $dedicationUserCoverThumb;
        $this->dedicationUserName = $dedicationUserName;
        $this->songFavoriteUsersCount = $songFavoriteUsersCount;
        $this->initialized = true;
        $this->authorizeActions(addToFavorites: $canAddToFavorites, addToPlaylist: $canAddToPlaylist);
        $this->setSongFavoriteStatus();
    }

    protected function authorizeActions(bool $addToFavorites, $addToPlaylist): void
    {
        $this->canAddToFavorites = $addToFavorites;
        $this->canAddToPlaylist = $addToPlaylist;
    }

    public function setSongFavoriteStatus(?string $action = null): void
    {
        if (! Auth::user()) {
            return;
        }
        if ($this->audioSourceClass !== Song::class) {
            return;
        }
        $this->songIsInFavorites = Auth::user()->favoriteSongs()->where('id', $this->audioSourceId)->exists();
        if ($action === 'add') {
            $this->songFavoriteUsersCount++;
        } elseif ($action === 'remove') {
            $this->songFavoriteUsersCount--;
        }
    }

    public function render(): View
    {
        return view('livewire.mobile-player.current-audio-details');
    }
}

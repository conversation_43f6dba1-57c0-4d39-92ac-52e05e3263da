<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Events\Event;
use App\View\Components\Admin\Media\Thumb;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;
use Rappasoft\LaravelLivewireTables\Views\Filters\SelectFilter;

class EventsTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public function builder(): Builder
    {
        return Event::query()
            ->with(['contentLocations'])
            ->select();
    }

    public function mount()
    {
        $user = Auth::user();
        if ($user?->team?->unique_key === 'volunteer' || $user?->team?->unique_key === 'editor') {
            $this->setFilter('author_filter', $user->id);
        }
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('started_at', 'desc');

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route('event.create'),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable(fn (Builder $query, string $direction) => $query->orderBy('events.id', $direction))
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(Thumb::class)->render()->with([
                    'media' => $row->getFirstMedia('cover'),
                ]))
                ->collapseOnMobile(),
            Column::make('Titre', 'title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable(fn (Builder $query, string $direction) => $query->orderBy('events.title', $direction))
                ->searchable(),
            Column::make('Thématique', 'thematic.title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->collapseOnTablet(),
            BooleanColumn::make('Activation', 'active')
                ->collapseOnMobile(),
            Column::make('Localisation')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.boolean-label-column')->with([
                        'status' => $row->place() ? true : false,
                        'label' => $row->place()?->name,
                    ])
                )
                ->collapseOnTablet(),
            Column::make('Auteur', 'author.username')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable()
                ->collapseOnTablet(),
            Column::make('Date début', 'started_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier'),
            Column::make('Date fin', 'ended_at')
                ->format(fn ($value, $row, Column $column) => $value?->setTimezone('Europe/Paris')->format('d/m/Y'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier'),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable(fn (Builder $query, string $direction) => $query->orderBy('events.created_at', $direction))
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable(fn (Builder $query, string $direction) => $query->orderBy('events.updated_at', $direction))
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'viewLink' => route('app.event.show', $row),
                        'editLink' => route('event.edit', $row),
                        'deleteLink' => route('event.destroy', $row),
                        'entryTitle' => $row->title,
                        'withParams' => collect(['edit']),
                    ])
                )->html(),
        ];
    }

    public function editWithParams(string $editUrl)
    {
        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to($editUrl . $previousParams);
    }

    public function filters(): array
    {
        $user = Auth::user();

        return [
            SelectFilter::make('Auteur', 'author_filter')
                ->options([
                    '' => 'Tous',
                    $user->id => $user->username,
                ])
                ->filter(function (Builder $builder, string $value) {
                    $builder->where('user_id', $value);
                }),
        ];
    }
}

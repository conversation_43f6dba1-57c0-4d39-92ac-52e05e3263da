<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Radio\RadioStation;
use App\View\Components\Admin\Media\Thumb;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;

class RadioStationsTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    protected $model = RadioStation::class;

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('position', 'asc');

        $this->setReorderEnabled()
            ->setDefaultReorderSort('position', 'asc')
            ->setReorderMethod('reorder');

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route('radio-station.create'),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('Position', 'position')
                ->collapseOnMobile(),
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(Thumb::class)->render()->with([
                    'media' => $row->getFirstMedia('cover'),
                ]))
                ->collapseOnMobile(),
            Column::make('ID Winmedia', 'winmedia_id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0'),
            Column::make('Nom', 'name')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            BooleanColumn::make('Activation', 'active')
                ->collapseOnMobile(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'editLink' => route('radio-station.edit', $row),
                        'deleteLink' => Auth::user()->can('access_with_team', 'admin') ? route('radio-station.destroy', $row) : null,
                        'entryTitle' => $row->name,
                    ])
                )->html(),
        ];
    }

    public function reorder($items): void
    {
        foreach ($items as $item) {
            RadioStation::find($item['value'])->update(['position' => (int) $item['order']]);
        }

        radioStations(true);
        lastRadioStationsLiveBroadcasts(true);

        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'L\'ordre des stations a bien été pris en compte.',
        ]);
    }
}

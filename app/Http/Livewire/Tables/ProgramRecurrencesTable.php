<?php

namespace App\Http\Livewire\Tables;

use App\Models\Radio\Program;
use App\Models\Radio\ProgramRecurrence;
use App\Models\Radio\RadioStation;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;

class ProgramRecurrencesTable extends DataTableComponent
{
    public Program $program;

    public function builder(): Builder
    {
        return ProgramRecurrence::query()
            ->with(['radioStations'])
            ->where('program_id', $this->program->id)
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('id', 'asc');

        $this->setSearchStatus(false);

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route('program.recurrence.create', [
                        'program' => $this->program,
                    ]),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Label', 'label')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable(),
            Column::make('Stations radio')
                ->label(function ($row, Column $column) {
                    $radioStations = $row->radioStations;

                    return $radioStations->count() === RadioStation::count()
                        ? 'Toutes'
                        : $radioStations->implode('name', ', ');
                }),
            Column::make('Mois', 'months')
                ->format(function ($value, $row, Column $column) {
                    if (! $value) {
                        return 'Tous';
                    }
                    $months = [];
                    foreach ($value as $month) {
                        $months[] = Str::title(Date::now()->month(0)->month($month)->getTranslatedMonthName());
                    }

                    return implode(', ', $months);
                })
                ->collapseOnMobile(),
            Column::make('Jours du mois', 'month_days')
                ->format(function ($value, $row, Column $column) {
                    if (! $value) {
                        return 'Tous';
                    }

                    return implode(', ', $value);
                })
                ->collapseOnMobile(),
            Column::make('Jours de la semaine', 'week_days')
                ->format(function ($value, $row, Column $column) {
                    if (! $value) {
                        return 'Tous';
                    }
                    $days = [];
                    foreach ($value as $weekDay) {
                        $days[] = Str::title(Date::now()->isoWeekday($weekDay)->getTranslatedDayName());
                    }

                    return implode(', ', $days);
                })
                ->collapseOnMobile(),
            Column::make('Heure locale', 'local_time')
                ->format(fn ($value, $row, Column $column) => $value->format('H:i'))
                ->collapseOnMobile(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'editLink' => route('program.recurrence.edit', $row),
                        'deleteLink' => route('program.recurrence.destroy', $row),
                        'entryTitle' => $row->label,
                    ])
                )->html(),
        ];
    }
}

<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Performers\Label;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;

class LabelsTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public function builder(): Builder
    {
        return Label::query()
            ->with(['songs', 'albums', 'performers', 'contentLocations'])
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('id', 'desc');

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Nom', 'name')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            Column::make('Localisation')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.boolean-label-column')->with([
                        'status' => $row->place() ? true : false,
                        'label' => $row->place()?->name,
                    ])
                )
                ->collapseOnMobile(),
            Column::make('Titres')
                ->label(function ($row, Column $column) {
                    $count = $row->songs->count();
                    $label = trans_choice('[0,1]:count titre|[2,*]:count titres', $count, compact('count'));

                    return '<a href="' . route(
                        'songs.index',
                        ['table[search]' => 'label:' . $row->name]
                    ) . '" title="' . $label . '">' . $label . '</a>';
                })
                ->html()
                ->collapseOnTablet(),
            Column::make('Albums')
                ->label(function ($row, Column $column) {
                    $count = $row->albums->count();
                    $label = trans_choice('[0,1]:count album|[2,*]:count albums', $count, compact('count'));

                    return '<a href="' . route(
                        'albums.index',
                        ['table[search]' => 'label:' . $row->name]
                    ) . '" title="' . $label . '">' . $label . '</a>';
                })
                ->html()
                ->collapseOnTablet(),
            Column::make('Artistes')
                ->label(function ($row, Column $column) {
                    $count = $row->performers->count();
                    $label = trans_choice('[0,1]:count artiste|[2,*]:count artistes', $count, compact('count'));

                    return '<a href="' . route(
                        'performers.index',
                        ['table[search]' => 'label:' . $row->name]
                    ) . '" title="' . $label . '">' . $label . '</a>';
                })
                ->html()
                ->collapseOnTablet(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'editLink' => route('label.edit', $row),
                        'entryTitle' => $row->name,
                        'withParams' => collect(['edit']),
                    ])
                )->html(),
        ];
    }

    public function editWithParams(string $editUrl)
    {
        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to($editUrl . $previousParams);
    }
}

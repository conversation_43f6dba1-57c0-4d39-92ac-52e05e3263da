<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Radio\Program;
use App\View\Components\Admin\Media\Thumb;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;

class SubProgramsAssociationTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public Program $mainProgram;

    public function builder(): Builder
    {
        return Program::query()
            ->with(['thematic'])
            ->where('main_program_id', null)
            ->where('id', '!=', $this->mainProgram->id)
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('updated_at', 'desc');

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(Thumb::class)->render()->with([
                    'media' => $row->getFirstMedia('cover'),
                ]))
                ->collapseOnMobile(),
            Column::make('Titre', 'title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            Column::make('Thématique')
                ->label(fn ($row, Column $column) => Str::limit($row->thematic->title, 25))
                ->collapseOnTablet(),
            Column::make('Durée')
                ->label(fn ($row, Column $column) => $row->human_readable_duration),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'viewLink' => route('app.program.show', $row),
                        'associateSubprogramLink' => route('programs.subprogram.link', ['mainProgram' => $this->mainProgram->id, 'program' => $row->id]),
                        'entryTitle' => $row->title,
                    ])
                )->html(),
        ];
    }
}

<?php

namespace App\Http\Livewire\Tables;

use App\Models\Brickables\CarouselBrickSlide;
use App\View\Components\Admin\Media\Thumb;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;

class CarouselBrickSlidesTable extends DataTableComponent
{
    public array $carouselBrick;

    public array $requestSource;

    public function builder(): Builder
    {
        return CarouselBrickSlide::query()
            ->where('brick_id', $this->carouselBrick['id']);
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('position', 'asc');

        $this->setReorderEnabled()
            ->setDefaultReorderSort('position', 'asc')
            ->setReorderMethod('reorder');

        $this->setSearchStatus(false);

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route(
                        'brick.carousel.slide.create',
                        [
                            'brick' => $this->carouselBrick['id'],
                            'admin_panel_url' => $this->requestSource['admin_panel_url'],
                        ]
                    ),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('Position', 'position'),
            Column::make('ID', 'id'),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(Thumb::class)->render()->with([
                    'media' => $row->getFirstMedia('images'),
                ])),
            Column::make('Label', 'label')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25)),
            BooleanColumn::make('Activation', 'active'),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'editLink' => route(
                            'brick.carousel.slide.edit',
                            [
                                'slide' => $row,
                                'admin_panel_url' => $this->requestSource['admin_panel_url'],
                            ]
                        ),
                        'deleteLink' => route('brick.carousel.slide.destroy', $row),
                        'entryTitle' => $row->label,
                    ])
                )->html(),
        ];
    }

    public function reorder($items): void
    {
        foreach ($items as $item) {
            CarouselBrickSlide::find($item['value'])->update(['position' => (int) $item['order']]);
        }

        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'L\'ordre des slides a bien été pris en compte.',
        ]);
    }
}

<?php

namespace App\Http\Livewire\Tables;

use App\Models\Logs\LogJoinUsFormMessage;
use Illuminate\Database\Eloquent\Builder;
use Parsedown;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;

class JoinUsFormLogsTable extends DataTableComponent
{
    protected $model = LogJoinUsFormMessage::class;

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('created_at', 'desc');

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Contact', 'data')
                ->format(fn ($value, $row, Column $column) => $this->formatContactData($value))
                ->html()
                ->searchable(fn (Builder $builder, string $searchTerm) => $builder->where(
                    'data',
                    config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                    '%' . strtolower($searchTerm) . '%'
                )),
            Column::make('Objet', 'data')
                ->format(fn ($value, $row, Column $column) => $this->formatObject($value))
                ->html(),
            Column::make('Message', 'data')
                ->format(fn ($value, $row, Column $column) => '<div style="max-width: 700px;">' .
                    (new Parsedown())->parse($value['message']) . '</div>')
                ->html()
                ->collapseOnMobile(),
            Column::make('Informations complémentaires', 'data')
                ->format(fn ($value, $row, Column $column) => $this->formatAdditionalInformation($value))
                ->html()
                ->collapseOnTablet(),
            Column::make('Pièces jointes')
                ->label(fn ($row, Column $column) => $this->formatAttachmentsDownloadLinks($row))
                ->html()
                ->collapseOnTablet(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier'),
        ];
    }

    protected function formatContactData(array $data): string
    {
        return 'Nom : <b>' . $data['first_name'] . ' ' . $data['last_name'] . '</b><br>'
            . 'Mail : <b>' . $data['email'] . '</b><br>'
            . ($data['phone_number'] ? 'Téléphone: <b>' . $data['phone_number'] . '</b><br>' : '')
            . 'Ville : <b>' . $data['city'] . '</b><br>'
            . 'Année de naissance : <b>' . $data['birth_year'] . '</b><br>';
    }

    protected function formatObject(array $data): string
    {
        return 'Demande : <b>' . $data['application_type'] . '</b><br>'
            . 'Mission : <b>' . $data['mission'] . '</b><br>'
            . 'Studio : <b>' . $data['studio'] . '</b>';
    }

    protected function formatAdditionalInformation(array $data): string
    {
        return 'Ecoute SUN: <b>' . $data['listening_sun'] . '</b><br>'
            . ($data['experiences']
                ? 'Expériences : <b>' . implode(', ', $data['experiences']) . '</b><br>'
                : '')
            . ($data['listening_types']
                ? 'Programmes audios écoutés : <b>' . implode(', ', $data['listening_types']) . '</b><br>'
                : '')
            . ($data['favorite_music_types']
                ? 'Genres favoris : <b>' . implode(', ', $data['favorite_music_types']) . '</b><br>'
                : '');
    }

    protected function formatAttachmentsDownloadLinks(LogJoinUsFormMessage $log): string
    {
        $attachments = $log->getMedia('attachments');
        $links = '';
        $isFirst = true;

        foreach ($attachments as $attachment) {
            if (! $isFirst) {
                $links .= '<br>';
            }
            $isFirst = false;
            $downloadRoute = route('contact.form.logs.attachment.download', $attachment);
            $fileName = $attachment->file_name;

            $links .= "<a href=\"$downloadRoute\" title=\"Télécharger $fileName\">$fileName</a>";
        }

        return $links;
    }
}

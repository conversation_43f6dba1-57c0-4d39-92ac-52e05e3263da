<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Performers\Member;
use App\Models\Performers\Performer;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;

class MembersTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public ?Performer $performer = null;

    public function builder(): Builder
    {
        if ($this->performer) {
            return Member::query()
                ->with(['performers'])
                ->whereHas('performers', function ($query) {
                    $query->where('performers.id', $this->performer->id);
                })
                ->select();
        } else {
            return Member::query()
                ->with(['performers'])
                ->select();
        }
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('id', 'desc');

        if (! $this->performer) {
            $this->setConfigurableAreas([
                'toolbar-right-end' => [
                    'livewire.datatables.create-button', [
                        'createLink' => route('member.create'),
                    ],
                ],
            ]);
        }

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Prénom', 'first_name')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            Column::make('Nom', 'last_name')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            Column::make('Surnom', 'nick_name')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            Column::make('Rôle')
                ->label(fn ($row, Column $column) => Str::limit($row->performers()->find($this->performer?->id)?->pivot->role, 25))
                ->hideIf(! $this->performer),
            Column::make('Artistes associés')
                ->label(function ($row, Column $column) {
                    $count = $row->performers->count();
                    $label = trans_choice('[0,1]:count artiste|[2,*]:count artistes', $count, compact('count'));

                    //return '<a href="#" title="' . $label . '">' . $label . '</a>';
                    return $label;
                })
                ->html()
                ->collapseOnMobile(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'editLink' => $this->performer
                            ? route('member.edit', ['performer' => $this->performer->id, 'member' => $row->id])
                            : route('member.edit', ['member' => $row->id]),
                        'deleteLink' => $this->performer ? null : route('member.destroy', $row),
                        'entryTitle' => $row->title,
                    ])
                )->html(),
        ];
    }
}

<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Radio\Program;
use App\View\Components\Admin\Media\Thumb;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;

class ProgramsTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public ?Program $mainProgram = null;

    public function builder(): Builder
    {
        return Program::query()
            ->with(['mainProgram', 'thematic', 'authors', 'mainProgram', 'contentLocations'])
            ->when($this->mainProgram, function ($query) {
                $query->where('main_program_id', $this->mainProgram->id);
            })
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id');

        if ($this->mainProgram) {
            $this->setDefaultSort('id', 'asc');
            $this->setSearchStatus(false);
            if (Auth::user()->can('access_with_team', 'admin')) {
                $this->setConfigurableAreas([
                    'toolbar-right-start' => [
                        'livewire.datatables.associate-button', [
                            'associateSubprogramLink' => route('programs.subprogram.association', ['mainProgram' => $this->mainProgram]),
                        ],
                    ],
                    'toolbar-right-end' => [
                        'livewire.datatables.create-button', [
                            'createLink' => route('program.create', $this->mainProgram->id),
                        ],
                    ],
                ]);
            } else {
                $this->setConfigurableAreas([
                    'toolbar-right-end' => [
                        'livewire.datatables.create-button', [
                            'createLink' => route('program.create', $this->mainProgram->id),
                        ],
                    ],
                ]);
            }
        } else {
            $this->setDefaultSort('updated_at', 'desc');
            $this->setConfigurableAreas([
                'toolbar-right-end' => [
                    'livewire.datatables.create-button', [
                        'createLink' => route('program.create'),
                    ],
                ],
            ]);
        }

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(Thumb::class)->render()->with([
                    'media' => $row->getFirstMedia('cover'),
                ]))
                ->collapseOnMobile(),
            Column::make('Titre', 'title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                // Recherche "custom" combinée dans "Auteurs" donc non activée ici
                //->searchable()
                ->sortable(),
            Column::make('Émission parente')
                ->label(fn ($row, Column $column) => $row->mainProgram?->title)
                // Recherche "custom" combinée dans "Auteurs" donc non activée ici
                //->searchable()
                ->collapseOnTablet()
                ->hideIf($this->mainProgram !== null),
            Column::make('Localisation')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.boolean-label-column')->with([
                        'status' => $row->place() ? true : false,
                        'label' => $row->place()?->name,
                    ])
                )
                ->collapseOnTablet(),
            Column::make('Thématique')
                ->label(fn ($row, Column $column) => Str::limit($row->thematic->title, 25))
                ->collapseOnTablet(),
            Column::make('Durée')
                ->label(fn ($row, Column $column) => $row->human_readable_duration),
            Column::make('Auteurs')
                ->label(fn ($row, Column $column) => $row->authors->pluck('username')->implode(', '))
                ->searchable(fn (Builder $builder, string $searchTerm) => $builder
                    ->where(
                        'programs.title',
                        config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                        '%' . $searchTerm . '%'
                    )
                    ->orWhereHas('authors', function ($query) use ($searchTerm) {
                        $query->where(
                            'username',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        );
                    })
                    ->orWhereHas('mainProgram', function ($query) use ($searchTerm) {
                        $query->where(
                            'title',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        );
                    })
                )
                ->collapseOnTablet(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'viewLink' => route('app.program.show', $row),
                        'editLink' => route('program.edit', $row),
                        'deleteLink' => (! $this->mainProgram && Auth::user()->can('access_with_team', 'admin'))
                            ? route('program.destroy', $row)
                            : null,
                        'dissociateSubprogramLink' => ($this->mainProgram && Auth::user()->can('access_with_team', 'admin'))
                            ? route('programs.subprogram.unlink', ['mainProgram' => $this->mainProgram->id, 'program' => $row->id])
                            : null,
                        'entryTitle' => $row->title,
                    ])
                )->html(),
        ];
    }
}

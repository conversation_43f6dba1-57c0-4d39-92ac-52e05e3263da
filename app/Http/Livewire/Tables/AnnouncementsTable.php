<?php

namespace App\Http\Livewire\Tables;

use App\Models\Announcements\Announcement;
use App\Models\Radio\RadioStation;
use App\View\Components\Admin\Media\Thumb;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Filters\SelectFilter;

class AnnouncementsTable extends DataTableComponent
{
    public function builder(): Builder
    {
        return Announcement::query()
            ->with(['radioStations', 'announceable'])
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('published_at', 'desc');

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route('announcement.create'),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->setTrAttributes(fn ($row, $index) => $row->trashed() ? ['class' => 'table-danger'] : []);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(Thumb::class)->render()->with([
                    'media' => $row->displayable_media,
                ]))
                ->collapseOnMobile(),
            Column::make('Titre', 'title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                // Recherche "custom" combinée dans "Élément source annonce" donc non activée ici
                //->searchable()
                ->collapseOnMobile(),
            Column::make('Type source annonce', 'announceable_type')
                ->format(fn ($value, $row, Column $column) => Announcement::ANNOUNCEABLES[$value])
                ->collapseOnTablet(),
            Column::make('Élément source annonce')
                ->label(fn ($row, Column $column) => Str::limit($row->announceable->title, 25))
                ->searchable(fn (Builder $builder, string $searchTerm) => $builder
                    ->whereHas('announceable', function ($query) use ($searchTerm) {
                        $query->where(
                            'title',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        );
                    })->orWhere(
                        'title',
                        config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                        '%' . $searchTerm . '%'
                    )
                ),
            Column::make('Stations radio')
                ->label(fn ($row, Column $column) => $row->radioStations()->pluck('name')->implode(', '))
                ->collapseOnTablet(),
            Column::make('Activation', 'active')
                ->format(
                    fn ($value, $row, Column $column) => view('livewire.datatables.toggle-active-column')->with([
                        'id' => $row->id,
                        'isActive' => $row->active,
                    ])
                )
                ->html()
                ->collapseOnMobile(),
            Column::make('Date publication', 'published_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier'),
            Column::make('Date dépublication', 'unpublished_at')
                ->format(fn ($value, $row, Column $column) => $value?->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'editLink' => ! $row->trashed() ? route('announcement.edit', $row) : null,
                        'restoreLink' => ($row->trashed() && Auth::user()->can('access_with_team', 'admin')) ? route('announcement.restore', $row->id) : null,
                        'deleteLink' => $row->trashed()
                            ? (Auth::user()->can('access_with_team', 'admin') ? route('announcement.forceDestroy', $row->id) : null)
                            : route('announcement.destroy', $row),
                        'deleteTextConfirm' => $row->trashed() ? __('Are you sure you want to delete definitely from the database the entry :entry ?', [
                            'entry' => $row->announceable->title,
                        ]) : null,
                        'entryTitle' => $row->announceable->title,
                        'withParams' => collect(['edit']),
                    ])
                )->html(),
        ];
    }

    public function filters(): array
    {
        $defaultOption = ['' => 'Toutes'];
        $radioStationOptions = RadioStation::orderBy('winmedia_id')->pluck('name', 'id')->toArray();

        $filters = [
            SelectFilter::make('Catégorie')
                ->options($defaultOption + $radioStationOptions)
                ->filter(function (Builder $builder, string $value) {
                    $builder->whereHas('radioStations', function ($query) use ($value) {
                        $query->where('id', $value);
                    })
                        ->orWhereDoesntHave('radioStations');
                }),
        ];

        if (Auth::user()->can('access_with_team', 'admin')) {
            $filters[] = SelectFilter::make('Statut', 'trashed')
                ->options([
                    '' => 'Annonces en ligne',
                    'all' => 'Annonces en ligne et supprimées',
                    'deleted' => 'Annonces supprimées',
                ])
                ->filter(function (Builder $builder, string $value) {
                    switch ($value) {
                        case '':
                            break;
                        case 'all':
                            // @phpstan-ignore-next-line
                            $builder->withTrashed();
                            break;
                        case 'deleted':
                            // @phpstan-ignore-next-line
                            $builder->onlyTrashed();
                            break;
                    }
                });
        }

        return $filters;

    }

    public function editWithParams(string $editUrl)
    {
        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to($editUrl . $previousParams);
    }

    public function updateActive(Announcement $announcement, bool $value)
    {
        $announcement->active = $value;
        $announcement->save();

        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'La ligne #' . $announcement->id . ' a bien été ' . ($value ? 'activée.' : 'désactivée.'),
        ]);
    }
}

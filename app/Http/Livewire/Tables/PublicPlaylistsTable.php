<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Audio\Playlist;
use App\View\Components\Admin\Media\Thumb;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;

class PublicPlaylistsTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public function builder(): Builder
    {
        return Playlist::query()
            ->with(['thematic', 'contentLocations'])
            ->where('user_id', null)
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('updated_at', 'desc');

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route('playlist.create'),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(Thumb::class)->render()->with([
                    'media' => $row->getFirstMedia('cover'),
                ]))
                ->collapseOnMobile(),
            Column::make('Titre', 'title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            Column::make('Thématique')
                ->label(fn ($row, Column $column) => Str::limit($row->thematic->title, 25))
                ->collapseOnTablet(),
            Column::make('Date publication', 'published_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier'),
            Column::make('Date dépublication', 'unpublished_at')
                ->format(fn ($value, $row, Column $column) => $value?->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            BooleanColumn::make('Affichage rail', 'rail_displayed')
                ->collapseOnMobile(),
            BooleanColumn::make('Activation', 'active')
                ->collapseOnMobile(),
            Column::make('Localisation')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.boolean-label-column')->with([
                        'status' => $row->place() ? true : false,
                        'label' => $row->place()?->name,
                    ])
                )
                ->collapseOnTablet(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'viewLink' => route('app.playlist.show', $row),
                        'editLink' => route('playlist.edit', $row),
                        'deleteLink' => route('playlist.destroy', $row),
                        'entryTitle' => $row->title,
                        'withParams' => collect(['edit']),
                    ])
                )->html(),
        ];
    }

    public function editWithParams(string $editUrl)
    {
        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to($editUrl . $previousParams);
    }
}

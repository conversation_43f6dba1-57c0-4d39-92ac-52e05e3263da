<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Performers\Performer;
use App\View\Components\Admin\Media\ThumbUrl;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;
use Rappasoft\LaravelLivewireTables\Views\Filters\SelectFilter;

class PerformersTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public function builder(): Builder
    {
        return Performer::query()
            ->with(['songs', 'albums'])
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('id', 'desc');

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(ThumbUrl::class)->render()->with([
                    'url' => $row->coverThumb,
                ]))
                ->collapseOnMobile(),
            Column::make('Nom', 'name')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(function (Builder $builder, string $searchTerm) {
                    if (Str::startsWith($searchTerm, 'label:')) {
                        return $builder
                            ->whereHas('labels', function ($query) use ($searchTerm) {
                                $query->where(
                                    'labels.name',
                                    config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                                    Str::replaceFirst('label:', '', $searchTerm)
                                );
                            });
                    }

                    return $builder
                        ->where(
                            'performers.name',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        );
                }
                ),
            Column::make('Albums')
                ->label(function ($row, Column $column) {
                    $count = $row->albums->count();
                    $label = trans_choice('[0,1]:count album|[2,*]:count albums', $count, compact('count'));

                    return '<a href="' . route(
                        'albums.index',
                        ['table[search]' => 'artiste:' . $row->name]
                    ) . '" title="' . $label . '">' . $label . '</a>';
                })
                ->html()
                ->collapseOnMobile(),
            Column::make('Titres')
                ->label(function ($row, Column $column) {
                    $count = $row->songs->count();
                    $label = trans_choice('[0,1]:count titre|[2,*]:count titres', $count, compact('count'));

                    return '<a href="' . route(
                        'songs.index',
                        ['table[search]' => 'artiste:' . $row->name]
                    ) . '" title="' . $label . '">' . $label . '</a>';
                })
                ->html()
                ->collapseOnMobile(),
            BooleanColumn::make('Archivage', 'id')
                ->setCallback(fn (string $value, $row) => $row->songs->pluck('imedia')->contains(null))
                ->collapseOnTablet(),
            BooleanColumn::make('Winmedia', 'id')
                ->setCallback(fn (string $value, $row) => $row->songs->pluck('imedia')->contains(fn ($imedia) => ! is_null($imedia)))
                ->collapseOnTablet(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'viewLink' => route('app.performer.show', $row),
                        'editLink' => route('performer.edit', $row),
                        'entryTitle' => $row->name,
                        'withParams' => collect(['edit']),
                    ])
                )->html(),
        ];
    }

    public function editWithParams(string $editUrl)
    {
        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to($editUrl . $previousParams);
    }

    public function filters(): array
    {
        return [
            SelectFilter::make('Type musiques', 'songs_type')
                ->options([
                    '' => 'Tous',
                    'archivage' => 'Archivage',
                    'winmedia' => 'Winmedia',
                ])
                ->filter(function (Builder $builder, string $value) {
                    $builder->whereHas('songs', function ($query) use ($value) {
                        switch ($value) {
                            case 'archivage':
                                $query->where('imedia', null);
                                break;
                            case 'winmedia':
                                $query->where('imedia', '!=', null);
                                break;
                        }
                    });
                }),
        ];
    }
}

<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Performers\Album;
use App\View\Components\Admin\Media\ThumbUrl;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;
use Rappasoft\LaravelLivewireTables\Views\Filters\SelectFilter;

class AlbumsTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public function builder(): Builder
    {
        return Album::query()
            ->with(['performer', 'songs', 'contentLocations'])
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('id', 'desc');

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(ThumbUrl::class)->render()->with([
                    'url' => $row->coverThumb,
                ]))
                ->collapseOnMobile(),
            Column::make('Nom', 'name')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable(),
            // Recherche "custom" combinée dans "Artiste" donc non activée ici
            //->searchable(),
            Column::make('Artiste')
                ->label(function ($row, Column $column) {
                    return Str::limit($row->performer->name, 25);
                })
                ->html()
                ->collapseOnMobile()
                ->searchable(function (Builder $builder, string $searchTerm) {
                    if (Str::startsWith($searchTerm, 'artiste:')) {
                        return $builder
                            ->whereHas('performer', function ($query) use ($searchTerm) {
                                $query->where(
                                    'name',
                                    config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                                    Str::replaceFirst('artiste:', '', $searchTerm)
                                );
                            });
                    } elseif (Str::startsWith($searchTerm, 'label:')) {
                        return $builder
                            ->whereHas('label', function ($query) use ($searchTerm) {
                                $query->where(
                                    'name',
                                    config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                                    Str::replaceFirst('label:', '', $searchTerm)
                                );
                            });
                    }

                    return $builder
                        ->where(
                            'albums.name',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        )
                        ->orWhereHas('performer', function ($query) use ($searchTerm) {
                            $query->where(
                                'name',
                                config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                                '%' . $searchTerm . '%'
                            );
                        });
                }
                ),
            Column::make('Titres')
                ->label(function ($row, Column $column) {
                    $count = $row->songs->count();
                    $label = trans_choice('[0,1]:count titre|[2,*]:count titres', $count, compact('count'));

                    return '<a href="' . route(
                        'songs.index',
                        ['table[search]' => 'album:' . $row->name]
                    ) . '" title="' . $label . '">' . $label . '</a>';
                })
                ->html()
                ->collapseOnMobile(),
            Column::make('Localisation')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.boolean-label-column')->with([
                        'status' => $row->place() ? true : false,
                        'label' => $row->place()?->name,
                    ])
                )
                ->collapseOnTablet(),
            BooleanColumn::make('Archivage', 'id')
                ->setCallback(fn (string $value, $row) => $row->songs->pluck('imedia')->contains(null))
                ->collapseOnTablet(),
            BooleanColumn::make('Winmedia', 'id')
                ->setCallback(fn (string $value, $row) => $row->songs->pluck('imedia')->contains(fn ($imedia) => ! is_null($imedia)))
                ->collapseOnTablet(),
            Column::make('Date de sortie', 'published_at')
                ->format(fn ($value, $row, Column $column) => $value ? $value->setTimezone('Europe/Paris')->format('d/m/Y') : null)
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'viewLink' => route('app.album.show', $row),
                        'editLink' => route('album.edit', $row),
                        'entryTitle' => $row->name,
                        'withParams' => collect(['edit']),
                    ])
                )->html(),
        ];
    }

    public function editWithParams(string $editUrl)
    {
        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to($editUrl . $previousParams);
    }

    public function filters(): array
    {
        return [
            SelectFilter::make('Type musiques', 'songs_type')
                ->options([
                    '' => 'Tous',
                    'archivage' => 'Archivage',
                    'winmedia' => 'Winmedia',
                ])
                ->filter(function (Builder $builder, string $value) {
                    $builder->whereHas('songs', function ($query) use ($value) {
                        switch ($value) {
                            case 'archivage':
                                $query->where('imedia', null);
                                break;
                            case 'winmedia':
                                $query->where('imedia', '!=', null);
                                break;
                        }
                    });
                }),
        ];
    }
}

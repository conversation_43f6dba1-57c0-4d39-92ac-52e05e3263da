<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Audio\Song;
use App\View\Components\Admin\Media\ThumbUrl;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;

class SongsTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public function builder(): Builder
    {
        return Song::query()
            //->with(['performer', 'album'])
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('id', 'desc');

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Imedia', 'imedia')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(ThumbUrl::class)->render()->with([
                    'url' => $row->coverThumb,
                ]))
                ->collapseOnMobile(),
            Column::make('Artiste', 'performer')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(function (Builder $builder, string $searchTerm) {
                    if (Str::startsWith($searchTerm, 'artiste:')) {
                        return $builder
                            ->where(
                                'performer',
                                config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                                Str::replaceFirst('artiste:', '', $searchTerm)
                            );
                    } elseif (Str::startsWith($searchTerm, 'album:')) {
                        return $builder
                            ->where(
                                'album',
                                config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                                Str::replaceFirst('album:', '', $searchTerm)
                            );
                    } elseif (Str::startsWith($searchTerm, 'label:')) {
                        return $builder
                            ->where(
                                'publisher',
                                config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                                Str::replaceFirst('label:', '', $searchTerm)
                            );
                    }

                    return $builder
                        ->where(
                            'performer',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        )
                        ->orWhere(
                            'title',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        )
                        ->orWhere(
                            'version',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        )
                        ->orWhere(
                            'album',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        );
                }
                ),
            Column::make('Titre', 'title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable(),
            // Recherche "custom" combinée dans "Artiste" donc non activée ici
            //->searchable(),
            Column::make('Version', 'version')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable(),
            // Recherche "custom" combinée dans "Artiste" donc non activée ici
            //->searchable(),
            Column::make('Album', 'album')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable(),
            // Recherche "custom" combinée dans "Artiste" donc non activée ici
            //->searchable(),
            BooleanColumn::make('Présence dans Winmedia', 'imedia'),
            Column::make('Année', 'year')
                ->collapseOnTablet(),
            Column::make('Date de sortie', '_release_date')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'editLink' => $row->imedia ? null : route('song.edit', $row),
                        'deleteLink' => (Auth::user()->can('access_with_team', 'admin') && ! $row->imedia)
                            ? route('song.destroy', $row)
                            : null,
                        'entryTitle' => $row->title,
                        'withParams' => collect(['edit']),
                    ])
                )->html(),
        ];
    }

    public function editWithParams(string $editUrl)
    {
        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to($editUrl . $previousParams);
    }
}

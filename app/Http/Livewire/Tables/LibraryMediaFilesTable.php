<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\LibraryMedia\LibraryMediaCategory;
use App\Models\LibraryMedia\LibraryMediaFile;
use App\View\Components\Admin\LibraryMedia\ClipboardCopy\Buttons;
use App\View\Components\Admin\LibraryMedia\Thumb;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Filters\SelectFilter;

class LibraryMediaFilesTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public function builder(): Builder
    {
        return LibraryMediaFile::query()
            ->with(['category'])
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('updated_at', 'desc');

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route('libraryMedia.file.create'),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(Thumb::class)->render()->with([
                    'file' => $row,
                ])),
            Column::make('Nom', 'name')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            Column::make('Catégorie')
                ->label(fn ($row, Column $column) => $row->category->title)
                ->html()
                ->collapseOnMobile(),
            Column::make(__('MIME types'))
                ->label(fn ($row, Column $column) => $row->getFirstMedia('media')?->mime_type)
                ->html()
                ->collapseOnMobile(),
            Column::make('Copie presse-papier')
                ->label(fn ($row, Column $column) => app(Buttons::class)->render()->with([
                    'file' => $row,
                ]))
                ->html()
                ->collapseOnTablet(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'editLink' => route('libraryMedia.file.edit', $row),
                        'deleteLink' => route('libraryMedia.file.destroy', $row),
                        'entryTitle' => $row->name,
                    ])
                )->html(),
        ];
    }

    public function filters(): array
    {
        $defaultOption = ['' => 'Toutes'];
        $libraryMediaCategoryOptions = LibraryMediaCategory::orderBy('title')->pluck('title', 'id')->toArray();

        return [
            SelectFilter::make('Catégorie')
                ->options($defaultOption + $libraryMediaCategoryOptions)
                ->filter(function (Builder $builder, string $value) {
                    $builder->where('category_id', $value);
                }),
        ];
    }
}

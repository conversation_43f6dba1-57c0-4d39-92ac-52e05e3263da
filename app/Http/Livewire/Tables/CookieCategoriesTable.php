<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Cookies\CookieCategory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;

class CookieCategoriesTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public function builder(): Builder
    {
        return CookieCategory::query()
            ->with(['services'])
            ->select();
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('position', 'asc');

        $this->setReorderEnabled()
            ->setDefaultReorderSort('position', 'asc')
            ->setReorderMethod('reorder');

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route('cookie.category.create'),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('Position', 'position')
                ->collapseOnMobile(),
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->html(),
            Column::make('Identifiant unique', 'unique_key')
                ->sortable()
                ->searchable()
                ->collapseOnMobile(),
            Column::make('Titre', 'title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            Column::make(__('Associated services'))
                ->label(function ($row, Column $column) {
                    $count = $row->services->count();
                    $label = trans_choice('[0,1]:count service|[2,*]:count services', $count, compact('count'));

                    return '<a href="' . route(
                        'cookie.services.index',
                        ['table[filters][services_associés]' => $row->id]
                    ) . '" title="' . $label . '">' . $label . '</a>';
                })
                ->html()
                ->collapseOnMobile(),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'editLink' => route('cookie.category.edit', $row),
                        'deleteLink' => route('cookie.category.destroy', $row),
                        'entryTitle' => $row->title,
                    ])
                )->html(),
        ];
    }

    public function reorder($items): void
    {
        foreach ($items as $item) {
            CookieCategory::find($item['value'])->update(['position' => (int) $item['order']]);
        }

        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'L\'ordre des categories de cookies a bien été pris en compte.',
        ]);
    }
}

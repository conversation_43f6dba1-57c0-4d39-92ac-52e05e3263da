<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Audio\Podcast;
use App\View\Components\Admin\Media\Thumb;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;
use Rappasoft\LaravelLivewireTables\Views\Filters\SelectFilter;

class PodcastsTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    public function builder(): Builder
    {
        return Podcast::query()
            ->with(['media', 'authors', 'contentLocations'])
            ->select();
    }

    public function mount()
    {
        $user = Auth::user();
        if ($user?->team?->unique_key === 'volunteer' || $user?->team?->unique_key === 'editor') {
            $this->setFilter('author_filter', $user->id);
        }
    }

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('published_at', 'desc');

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route('podcast.create'),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable(
                    fn (Builder $query, string $direction) => $query->orderBy('podcasts.id', $direction)
                )
                ->setSortingPillDirections('0-9', '9-0')
                ->collapseOnMobile()
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Vignette')
                ->label(fn ($row, Column $column) => app(Thumb::class)->render()->with([
                    'media' => $row->getFirstMedia('cover'),
                ]))
                ->collapseOnMobile(),
            Column::make('Titre', 'title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable(
                    fn (Builder $query, string $direction) => $query->orderBy('podcasts.title', $direction)
                ),
            // Recherche "custom" combinée dans "Auteurs" donc non activée ici
            //->searchable(),
            Column::make('Émission', 'program.title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                // Recherche "custom" combinée dans "Auteurs" donc non activée ici
                //->searchable()
                ->collapseOnMobile(),
            Column::make('Thématique', 'thematic.title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->collapseOnTablet(),
            Column::make('Type', 'type')
                ->format(fn ($value, $row, Column $column) => Podcast::TYPES[$value])
                ->collapseOnTablet(),
            BooleanColumn::make('Fichier audio', 'id')
                ->setCallback(fn (string $value, $row) => (bool) $row->audio_stream['mp3']),
            BooleanColumn::make('Activation', 'active')
                ->collapseOnTablet(),
            Column::make('Localisation')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.boolean-label-column')->with([
                        'status' => $row->place() ? true : false,
                        'label' => $row->place()?->name,
                    ])
                )
                ->collapseOnTablet(),
            Column::make('Auteurs')
                ->label(fn ($row, Column $column) => $row->authors->pluck('username')->implode(', '))
                ->searchable(fn (Builder $builder, string $searchTerm) => $builder
                    ->where(
                        'podcasts.title',
                        config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                        '%' . $searchTerm . '%'
                    )
                    ->orWhereHas('program', function ($query) use ($searchTerm) {
                        $query->where(
                            'title',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        );
                    })
                    ->orWhereHas('authors', function ($query) use ($searchTerm) {
                        $query->where(
                            'username',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%' . $searchTerm . '%'
                        );
                    })
                )
                ->collapseOnTablet(),
            Column::make('Date publication', 'published_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier'),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable(
                    fn (Builder $query, string $direction) => $query->orderBy('podcasts.created_at', $direction)
                )
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet()
                ->hideIf(! Auth::user()->can('access_with_team', 'admin')),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable(
                    fn (Builder $query, string $direction) => $query->orderBy('podcasts.updated_at', $direction)
                )
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet()
                ->hideIf(! Auth::user()->can('access_with_team', 'admin')),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'viewLink' => route('app.podcast.show', $row),
                        'editLink' => route('podcast.edit', $row),
                        'deleteLink' => route('podcast.destroy', $row),
                        'entryTitle' => $row->title,
                        'withParams' => collect(['edit']),
                    ])
                )->html(),
        ];
    }

    public function editWithParams(string $editUrl)
    {
        $previousParams = isset(parse_url(request()->server('HTTP_REFERER'))['query']) ? ('?' . parse_url(request()->server('HTTP_REFERER'))['query']) : '';

        return redirect()->to($editUrl . $previousParams);
    }

    public function filters(): array
    {
        $user = Auth::user();

        return [
            SelectFilter::make('Auteur', 'author_filter')
                ->options([
                    '' => 'Tous',
                    $user->id => $user->username,
                ])
                ->filter(function (Builder $builder, string $value) {
                    $builder->whereHas('authors', function ($query) use ($value) {
                        $query->where('user_id', $value);
                    });
                }),
        ];
    }
}

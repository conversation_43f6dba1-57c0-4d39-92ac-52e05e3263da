<?php

namespace App\Http\Livewire\Tables;

use App\Http\Livewire\Traits\WithCustomSearchLivewireTable;
use App\Models\Pages\Page;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Rappasoft\LaravelLivewireTables\DataTableComponent;
use Rappasoft\LaravelLivewireTables\Views\Column;
use Rappasoft\LaravelLivewireTables\Views\Columns\BooleanColumn;

class PagesTable extends DataTableComponent
{
    use WithCustomSearchLivewireTable;

    protected $model = Page::class;

    public function configure(): void
    {
        $this->setPrimaryKey('id')
            ->setDefaultSort('updated_at', 'desc');

        $this->setConfigurableAreas([
            'toolbar-right-end' => [
                'livewire.datatables.create-button', [
                    'createLink' => route('page.create'),
                ],
            ],
        ]);

        $this->setTableAttributes([
            'default' => false,
            'class' => 'table align-middle',
        ]);
        $this->setTheadAttributes([
            'default' => false,
            'class' => 'table-light border-top border-bottom',
        ]);

        $this->emit('livewire:table:refresh');
    }

    public function columns(): array
    {
        return [
            Column::make('ID', 'id')
                ->sortable()
                ->setSortingPillDirections('0-9', '9-0')
                ->format(fn ($value, $row, Column $column) => '<strong>' . $value . '</strong>')
                ->html(),
            Column::make('Identifiant unique', 'unique_key')
                ->sortable()
                ->searchable()
                ->collapseOnMobile(),
            Column::make('Titre navigation', 'nav_title')
                ->format(fn ($value, $row, Column $column) => Str::limit($value, 25))
                ->sortable()
                ->searchable(),
            Column::make('URL', 'slug')
                ->format(fn ($value, $row, Column $column) => '/page/' . $value)
                ->searchable()
                ->collapseOnTablet(),
            BooleanColumn::make('Activation', 'active'),
            Column::make('Date création', 'created_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Date modification', 'updated_at')
                ->format(fn ($value, $row, Column $column) => $value->setTimezone('Europe/Paris')->format('d/m/Y H:i'))
                ->sortable()
                ->setSortingPillDirections('Plus ancien en premier', 'Plus récent en premier')
                ->collapseOnTablet(),
            Column::make('Actions')
                ->label(
                    fn ($row, Column $column) => view('livewire.datatables.actions-column')->with([
                        'viewLink' => route('page.show', $row),
                        'editLink' => route('page.edit', $row),
                        'deleteLink' => Auth::user()->can('access_with_team', 'admin') ? route('page.destroy', $row) : null,
                        'entryTitle' => $row->title,
                    ])
                )->html(),
        ];
    }
}

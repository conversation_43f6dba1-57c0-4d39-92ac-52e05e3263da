<?php

namespace App\Http\Livewire\Share;

use Livewire\Component;

class CopyLink extends Component
{
    public string $link;

    public string $linkTitle;

    public function toastSuccess(): void
    {
        $this->dispatchBrowserEvent('toast:success', [
            'title' => 'Le lien a été copié.',
        ]);
    }

    public function render()
    {
        return view('livewire.share.copy-link');
    }
}

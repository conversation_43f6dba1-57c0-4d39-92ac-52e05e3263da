<?php

namespace App\Http\Livewire\Places;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\Map\Place;
use App\Models\News\NewsArticle;
use App\Models\Performers\Album;
use App\Models\Performers\Performer;
use App\Models\Radio\Program;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Livewire\Component;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class Details extends Component
{
    public bool $initialized = false;

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllAudioSources = false;

    public array $selectedAudioSourceIds = [];

    public Place $place;

    public Collection $newsArticles;

    public Collection $programs;

    public Collection $podcasts;

    public Collection $playlists;

    public Collection $pastEvents;

    public Collection $nextEvents;

    public Collection $events;

    public Collection $performers;

    public Collection $albums;

    public bool $hasCurrentEvent = false;

    public ?float $placeLongitude = null;

    public ?float $placeLatitude = null;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    public function mount(): void
    {
        $this->newsArticles = collect();
        $this->programs = collect();
        $this->podcasts = collect();
        $this->playlists = collect();
        $this->pastEvents = collect();
        $this->nextEvents = collect();
        $this->events = collect();
        $this->performers = collect();
        $this->albums = collect();
    }

    public function init(): void
    {
        if ($this->place->currentPoint) {
            $this->placeLongitude = $this->place->currentPoint->getLongitude();
            $this->placeLatitude = $this->place->currentPoint->getLatitude();
        }
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'place_details', routeParams: ['place' => $this->place], livewireComponent: $this);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->initialized = true;
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function render(): View
    {
        if ($this->initialized) {
            $placeId = $this->place->id;

            $this->emit('rail:load', ['railName' => 'place-detail-news-rail']);
            $this->emit('rail:load', ['railName' => 'place-detail-programs-rail']);
            $this->emit('rail:load', ['railName' => 'place-detail-podcasts-rail']);
            $this->emit('rail:load', ['railName' => 'place-detail-playlists-rail']);
            $this->emit('rail:load', ['railName' => 'place-detail-events-rail']);
            $this->emit('rail:load', ['railName' => 'place-detail-performers-rail']);
            $this->emit('rail:load', ['railName' => 'place-detail-albums-rail']);

            $this->setHasCurrentEvent($placeId);
            $this->newsArticles = $this->getNewsArticles($placeId);
            $this->programs = $this->getPrograms($placeId);
            $this->podcasts = $this->getPodcasts($placeId);
            $this->playlists = $this->getPlaylists($placeId);
            $this->pastEvents = $this->getPastEvents($placeId);
            $this->nextEvents = $this->getNextEvents($placeId);
            $this->events = $this->nextEvents->merge($this->pastEvents)->take(10);
            $this->performers = $this->getPerformers($placeId);
            $this->albums = $this->getAlbums($placeId);

            $this->updateAudioSourceStatuses('newsArticles');
            $this->updateAudioSourceStatuses('podcasts');
            $this->updateAudioSourceStatuses('playlists');
            $this->updateAudioSourceStatuses('events');
            $this->updateAudioSourceStatuses('albums');
        }

        return view('livewire.places.details');
    }

    protected function getNewsArticles(int $placeId): Collection
    {
        return NewsArticle::with(['media'])
            ->published()
            ->active()
            ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->orderByDesc('published_at')
            ->limit(12)
            ->get();
    }

    protected function getNextEvents(int $placeId): Collection
    {
        return Event::with(['media'])
            ->active()
            ->where('ended_at', '>=', now()->subDay()->format('Y-m-d'))
            ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->orderByRaw(config('database.default') === 'pgsql'
                ? 'ABS((started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date - (now() AT TIME ZONE \'Europe/Paris\')::date)'
                : 'ABS(DATEDIFF(started_at, NOW()))')
            ->orderBy('started_at')
            ->limit(10)
            ->get();
    }

    protected function getPastEvents(int $placeId): Collection
    {
        return Event::with(['media'])
            ->active()
            ->where('ended_at', '<', now()->subDay()->format('Y-m-d'))
            ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->orderBy('started_at', 'DESC')
            ->orderBy('ended_at', 'DESC')
            ->limit(10)
            ->get();
    }

    protected function setHasCurrentEvent(int $placeId): void
    {
        $this->hasCurrentEvent = Event::with(['media'])
            ->active()
            ->where('started_at', '<=', now()->format('Y-m-d'))
            ->where('ended_at', '>=', now()->subDay()->format('Y-m-d'))
            ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->exists();
    }

    protected function getPrograms(int $placeId): Collection
    {
        $podcastConditions = function (Builder $query) {
            /** @phpstan-ignore-next-line */
            $query->hasAudio()
                ->active()
                ->where('published_at', '<=', Date::now());
        };

        return Program::with(['media'])
            ->withCount(['podcasts' => $podcastConditions])
            ->withMax(['podcasts' => $podcastConditions], 'published_at')
            ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->whereHas('podcasts', $podcastConditions)
            ->orderByRaw('podcasts_max_published_at DESC NULLS LAST')
            ->orderBy('title')
            ->limit(20)
            ->get();
    }

    protected function getPodcasts(int $placeId): Collection
    {
        return Podcast::with(['media', 'program'])
            ->active()
            ->hasAudio()
            ->where('published_at', '<=', Date::now())
            ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->orderByDesc('published_at')
            ->limit(15)
            ->get();
    }

    protected function getPlaylists(int $placeId): Collection
    {
        return Playlist::with(['media', 'songs'])
            ->active()
            ->public()
            ->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->orderByDesc('published_at')
            ->limit(15)
            ->get();
    }

    protected function getPerformers(int $placeId): Collection
    {
        $albumsSubquery = Album::select('performer_id')
            ->selectRaw('MAX(published_at) as max_published_at')
            ->selectRaw('DATE_TRUNC(\'minute\', MAX(created_at)) as max_created_at_truncated')
            ->selectRaw('MAX(created_at) as max_created_at')
            ->where(function (Builder $query) use ($placeId) {
                $query->whereHas('contentLocations', function (Builder $query) use ($placeId) {
                    $query->where('location_id', $placeId)
                        ->where('location_type', Place::class);
                })
                    ->orWhereHas('label', function (Builder $query) use ($placeId) {
                        $query->whereHas('contentLocations', function (Builder $query) use ($placeId) {
                            $query->where('location_id', $placeId)
                                ->where('location_type', Place::class);
                        });
                    });
            })
            ->where('compilation_id', null)
            ->groupBy('performer_id');

        return Performer::with(['albums', 'labels'])
            ->select('performers.id', 'performers.name')
            ->joinSub($albumsSubquery, 'albums_max', function ($join) {
                $join->on('performers.id', '=', 'albums_max.performer_id');
            })
            ->withCount('songs')
            ->orderByRaw('albums_max.max_published_at DESC NULLS LAST')
            ->orderByDesc('albums_max.max_created_at_truncated')
            ->orderByDesc('songs_count')
            ->orderByDesc('albums_max.max_created_at')
            ->limit(35)
            ->get();
    }

    protected function getAlbums(int $placeId): Collection
    {
        return Album::with(['contentLocations'])
            ->where(function (Builder $query) use ($placeId) {
                $query->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                    $query->where('location_id', $placeId)
                        ->where('location_type', Place::class);
                })
                    ->orWhereHas('label', function (Builder $query) use ($placeId): void {
                        $query->whereHas('contentLocations', function (Builder $query) use ($placeId): void {
                            $query->where('location_id', $placeId)
                                ->where('location_type', Place::class);
                        });
                    });
            })
            ->where(function (Builder $query) {
                $query->whereNull('compilation_id')
                    ->orWhereIn('id', function ($subquery) {
                        $subquery->select(\DB::raw('MIN(id)'))
                            ->from('albums')
                            ->whereNotNull('compilation_id')
                            ->groupBy('compilation_id');
                    });
            })
            ->orderByRaw('published_at DESC NULLS LAST')
            ->orderByDesc('created_at')
            ->limit(35)
            ->get();
    }

    protected function updateAudioSourceStatuses(string $type): void
    {
        if ($this->{$type}) {
            if ($this->pauseAllAudioSources) {
                $this->{$type} = $this->{$type}->map(function (NewsArticle|Event|Podcast|Playlist|Album $audioItem) {
                    $audioItem->selected = in_array($audioItem->id, $this->selectedAudioSourceIds, true);
                    $audioItem->playing = false;
                    $audioItem->dedicatable = false;

                    return $audioItem;
                });

                return;
            }
            $this->selectedAudioSourceIds = [];
            $this->{$type} = $this->{$type}->map(function (NewsArticle|Event|Podcast|Playlist|Album $audioItem) {
                $isPlaying = $audioItem::class === $this->playedAudioSourceClass
                    && $audioItem->id === $this->playedAudioSourceId;
                if ($isPlaying) {
                    $this->selectedAudioSourceIds[] = $audioItem->id;
                }
                $audioItem->selected = $isPlaying;
                $audioItem->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();
                $audioItem->dedicatable = false;

                return $audioItem;
            });
        }
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllAudioSources = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllAudioSources = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllAudioSources = false;
    }
}

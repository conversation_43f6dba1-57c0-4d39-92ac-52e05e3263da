<?php

namespace App\Http\Livewire\Events;

use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\News\NewsArticle;
use App\Services\Elasticsearch\ElasticsearchService;
use App\Services\Elasticsearch\SuggestedContentService;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Livewire\Component;

class Details extends Component
{
    public Event $event;

    public Collection $suggestedContents;

    public Collection $suggestedEvents;

    public bool $initialized = false;

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllAudioSources = false;

    public array $selectedAudioSourceIds = [];

    public bool $eventIsPlaying = false;

    public ?string $url = null;

    protected $listeners = [
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    public function mount(): void
    {
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->setPlayingStatus();
    }

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    protected function setPlayingStatus(): void
    {
        $this->eventIsPlaying = $this->playedAudioSourceClass === Event::class
            && $this->playedAudioSourceId === $this->event->id
            && app(UserJourneysService::class)->getPlayerPlayingStatus();
    }

    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(
            routeKey: 'event_details',
            routeParams: ['event' => $this->event],
            livewireComponent: $this
        );
        $this->initialized = true;
    }

    public function render(): View
    {
        if ($this->initialized && $this->event->active) {
            $this->emit('rail:load', ['railName' => 'events-details-suggested-contents']);
            $this->emit('rail:load', ['railName' => 'events-details-suggested-events-rail']);
            $this->setSuggestions();
            $this->updateSuggestedContentsStatuses();
        }

        return view('livewire.events.details');
    }

    protected function updateSuggestedContentsStatuses(): void
    {
        if ($this->pauseAllAudioSources) {
            $this->suggestedContents = $this->suggestedContents->map(function (Podcast|NewsArticle $contentItem) {
                $contentItem->selected = in_array($contentItem->id, $this->selectedAudioSourceIds, true);
                $contentItem->playing = false;

                return $contentItem;
            });
            $this->suggestedEvents = $this->suggestedEvents->map(function (Event $contentItem) {
                $contentItem->selected = in_array($contentItem->id, $this->selectedAudioSourceIds, true);
                $contentItem->playing = false;

                return $contentItem;
            });

            return;
        }
        $this->selectedAudioSourceIds = [];
        $this->suggestedContents = $this->suggestedContents->map(function (Podcast|NewsArticle $contentItem) {
            $isPlaying = $contentItem::class === $this->playedAudioSourceClass
                && $contentItem->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedAudioSourceIds[] = $contentItem->id;
            }
            $contentItem->selected = $isPlaying;
            $contentItem->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $contentItem;
        });
        $this->suggestedEvents = $this->suggestedEvents->map(function (Event $contentItem) {
            $isPlaying = $contentItem::class === $this->playedAudioSourceClass
                && $contentItem->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedAudioSourceIds[] = $contentItem->id;
            }
            $contentItem->selected = $isPlaying;
            $contentItem->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $contentItem;
        });
    }

    protected function setSuggestions(): void
    {
        $esSourceContent = app(ElasticsearchService::class)->searchSimpleById(
            config('services.elasticsearch.indexes.events'),
            $this->event->id
        );
        if ($esSourceContent['hits']['hits'][0]['_id'] ?? false) {
            $suggestedContents = app(SuggestedContentService::class)->getSuggestedNewsAndPodcasts(
                config('services.elasticsearch.indexes.events'),
                $esSourceContent['hits']['hits'][0]['_id']
            );
            $suggestedEvents = app(SuggestedContentService::class)->getSuggestedEvents(
                config('services.elasticsearch.indexes.events'),
                $esSourceContent['hits']['hits'][0]['_id']
            );
            $this->suggestedContents = $suggestedContents;
            $this->suggestedEvents = $suggestedEvents;
        }
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->setPlayingStatus();
        $this->pauseAllAudioSources = false;
    }

    public function playerHasPaused(): void
    {
        $this->eventIsPlaying = false;
        $this->pauseAllAudioSources = true;
    }

    public function playerIsResuming(): void
    {
        $this->setPlayingStatus();
        $this->pauseAllAudioSources = false;
    }
}

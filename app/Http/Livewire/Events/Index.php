<?php

namespace App\Http\Livewire\Events;

use App\Models\Events\Event;
use App\Services\Seo\SeoMetaService;
use App\Services\Users\UserJourneysService;
use Exception;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    public bool $initialized = false;

    public int $selectedRadioStationId;

    public ?string $url = null;

    public string $eventsToDisplay = Event::CURRENT;

    protected int $elementsPerPage = 21;

    public Collection $events;

    public array $selectedEventIds = [];

    public ?string $playedAudioSourceClass = null;

    public ?int $playedAudioSourceId = null;

    public bool $pauseAllEvents = false;

    protected $listeners = [
        'radio:station:universe:updated' => 'setSelectedRadioStationId',
        'player:audio:source:updated' => 'updatePlayedAudioSource',
        'player:audio:source:paused' => 'playerHasPaused',
        'player:audio:source:played' => 'playerIsResuming',
    ];

    protected function setPlayedAudioSource(?string $audioSourceClass, ?int $audioSourceId): void
    {
        $this->playedAudioSourceClass = $audioSourceClass;
        $this->playedAudioSourceId = $audioSourceId;
    }

    public function mount(): void
    {
        $this->setPage($this->page);
        $this->events = collect();
    }

    /** @throws \Exception */
    public function init(): void
    {
        app(SeoMetaService::class)->generateSeoMeta(routeKey: 'events', livewireComponent: $this);
        $selectedRadioStationId = app(UserJourneysService::class)->getSelectedRadioStationUniverseId();
        $this->setSelectedRadioStationId($selectedRadioStationId);
        [
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
        ] = app(UserJourneysService::class)->getPlayedAudioSource();
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->initialized = true;
    }

    public function setSelectedRadioStationId(int $selectedRadioStationId): void
    {
        $this->selectedRadioStationId = $selectedRadioStationId;
    }

    public function render(): View
    {
        $links = null;
        $months = null;

        if ($this->initialized) {
            switch ($this->eventsToDisplay) {
                case Event::PAST:
                    $events = Event::with(['contentLocations', 'media'])
                        ->where('ended_at', '<', now()->subDay()->format('Y-m-d'))
                        ->where(function (Builder $subWhereQuery) {
                            $subWhereQuery->whereRelation(
                                'radioStations',
                                fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStationId)
                            )->orWhereDoesntHave('radioStations');
                        })
                        ->where('active', true)
                        ->orderBy('started_at', 'DESC')
                        ->orderBy('ended_at', 'DESC')
                        ->paginate($this->elementsPerPage);
                    $months = $this->getMonths($events);
                    break;
                case Event::CURRENT:
                    $events = Event::with(['contentLocations', 'media'])
                        ->where('started_at', '<=', now()->format('Y-m-d'))
                        ->where('ended_at', '>=', now()->subDay()->format('Y-m-d'))
                        ->where(function (Builder $subWhereQuery) {
                            $subWhereQuery->whereRelation(
                                'radioStations',
                                fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStationId)
                            )->orWhereDoesntHave('radioStations');
                        })
                        ->where('active', true)
                        ->orderByRaw(config('database.default') === 'pgsql'
                            ? 'ABS((started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date - (now() AT TIME ZONE \'Europe/Paris\')::date)'
                            : 'ABS(DATEDIFF(started_at, NOW()))')
                        ->orderBy('started_at')
                        ->paginate($this->elementsPerPage);
                    $months = collect();
                    break;
                case Event::NEXT:
                    $events = Event::with(['contentLocations', 'media'])
                        ->where('started_at', '>', now()->format('Y-m-d'))
                        ->where(function (Builder $subWhereQuery) {
                            $subWhereQuery->whereRelation(
                                'radioStations',
                                fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $this->selectedRadioStationId)
                            )->orWhereDoesntHave('radioStations');
                        })
                        ->where('active', true)
                        ->orderBy('started_at', 'ASC')
                        ->orderBy('ended_at', 'ASC')
                        ->paginate($this->elementsPerPage);
                    $months = $this->getMonths($events);
                    break;
                default:
                    throw new Exception('Unrecognized ' . $this->eventsToDisplay . ' page selector.');
            }

            $links = $events->links();
            $this->events = collect($events->items());
        }

        $this->updateAudioEventsStatuses();

        return view('livewire.events.index', [
            'links' => $links,
            'months' => $months,
        ]);
    }

    protected function getMonths(LengthAwarePaginator $events): Collection
    {
        $months = $events->map(fn (Event $event) => $event->started_at
            ->setTimezone('Europe/Paris')
            ->format('Y-m'))->unique()->values()->collect();

        return $months;
    }

    public function setEventsToDisplay(string $eventsToDisplay): void
    {
        $this->eventsToDisplay = $eventsToDisplay;
        $this->resetPage();
    }

    protected function updateAudioEventsStatuses(): void
    {
        if ($this->pauseAllEvents) {
            $this->events = $this->events->map(function (Event $event) {
                $event->selected = in_array($event->id, $this->selectedEventIds, true);
                $event->playing = false;

                return $event;
            });

            return;
        }
        $this->selectedEventIds = [];
        $this->events = $this->events->map(function (Event $event) {
            $isPlaying = $event::class === $this->playedAudioSourceClass
                && $event->id === $this->playedAudioSourceId;
            if ($isPlaying) {
                $this->selectedEventIds[] = $event->id;
            }
            $event->selected = $isPlaying;
            $event->playing = $isPlaying && app(UserJourneysService::class)->getPlayerPlayingStatus();

            return $event;
        });
    }

    public function updatePlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId
    ): void {
        $this->setPlayedAudioSource($audioSourceClass, $audioSourceId);
        $this->pauseAllEvents = false;
    }

    public function playerHasPaused(): void
    {
        $this->pauseAllEvents = true;
    }

    public function playerIsResuming(): void
    {
        $this->pauseAllEvents = false;
    }

    public function paginationView(): string
    {
        return 'livewire.pagination';
    }
}

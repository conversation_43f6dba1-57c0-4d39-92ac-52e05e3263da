<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Str;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;

class ContactFormMessage extends Notification implements ShouldQueue
{
    use Queueable;

    public int $tries = 3;

    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public array $validatedData,
        public string $desiredDestinationKey,
        public string $desiredDestinationLabel,
        public string $desiredDestinationMail,
        public ?string $userAgent,
        public MediaCollection $attachmentsGeneral,
        public MediaCollection $attachmentsAudio,
        public MediaCollection $attachmentsImage,
        public MediaCollection $attachmentsAudioCover,
        public MediaCollection $attachmentsAudioPerformerPicture,
        public int $attachmentsGeneralTotalSize,
        public int $attachmentsImageTotalSize,
        public bool $isCopyToSender
    ) {
        $this->onQueue('high');
    }

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        $mailMessage = (new MailMessage())
            ->level('success')
            ->subject($this->isCopyToSender
                ? __('Copy of your sent message')
                : Str::upper($this->desiredDestinationLabel) . ' - ' . __('New message from the contact form') . ' - ' . $this->validatedData['first_name'] . ' ' . $this->validatedData['last_name']);
        if ($this->isCopyToSender) {
            $mailMessage->from($this->desiredDestinationMail, config('mail.from.name'));
            $mailMessage->greeting(__('Hello') . ' ' . $this->validatedData['first_name'] . ' ' . $this->validatedData['last_name'] . ',');
        } else {
            $mailMessage->from(config('mail.from.address'), $this->validatedData['first_name'] . ' ' . $this->validatedData['last_name'] . ' via SUN');
            $mailMessage->replyTo($this->validatedData['email'], $this->validatedData['first_name'] . ' ' . $this->validatedData['last_name'] . ' via SUN');
        }

        if ($this->isCopyToSender && $this->desiredDestinationKey === 'evenement') {
            $mailMessage->line('Nous vous remercions pour votre proposition d\'évènement et reviendrons vers vous si une mise en avant est envisagée.')
                ->line('Voici une copie de votre message envoyé.');
        } elseif ($this->isCopyToSender && $this->desiredDestinationKey === 'titre') {
            $mailMessage->line('Nous vous remercions pour votre proposition de titre(s) et reviendrons vers vous si une mise en avant est envisagée.')
                ->line('Voici une copie de votre message envoyé.');
        } else {
            $mailMessage->line(__($this->isCopyToSender
                ? 'Here is a copy of your message, sent from the contact form of :app.'
                : 'This message has been sent to you from the contact form of :app.', ['app' => config('app.name')]));
        }

        $mailMessage->line('  ')
            ->line('**Contact :**')
            ->line('*NOM :* ' . $this->validatedData['last_name'])
            ->line('*Prénom :* ' . $this->validatedData['first_name'])
            ->line('*Email :* [' . $this->validatedData['email'] . '](mailto:' . $this->validatedData['email'] . ')');
        if ($this->validatedData['phone_number']) {
            $mailMessage->line('*Téléphone :* [' . $this->validatedData['phone_number'] . ']' . '(tel:' . $this->validatedData['phone_number'] . ')');
        }

        if ($this->desiredDestinationKey !== 'evenement' && $this->desiredDestinationKey !== 'titre') {
            $mailMessage->line('  ')
                ->line('**Message :**')
                ->lines(explode(PHP_EOL, $this->validatedData['message']));
        }

        if ($this->desiredDestinationKey === 'equipe' && $this->validatedData['desired_team'] === 'redaction') {
            $mailMessage->line('  ')
                ->line('**Informations complémentaires :**')
                ->line('*' . __('validation.attributes.redaction_departement') . ' :* ' . str_replace(',', ', ', trim($this->validatedData['redaction_departement'], '[]')));
        }

        if ($this->desiredDestinationKey === 'equipe' && $this->validatedData['desired_team'] === 'programmation' && isset($this->validatedData['programmation_live_session'])) {
            $mailMessage->line('  ')
                ->line('**Informations complémentaires :**')
                ->line('*' . __('validation.attributes.programmation_live_session') . ' :* ' . $this->validatedData['programmation_live_session']);
        }

        if ($this->desiredDestinationKey === 'probleme') {
            $mailMessage->line('  ')
                ->line('**Informations complémentaires :**')
                ->line('*' . __('validation.attributes.problem_type') . ' :* ' . $this->validatedData['problem_type']);

            if ($this->validatedData['problem_type'] === \App\Http\Livewire\Front\Contact\Form::PROBLEM_TYPES_OPTIONS[0]) {
                // L'écoute et réception en FM / DAB+
                $mailMessage
                    ->line('*' . __('validation.attributes.problem_localisation') . ' :* ' . $this->validatedData['problem_localisation']);
            } elseif ($this->validatedData['problem_type'] === \App\Http\Livewire\Front\Contact\Form::PROBLEM_TYPES_OPTIONS[1]) {
                // L'application mySUN (site web, application mobile)
                $mailMessage
                    ->line('*' . __('validation.attributes.problem_device') . ' :* ' . $this->validatedData['problem_device'])
                    ->line('*' . __('validation.attributes.problem_device_name') . ' :* ' . $this->validatedData['problem_device_name']);
                if ($this->userAgent) {
                    $mailMessage->line('*User-Agent :* ' . $this->userAgent);
                }
            }
        }

        if ($this->desiredDestinationKey === 'evenement') {
            $mailMessage->line('  ')
                ->line('**Informations complémentaires :**')
                ->line('*' . __('validation.attributes.event_structure_name') . ' :* ' . $this->validatedData['event_structure_name'])
                ->line('*' . __('validation.attributes.event_already_solicited') . ' :* ' . $this->validatedData['event_already_solicited']);

            $mailMessage->line('  ')
                ->line('**Détail de l\'évènement :**')
                ->line('*' . __('validation.attributes.event_name') . ' :* ' . $this->validatedData['event_name'])
                ->line('*' . __('validation.attributes.event_date') . ' :* ' . $this->validatedData['event_date'])
                ->line('*' . __('validation.attributes.event_time_start') . ' :* ' . $this->validatedData['event_time_start'])
                ->line('*' . __('validation.attributes.event_time_end') . ' :* ' . $this->validatedData['event_time_end']);

            $eventStationsIds = json_decode($this->validatedData['event_stations'], true);
            if (is_array($eventStationsIds)) {
                $eventStations = \App\Models\Radio\RadioStation::whereIn('winmedia_id', $eventStationsIds)->get();
                $mailMessage
                    ->line('*' . __('validation.attributes.event_stations') . ' :* ' . $eventStations->pluck('name')->implode(', '));
            }

            $mailMessage
                ->line('*' . __('validation.attributes.event_address') . ' :* ' . $this->validatedData['event_address'])
                ->line('*' . __('validation.attributes.event_description') . ' :* ')
                ->lines(explode(PHP_EOL, $this->validatedData['event_description']))
                ->line('*' . __('validation.attributes.event_thematic') . ' :* ' . \App\Models\Audio\Thematic::find($this->validatedData['event_thematic'])?->title)
                ->line('*' . __('validation.attributes.event_type') . ' :* ' . str_replace(',', ', ', trim($this->validatedData['event_type'], '[]')))
                ->line('*' . __('validation.attributes.event_free') . ' :* ' . $this->validatedData['event_free'])
                ->line('*' . __('validation.attributes.event_audience_age') . ' :* ' . $this->validatedData['event_audience_age'])
                ->line('*' . __('validation.attributes.event_giveaway_tickets') . ' :* ' . $this->validatedData['event_giveaway_tickets'])
                ->line('*' . __('validation.attributes.event_link') . ' :* '
                    . ($this->validatedData['event_link'] ? ('[' . $this->validatedData['event_link'] . '](' . $this->validatedData['event_link'] . ')') : ''))
                ->line('*' . __('validation.attributes.event_buy_link') . ' :* '
                    . ($this->validatedData['event_buy_link'] ? ('[' . $this->validatedData['event_buy_link'] . '](' . $this->validatedData['event_buy_link'] . ')') : ''));
        }

        if ($this->desiredDestinationKey === 'titre') {
            $mailMessage->line('  ')
                ->line('**Informations complémentaires :**')
                ->line('*' . __('validation.attributes.track_person_type') . ' :* ' . $this->validatedData['track_person_type']);
            if ($this->validatedData['track_person_type'] !== 'Auditeur') {
                $mailMessage
                    ->line('*' . __('validation.attributes.track_structure_name') . ' :* ' . $this->validatedData['track_structure_name']);
            }
        }

        if ($this->desiredDestinationKey === 'evenement' || $this->desiredDestinationKey === 'titre') {
            $mailMessage->line('  ')
                ->line('**Message d\'informations supplémentaires :**')
                ->lines(explode(PHP_EOL, $this->validatedData['message']));
        }

        $linksGeneral = [];
        $linksEvent = [];
        $linksTracks = [];
        foreach ($this->attachmentsGeneral as $attachmentGeneral) {
            $attachmentGeneralLink = route('contact.form.logs.attachment.download', $attachmentGeneral);
            $attachmentGeneralFileName = $attachmentGeneral->file_name;
            $linksGeneral[] = ['url' => $attachmentGeneralLink, 'label' => $attachmentGeneralFileName];
        }
        foreach ($this->attachmentsImage as $attachmentImage) {
            $attachmentImageLink = route('contact.form.logs.attachment.download', $attachmentImage);
            $attachmentImageFileName = $this->desiredDestinationKey === 'evenement'
                ? '[Visuel évènement] ' . $attachmentImage->file_name
                : $attachmentImage->file_name;
            $linksEvent[] = [
                'url' => $attachmentImageLink,
                'label' => $attachmentImageFileName,
            ];
        }
        foreach ($this->attachmentsAudio as $attachmentAudio) {
            $attachmentAudioLink = route('contact.form.logs.attachment.download', $attachmentAudio);
            $attachmentAudioFileName = '[Audio] ' . $attachmentAudio->file_name;
            $attachmentAudioTitleArray = [];
            $attachmentAudioTitleBuyLink = null;
            $attachmentAudioCoverLink = null;
            $attachmentAudioCoverFileName = null;
            $attachmentAudioPerformerPictureLink = null;
            $attachmentAudioPerformerPictureFileName = null;

            if ($attachmentAudio->hasCustomProperty('audio_file_metadata_id')) {
                $audioFileMetadataID = $attachmentAudio->getCustomProperty('audio_file_metadata_id');

                foreach ($this->attachmentsAudioCover as $attachmentAudioCover) {
                    if ($attachmentAudioCover->hasCustomProperty('audio_file_metadata_id')) {
                        if ($attachmentAudioCover->getCustomProperty('audio_file_metadata_id') === $audioFileMetadataID) {
                            $attachmentAudioCoverLink = route('contact.form.logs.attachment.download', $attachmentAudioCover);
                            $attachmentAudioCoverFileName = '[Cover] ' . $attachmentAudioCover->file_name;
                        }
                    }
                }
                foreach ($this->attachmentsAudioPerformerPicture as $attachmentAudioPerformerPicture) {
                    if ($attachmentAudioPerformerPicture->hasCustomProperty('audio_file_metadata_id')) {
                        if ($attachmentAudioPerformerPicture->getCustomProperty('audio_file_metadata_id') === $audioFileMetadataID) {
                            $attachmentAudioPerformerPictureLink = route('contact.form.logs.attachment.download', $attachmentAudioPerformerPicture);
                            $attachmentAudioPerformerPictureFileName = '[Photo artiste] ' . $attachmentAudioPerformerPicture->file_name;
                        }
                    }
                }

                if (is_array($this->validatedData['audio_metadata'])) {
                    if (array_key_exists($audioFileMetadataID, $this->validatedData['audio_metadata'])) {
                        $attachmentAudioMetadata = $this->validatedData['audio_metadata'][$audioFileMetadataID];
                        $attachmentAudioTitleArray[] = 'Artiste: ' . $attachmentAudioMetadata['performer'];
                        $attachmentAudioTitleArray[] = 'Titre: ' . $attachmentAudioMetadata['title'];
                        $attachmentAudioTitleArray[] = 'Album: ' . $attachmentAudioMetadata['album'];
                        $attachmentAudioTitleArray[] = 'Genre: ' . $attachmentAudioMetadata['genre'];
                        $attachmentAudioTitleArray[] = 'Date de sortie: ' . $attachmentAudioMetadata['release_date'];
                        if (isset($attachmentAudioMetadata['embargo'])) {
                            $attachmentAudioTitleArray[] = 'Date d\'embargo: ' . $attachmentAudioMetadata['embargo'];
                        }
                        $attachmentAudioTitleArray[] = 'Région: ' . $attachmentAudioMetadata['localisation_region'];
                        if (isset($attachmentAudioMetadata['localisation_town'])) {
                            $attachmentAudioTitleArray[] = 'Ville: ' . $attachmentAudioMetadata['localisation_town'];
                        }
                        if ($attachmentAudioMetadata['localisation_region'] === 'Pays de la Loire' && isset($attachmentAudioMetadata['programmation_live_session'])) {
                            $attachmentAudioTitleArray[] = __('validation.attributes.programmation_live_session') . ' : ' . $attachmentAudioMetadata['programmation_live_session'];
                        }
                        $attachmentAudioTitleBuyLink = $attachmentAudioMetadata['buy_link'] ?? null;
                    }
                }

                if ($attachmentAudioTitleArray) {
                    if ($attachmentAudioTitleBuyLink) {
                        $linksTracks[] = [
                            'url' => $attachmentAudioLink,
                            'label' => $attachmentAudioFileName,
                            'prepend_array' => $attachmentAudioTitleArray,
                            'prepend_buy_link' => $attachmentAudioTitleBuyLink,
                            'start' => true,
                        ];
                    } else {
                        $linksTracks[] = [
                            'url' => $attachmentAudioLink,
                            'label' => $attachmentAudioFileName,
                            'prepend_array' => $attachmentAudioTitleArray,
                            'start' => true,
                        ];
                    }
                } else {
                    $linksTracks[] = [
                        'url' => $attachmentAudioLink,
                        'label' => $attachmentAudioFileName,
                        'start' => true,
                    ];
                }

                if ($attachmentAudioCoverLink && $attachmentAudioCoverFileName) {
                    $linksTracks[] = [
                        'url' => $attachmentAudioCoverLink,
                        'label' => $attachmentAudioCoverFileName,
                        'start' => false,
                    ];
                }

                if ($attachmentAudioPerformerPictureLink && $attachmentAudioPerformerPictureFileName) {
                    $linksTracks[] = [
                        'url' => $attachmentAudioPerformerPictureLink,
                        'label' => $attachmentAudioPerformerPictureFileName,
                        'start' => false,
                    ];
                }
            }
        }

        // Limit for attachments in mail 20 Mo (general priority, then image)
        if ($this->attachmentsGeneralTotalSize < (1024 * 1024 * 20)) {
            foreach ($this->attachmentsGeneral as $attachmentGeneral) {
                $mailMessage->attach(
                    $attachmentGeneral->getPath(),
                    ['as' => $attachmentGeneral->file_name, 'mime' => $attachmentGeneral->mime_type]
                );
            }
        }
        if (($this->attachmentsGeneralTotalSize + $this->attachmentsImageTotalSize) < (1024 * 1024 * 20)) {
            foreach ($this->attachmentsImage as $attachmentImage) {
                $mailMessage->attach(
                    $attachmentImage->getPath(),
                    ['as' => $attachmentImage->file_name, 'mime' => $attachmentImage->mime_type]
                );
            }
        }

        /*if (! $this->isCopyToSender) {
            $mailMessage->action('(WIP) Editer et publier sur la plateforme', 'https://lesonunique.com/admin');
        }*/

        $mailMessage->markdown('mail.custom', [
            'links_general' => $linksGeneral,
            'links_event' => $linksEvent,
            'links_tracks' => $linksTracks,
        ]);

        return $mailMessage;
    }
}

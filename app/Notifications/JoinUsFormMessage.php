<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Spatie\MediaLibrary\MediaCollections\Models\Collections\MediaCollection;

class JoinUsFormMessage extends Notification implements ShouldQueue
{
    use Queueable;

    public int $tries = 3;

    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public string $applicationType,
        public string $firstName,
        public string $lastName,
        public string $city,
        public string $birthYear,
        public string $email,
        public ?string $phoneNumber,
        public string $mission,
        public string $studio,
        public string $message,
        public array $experiences,
        public ?string $listeningSun,
        public array $listeningTypes,
        public array $favoriteMusicTypes,
        public MediaCollection $attachments,
        public int $attachmentsTotalSize,
        public bool $isCopyToSender
    ) {
        $this->onQueue('high');
    }

    public function via(): array
    {
        return ['mail'];
    }

    /** @SuppressWarnings(PHPMD.NPathComplexity) */
    public function toMail(): MailMessage
    {
        $mailMessage = (new MailMessage())
            ->level('success')
            ->subject($this->isCopyToSender
                ? 'Copie de votre candidature envoyée'
                : 'CANDIDATURE - ' . $this->applicationType . ' en tant que ' . $this->mission . ' - ' . $this->firstName . ' ' . $this->lastName);
        if ($this->isCopyToSender) {
            $mailMessage->from('<EMAIL>', config('mail.from.name'));
            $mailMessage->greeting(__('Hello') . " $this->firstName $this->lastName,");
        } else {
            $mailMessage->from(config('mail.from.address'), $this->firstName . ' ' . $this->lastName . ' via SUN');
            $mailMessage->replyTo($this->email, $this->firstName . ' ' . $this->lastName . ' via SUN');
        }
        $mailMessage->line(__($this->isCopyToSender
            ? 'Here is a copy of your message, sent from the application form of :app.'
            : 'This message has been sent to you from the application form of :app.', ['app' => config('app.name')]))
            ->line('  ')
            ->line('**Contact :**')
            ->line('*NOM :* ' . $this->lastName)
            ->line('*Prénom :* ' . $this->firstName)
            ->line('*Email :* [' . $this->email . '](mailto:' . $this->email . ')');
        if ($this->phoneNumber) {
            $mailMessage->line('*Téléphone :* [' . $this->phoneNumber . ']' . '(tel:' . $this->phoneNumber . ')');
        }
        $mailMessage
            ->line('*Ville :* ' . $this->city)
            ->line('*Année de naissance :* ' . $this->birthYear)
            ->line('  ')
            ->line('**Candidature :**')
            ->line('*Type :* ' . $this->applicationType)
            ->line('*Mission :* ' . $this->mission)
            ->line('*Studio :* ' . $this->studio)
            ->line('  ')
            ->line('**Message :**')
            ->lines(explode(PHP_EOL, $this->message))
            ->line('  ')
            ->line('**Informations complémentaires :**')
            ->line('*Ecoute déjà SUN :* ' . $this->listeningSun);
        if ($this->experiences) {
            $mailMessage->line('*Expériences :* ' . implode(', ', $this->experiences));
        }
        if ($this->listeningTypes) {
            $mailMessage->line('*Programmes audios écoutés :* ' . implode(', ', $this->listeningTypes));
        }
        if ($this->favoriteMusicTypes) {
            $mailMessage->line('*Genres musicaux favoris :* ' . implode(', ', $this->favoriteMusicTypes));
        }

        $links = [];

        foreach ($this->attachments as $attachment) {
            $attachmentLink = route('contact.form.logs.attachment.download', $attachment);
            $attachmentFileName = $attachment->file_name;
            $links[] = ['url' => $attachmentLink, 'label' => $attachmentFileName];
        }

        // Limit for attachments in mail 20 Mo
        if ($this->attachmentsTotalSize < (1024 * 1024 * 20)) {
            foreach ($this->attachments as $attachment) {
                $mailMessage->attach(
                    $attachment->getPath(),
                    ['as' => $attachment->file_name, 'mime' => $attachment->mime_type]
                );
            }
        }

        $mailMessage->markdown('mail.custom', ['links_general' => $links]);

        return $mailMessage;
    }
}

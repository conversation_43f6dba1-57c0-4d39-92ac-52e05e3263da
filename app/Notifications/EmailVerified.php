<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class EmailVerified extends Notification implements ShouldQueue
{
    use Queueable;

    public int $tries = 3;

    public function __construct()
    {
        $this->onQueue('high');
    }

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail($notifiable): MailMessage
    {
        return (new MailMessage())
            ->level('success')
            ->subject('Votre adresse email a bien été validée')
            ->greeting(__('Hello') . ' ' . $notifiable->name . ',')
            ->line('Votre adresse email a bien été validée. Vous pouvez maintenant vous connecter avec votre compte à la plateforme mySUN.')
            ->action('Accéder à la plateforme mySUN', route('app.index'));
    }
}

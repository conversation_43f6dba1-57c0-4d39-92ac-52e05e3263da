<?php

namespace App\View\Components\Front\Events;

use Carbon\CarbonInterface;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Card extends Component
{
    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public int $id,
        public string $coverUrl,
        public string $title,
        public string $description,
        public bool $playing,
        public bool $selected,
        public bool $hasAudio,
        public CarbonInterface $startedAt,
        public CarbonInterface $endedAt,
        public string $actionMode = 'route',
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.events.card');
    }
}

<?php

namespace App\View\Components\Front\Performers;

use App\Models\Performers\Member;
use Illuminate\Contracts\View\View;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\View\Component;

class MemberLine extends Component
{
    public Collection $performersOfMember;

    public function __construct(
        public Member $member,
        public bool $collapse = false,
    ) {
        $this->performersOfMember = $member->performers()->get();
    }

    public function render(): View
    {
        return view('components.front.performers.member-line');
    }
}

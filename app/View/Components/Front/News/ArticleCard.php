<?php

namespace App\View\Components\Front\News;

use Carbon\CarbonInterface;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ArticleCard extends Component
{
    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public int $id,
        public string $title,
        public string $slug,
        public string $coverUrl,
        public bool $smallCover,
        public bool $playing,
        public bool $selected,
        public bool $hasAudio,
        public CarbonInterface $publishedAt,
        public string $actionMode = 'route',
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.news.article-card');
    }
}

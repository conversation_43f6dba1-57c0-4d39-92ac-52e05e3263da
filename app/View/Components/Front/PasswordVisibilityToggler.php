<?php

namespace App\View\Components\Front;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PasswordVisibilityToggler extends Component
{
    public function __construct(public string $passwordVisibleVariableName, public bool $passwordVisible = false)
    {
        //
    }

    public function render(): View
    {
        return view('components.front.password-visibility-toggler');
    }
}

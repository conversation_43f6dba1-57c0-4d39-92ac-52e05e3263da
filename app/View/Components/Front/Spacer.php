<?php

namespace App\View\Components\Front;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Spacer extends Component
{
    public const TYPES = [
        'xxl' => ['key' => 'xxl', 'label' => 'Ultra large (120px)', 'class' => 'spacer-xxl'],
        'xl' => ['key' => 'xl', 'label' => 'Extra large (80px)', 'class' => 'spacer-xl'],
        'lg' => ['key' => 'lg', 'label' => 'Large (40px)', 'class' => 'spacer-lg'],
        'md' => ['key' => 'md', 'label' => 'Moyen (32px)', 'class' => 'spacer-md'],
        'sm' => ['key' => 'sm', 'label' => 'Petit (24px)', 'class' => 'spacer-sm'],
        'xs' => ['key' => 'xs', 'label' => 'Très petit (16px)', 'class' => 'spacer-xs'],
        'xxs' => ['key' => 'xxs', 'label' => 'Ultra petit (8px)', 'class' => 'spacer-xxs'],
        'tiny' => ['key' => 'tiny', 'label' => 'Minuscule (4px)', 'class' => 'tiny'],
    ];

    public array $type;

    public function __construct(string $typeKey)
    {
        $this->type = self::TYPES[$typeKey];
    }

    public function render(): View
    {
        return view('components.front.spacer');
    }
}

<?php

namespace App\View\Components\Front\Map;

use App\Models\Audio\Playlist;
use App\Models\Audio\Song;
use App\Models\Performers\Album;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Illuminate\View\Component;

class SongLine extends Component
{
    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public int $lineNumber,
        public Song $song,
        public bool $selected,
        public bool $playing,
        public bool $favorite,
        public bool $dedicatable,
        public ?int $sourceId = null,
        public ?string $sourceClass = null,
        public bool $dedicationHighlighted = true,
        public bool $dedicationDisabled = false,
        public string $playButtonKeyPrefix = '',
    ) {
        $this->playButtonKeyPrefix = \trim($this->playButtonKeyPrefix);
    }

    public function playButtonComponentKey(): string
    {
        $key = $this->playButtonKeyPrefix . 'play-';

        if ($this->sourceClass && $this->sourceId) {
            $key .= match ($this->sourceClass) {
                Album::class => 'album-',
                Playlist::class => 'playlist-',
                default => '',
            };

            $key .= $this->sourceId . '-';
        }

        $key .= 'song-' . $this->song->id . '-';
        $key .= $this->playing ? 'playing' : '';

        return $key;
    }

    public function albumId(): ?int
    {
        return $this->song->albumRelationship?->id;
    }

    public function albumTitle(): ?string
    {
        return $this->song->albumRelationship?->name;
    }

    public function albumSuffix(): string
    {
        if ($this->song->publisher) {
            $publisher = \ltrim(\ltrim($this->song->publisher, '@'), '#');

            return $this->song->yearFourDigit
                ? ' (' . $this->song->yearFourDigit . ' · ' . $publisher . ')'
                : ' (' . $publisher . ')';
        }

        return $this->song->yearFourDigit ? ' (' . $this->song->yearFourDigit . ')' : '';
    }

    public function performerId(): ?int
    {
        return $this->song->performerRelationship?->id;
    }

    public function performerName(): ?string
    {
        return $this->song->performerRelationship?->name ?: $this->song->performer;
    }

    public function titleSuffix(): string
    {
        $version = trim($this->song->version);

        return Str::length($version) ? ' (' . $version . ')' : '';
    }

    public function render(): View
    {
        return view('components.front.map.song-line');
    }
}

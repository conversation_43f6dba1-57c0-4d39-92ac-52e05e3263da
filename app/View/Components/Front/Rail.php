<?php

namespace App\View\Components\Front;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use InvalidArgumentException;

class Rail extends Component
{
    public function __construct(
        public string $name,
        public ?string $title = null,
        public ?string $showAllRoute = null,
        public array $showAllParams = [],
        public ?string $titleClass = null,
        public int $titleLevel = 2,
    ) {
        if ($this->titleLevel < 1 || $this->titleLevel > 6) {
            throw new InvalidArgumentException('The title level must be between 1 and 6');
        }
    }

    public function render(): View
    {
        return view('components.front.rail');
    }
}

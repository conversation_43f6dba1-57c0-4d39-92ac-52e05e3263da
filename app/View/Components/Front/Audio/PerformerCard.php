<?php

namespace App\View\Components\Front\Audio;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PerformerCard extends Component
{
    public function __construct(
        public string $performerId,
        public string $performerName,
        public string $performerCoverThumbUrl,
        public ?string $performerMusicStyles,
        public string $actionMode = 'route',
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.audio.performer-card');
    }
}

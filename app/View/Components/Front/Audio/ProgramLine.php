<?php

namespace App\View\Components\Front\Audio;

use App\Models\Audio\Podcast;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\View\Component;

class ProgramLine extends Component
{
    public function __construct(
        public Podcast $podcast,
        public bool $playing,
        public bool $selected,
        public ?Collection $subPodcasts = null,
        public ?int $lineNumber = null,
        public bool $isDark = false,
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.audio.program-line');
    }
}

<?php

namespace App\View\Components\Front\Audio;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Illuminate\View\Component;

class SongLine extends Component
{
    public string $titleSupplement = '';

    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public int $lineNumber,
        public int $songId,
        public string $coverUrl,
        public string $title,
        public ?int $performerId,
        public string $performerName,
        public string $version,
        public ?int $albumId,
        public string $albumName,
        public string $humanReadableDuration,
        public string $dynamicData,
        public bool $playing,
        public bool $selected,
        public bool $dedicatable,
        public bool $dedicationActionHighlighted = true,
        public ?int $year = null,
        public ?int $placeLabelId = null,
        public ?string $labelName = null,
        public ?string $liveBroadcastHour = null,
        public ?int $playlistSourceId = null,
        public ?int $albumSourceId = null,
        public bool $isMusic = false,
        public bool $dedicationDisabled = false,
        public ?int $songFavoriteUsersCount = null,
    ) {
        $this->setTitleSupplement();
        $labelName = ltrim(ltrim($labelName, '@'), '#');
    }

    protected function setTitleSupplement(): void
    {
        $version = trim($this->version);
        if (Str::length($version) > 0) {
            $this->titleSupplement = ' (' . $version . ')';
        }
    }

    public function render(): View
    {
        return view('components.front.audio.song-line');
    }
}

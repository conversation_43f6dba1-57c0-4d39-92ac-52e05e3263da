<?php

namespace App\View\Components\Front\Audio;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Date;
use Illuminate\View\Component;

class RadioStationCard extends Component
{
    public ?string $timer = null;

    public ?int $progressPercent = null;

    public ?string $totalDuration = null;

    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public int $radioStationId,
        public string $radioStationName,
        public string $radioStationColor,
        public ?string $currentSongCoverUrl,
        public string $currentSongTitle,
        public ?string $currentSongPerformer = null,
        public ?string $currentSongLabel = null,
        public ?string $currentSongYear = null,
        protected ?string $currentSongStartedAt = null,
        protected ?int $currentSongRealDuration = null,
        public bool $isHighlighted = false,
        public bool $isPlaying = false,
        public bool $isDark = true,
        public ?int $songPerformerId = null,
        public ?int $songAlbumId = null,
        public ?int $songPlaceLabelId = null,
        public ?int $programId = null,
    ) {
        if ($isHighlighted) {
            $now = Date::now();
            $startedAt = Date::parse($currentSongStartedAt);
            $playedMilliseconds = $now->diffInMilliseconds($startedAt);
            $timer = $now->diff($startedAt)->format('%H:%I:%S');
            $totalDuration = Date::now()->startOfDay()->addMilliseconds($currentSongRealDuration);
            $progressPercent = $currentSongRealDuration ? ($playedMilliseconds * 100) / $currentSongRealDuration : 0;
            $this->timer = Date::parse($timer)->gte($totalDuration)
                ? $totalDuration->format('H:i:s')
                : $timer;
            $this->progressPercent = $progressPercent >= 100 ? 100 : $progressPercent;
            $this->totalDuration = $totalDuration->format('H:i:s');
        }
    }

    public function render(): View
    {
        return view('components.front.audio.radio-station-card');
    }
}

<?php

namespace App\View\Components\Front\Audio;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PlaylistCard extends Component
{
    public function __construct(
        public int $playlistId,
        public ?string $coverUrl,
        public array $mosaicCoverUrl,
        public string $playlistTitle,
        public int $songsCount,
        public bool $playing,
        public bool $selected,
        public bool $publicPlaylist = false,
        public string $actionMode = 'route',
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.audio.playlist-card');
    }
}

<?php

namespace App\View\Components\Front\Audio;

use Carbon\CarbonInterface;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PodcastLine extends Component
{
    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public int $podcastId,
        public string $coverUrl,
        public string $title,
        public string $description,
        public string $humanReadableDuration,
        public CarbonInterface $publishedAt,
        public bool $playing,
        public bool $selected,
        public ?int $programSourceId = null,
        public array $audioSourceParams = [],
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.audio.podcast-line');
    }
}

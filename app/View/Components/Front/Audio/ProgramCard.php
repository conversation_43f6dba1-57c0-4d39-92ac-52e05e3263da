<?php

namespace App\View\Components\Front\Audio;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class ProgramCard extends Component
{
    public function __construct(
        public string $programId,
        public string $programName,
        public string $programCoverThumbUrl,
        public ?int $podcastsCount = 0,
        public string $actionMode = 'route',
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.audio.program-card');
    }
}

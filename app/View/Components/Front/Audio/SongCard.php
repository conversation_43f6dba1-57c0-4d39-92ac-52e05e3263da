<?php

namespace App\View\Components\Front\Audio;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class SongCard extends Component
{
    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public int $songId,
        public string $coverUrl,
        public string $title,
        public ?int $performerId,
        public string $performerName,
        public ?int $albumId,
        public string $albumName,
        public bool $playing,
        public bool $selected,
        public bool $dedicatable,
        public bool $dedicationDisabled = false,
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.audio.song-card');
    }
}

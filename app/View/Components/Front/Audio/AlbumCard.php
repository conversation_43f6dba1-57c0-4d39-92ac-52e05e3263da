<?php

namespace App\View\Components\Front\Audio;

use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class AlbumCard extends Component
{
    public function __construct(
        public int $id,
        public string $coverUrl,
        public string $name,
        public int $songsCount,
        public bool $playing,
        public bool $selected,
        public ?string $albumLocationName = null,
        public ?int $albumLocationId = null
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.audio.album-card');
    }
}

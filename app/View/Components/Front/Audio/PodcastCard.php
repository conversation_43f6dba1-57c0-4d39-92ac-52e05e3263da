<?php

namespace App\View\Components\Front\Audio;

use Carbon\CarbonInterface;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PodcastCard extends Component
{
    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        public int $podcastId,
        public string $coverUrl,
        public int $programId,
        public string $programTitle,
        public string $podcastTitle,
        public string $description,
        public string $humanReadableDuration,
        public bool $playing,
        public bool $selected,
        public ?CarbonInterface $publishedAt = null,
        public string $actionMode = 'route',
    ) {
        //
    }

    public function render(): View
    {
        return view('components.front.audio.podcast-card');
    }
}

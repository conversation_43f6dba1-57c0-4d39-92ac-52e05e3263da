<?php

namespace App\View\Components\Front\Audio;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\File;
use Illuminate\View\Component;
use Intervention\Image\Facades\Image;

class PlaylistHeader extends Component
{
    public ?string $backgroundRgbColor = null;

    public function __construct(
        public string $title,
        public Collection $headerBackgroundCoverUrls,
        public ?string $firstCoverPath,
        public bool $backgroundGradientImageMode = false,
        public ?string $headerBackgroundUrl = null
    ) {
        $this->setCoversPatchworkHeaderBackgroundColor();
    }

    protected function setCoversPatchworkHeaderBackgroundColor(): void
    {
        if (File::exists($this->firstCoverPath)) {
            $image = Image::make($this->firstCoverPath);
            $dominantColor = $image->resize(1, 1)->limitColors(1)->pickColor(0, 0);
            $this->backgroundRgbColor = implode(',', $dominantColor);
        }
    }

    public function render(): View
    {
        return view('components.front.audio.playlist-header');
    }
}

<?php

namespace App\View\Components\Front\Audio;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\File;
use Illuminate\View\Component;
use Intervention\Image\Facades\Image;

class AlbumHeader extends Component
{
    public ?string $backgroundRgbColor = null;

    public function __construct(
        public string $title,
        public ?string $coverPath,
    ) {
        $this->setCoverBackgroundColor();
    }

    protected function setCoverBackgroundColor(): void
    {
        if (File::exists($this->coverPath)) {
            $image = Image::make($this->coverPath);
            $dominantColor = $image->resize(1, 1)->limitColors(1)->pickColor(0, 0);
            $this->backgroundRgbColor = implode(',', $dominantColor);
        }
    }

    public function render(): View
    {
        return view('components.front.audio.album-header');
    }
}

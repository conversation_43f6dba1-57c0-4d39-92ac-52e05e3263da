<?php

namespace App\View\Components\Common\Forms;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Str;
use Illuminate\View\Component;

/**
 * @SuppressWarnings(PHPMD.ExcessiveParameterList)
 * @SuppressWarnings(PHPMD.TooManyFields)
 */
class Filepond extends Component
{
    public bool|int $multiple;

    public bool|int $required;

    public bool|int $disabled;

    public array|string $accept;

    public array $oldFiles;

    public int $maxFiles;

    public int $maxFileSize;

    public int $maxTotalFileSize;

    public ?string $label;

    public ?string $caption;

    public ?string $acceptCaption;

    public ?string $prepend;

    public bool|int $withCropper;

    public ?string $aspectRatio;

    public bool|int $withMetadata;

    public bool|int $isSquareImage;

    public string $squareLabel = '';

    /**
     * Create a new component instance.
     */
    public function __construct(
        ?bool $multiple = false,
        ?bool $required = false,
        ?bool $disabled = false,
        ?string $accept = '',
        ?array $oldFiles = [],
        ?int $maxFiles = 3,
        ?int $maxFileSize = 10,
        ?int $maxTotalFileSize = 20,
        ?string $label = null,
        ?string $caption = null,
        ?string $acceptCaption = null,
        ?string $prepend = null,
        ?bool $withCropper = false,
        ?string $aspectRatio = '1:1',
        ?bool $withMetadata = false,
        ?bool $isSquareImage = false,
        ?string $squareStartLabel = 'Faites glisser une image')
    {
        $this->multiple = $multiple;
        $this->required = $required;
        $this->disabled = $disabled;
        $this->accept = $accept;
        $this->oldFiles = $oldFiles;
        $this->maxFiles = $maxFiles;
        $this->maxFileSize = $maxFileSize;
        $this->maxTotalFileSize = $maxTotalFileSize;
        $this->label = $label;
        $this->caption = $caption;
        $this->acceptCaption = $acceptCaption;
        $this->prepend = $prepend;
        $this->withCropper = $withCropper;
        $this->aspectRatio = $aspectRatio;
        $this->withMetadata = $withMetadata;
        $this->isSquareImage = $isSquareImage;
        $this->squareLabel = $squareStartLabel . ' ou <span class="filepond--label-action">Parcourir</span>';
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View
    {
        // Set boolean values
        if (! $this->multiple) {
            $this->multiple = 0;
        }
        if (! $this->required) {
            $this->required = 0;
        }
        if (! $this->disabled) {
            $this->disabled = 0;
        }
        if (! $this->withCropper) {
            $this->withCropper = 0;
        }
        if (! $this->withMetadata) {
            $this->withMetadata = 0;
        }
        if (! $this->isSquareImage) {
            $this->isSquareImage = 0;
        }

        // Prepare accept files to JSON
        if (is_string($this->accept)) {
            $this->accept = explode(',', $this->accept);
        }
        $this->accept = array_map('trim', $this->accept);
        $this->accept = array_filter($this->accept);
        $this->accept = array_unique($this->accept);
        $this->accept = array_values($this->accept);
        $this->accept = array_map('strtolower', $this->accept);
        $this->accept = json_encode($this->accept);

        if (! $this->caption) {
            $caption = '';

            if ($this->acceptCaption) {
                $caption .= $this->acceptCaption . ' &nbsp;&nbsp;';
            }
            if ($this->maxFileSize && $this->multiple) {
                $caption .= 'Poids max. par fichier : ' . $this->maxFileSize . ' Mo. &nbsp;&nbsp;';
            } elseif ($this->maxFileSize && ! $this->multiple) {
                $caption .= 'Poids max. : ' . $this->maxFileSize . ' Mo. &nbsp;&nbsp;';
            }
            if ($this->multiple) {
                if ($this->maxTotalFileSize) {
                    $caption .= 'Poids max. total : ' . $this->maxTotalFileSize . ' Mo. &nbsp;&nbsp;';
                }
                if ($this->maxFiles) {
                    $caption .= 'Nombre max de fichiers : ' . $this->maxFiles . '. &nbsp;&nbsp;';
                }
            }

            $this->caption = Str::length($caption) > 0 ? $caption : null;
        }

        return view('components.common.forms.filepond');
    }
}

<?php

namespace App\Brickables;

use App\Http\Controllers\Brickables\OneTitleTextColumnOneImageColumnBricksController;
use App\Http\Requests\Brickables\OneTitleTextColumnOneImageColumn\OneTitleTextColumnOneImageColumnStoreRequest;
use App\Http\Requests\Brickables\OneTitleTextColumnOneImageColumn\OneTitleTextColumnOneImageColumnUpdateRequest;
use App\Models\Brickables\OneTitleTextColumnOneImageColumnBrick;
use Okipa\LaravelBrickables\Abstracts\Brickable;

class OneTitleTextColumnOneImageColumn extends Brickable
{
    protected function setBrickModelClass(): string
    {
        return OneTitleTextColumnOneImageColumnBrick::class;
    }

    protected function setBricksControllerClass(): string
    {
        return OneTitleTextColumnOneImageColumnBricksController::class;
    }

    public function validateStoreInputs(): array
    {
        $fileKeys = array_keys(app(OneTitleTextColumnOneImageColumnStoreRequest::class)->allFiles());

        return array_filter(
            app(OneTitleTextColumnOneImageColumnStoreRequest::class)->validated(),
            static fn ($key) => ! in_array($key, $fileKeys, true),
            ARRAY_FILTER_USE_KEY
        );
    }

    public function validateUpdateInputs(): array
    {
        $fileKeys = array_keys(app(OneTitleTextColumnOneImageColumnUpdateRequest::class)->allFiles());

        return array_filter(
            app(OneTitleTextColumnOneImageColumnUpdateRequest::class)->validated(),
            static fn ($key) => ! in_array($key, $fileKeys, true),
            ARRAY_FILTER_USE_KEY
        );
    }
}

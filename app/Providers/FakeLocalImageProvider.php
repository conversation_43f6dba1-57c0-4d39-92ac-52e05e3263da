<?php

namespace App\Providers;

use Faker\Provider\Base;
use Illuminate\Support\Facades\File;
use Intervention\Image\Facades\Image;

class FakeLocalImageProvider extends Base
{
    /** @throws \Exception */
    public static function image($dir = null, $width = 640, $height = 480)
    {
        $gdImage = imagecreate($width, $height);
        $tempStoragePath = sys_get_temp_dir() . '/faker/images';
        if (! File::isDirectory($tempStoragePath)) {
            File::makeDirectory($tempStoragePath, recursive: true);
        }
        $img = Image::make($gdImage)
            ->colorize(random_int(-100, 100), random_int(-100, 100), random_int(-100, 100))
            ->save(($dir ?: $tempStoragePath) . '/' . uniqid('fake-image', true) . '.jpg');

        return $img->basePath();
    }
}

<?php

namespace App\Providers;

use App\Models\Users\User;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     *
     * @throws \Exception
     */
    public function boot(): void
    {
        $this->registerPolicies();
        Gate::define('access_with_team', function (User $user, $teamUniqueKeys) {
            $teamUniqueKeys = explode('|', $teamUniqueKeys);

            return in_array($user->team->unique_key, $teamUniqueKeys, true);
        });
    }
}

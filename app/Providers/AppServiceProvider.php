<?php

namespace App\Providers;

use Carbon\CarbonImmutable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;
use Illuminate\Validation\Rules\Password;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Set Bootstrap pagination UI.
        // More @ https://laravel.com/docs/pagination#using-bootstrap
        Paginator::useBootstrap();
        // Defined default password rule.
        // More @ https://laravel.com/docs/validation#defining-default-password-rules
        Password::defaults(static fn () => Password::min(8)->uncompromised());
        // Trigger errors if eager loading is not correctly configured.
        // More @ https://laravel.com/docs/eloquent-relationships#preventing-lazy-loading
        Model::preventLazyLoading(! app()->isProduction());
        // Use immutable carbon dates by default.
        // More @ https://dyrynda.com.au/blog/laravel-immutable-dates
        Date::use(CarbonImmutable::class);
        // Exclude unvalidated array keys in the `validated` data.
        // More @ https://laravel.com/docs/8.x/validation#excluding-unvalidated-array-keys
        Validator::excludeUnvalidatedArrayKeys();
        // Fix for SwiftMailer Service
        $_SERVER['SERVER_NAME'] = gethostname();
    }
}

<?php

declare(strict_types=1);

namespace App\Providers;

use App\Services\Elasticsearch\CustomSearchService;
use App\Services\Map\MapConfiguration;
use App\Services\Map\MapService;
use App\Services\Map\MapStateService;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class MapServiceProvider extends ServiceProvider implements DeferrableProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(MapStateService::class, function (Application $app) {
            $request = $app->make('request');
            $cache = $app->make('cache');
            $session = $app->make('session');
            $sessionLifetime = $app->make('config')->get('session.lifetime');

            $iframeConfig = null;
            if ($request->get('sent_from_iframe')) {
                // Vérifier si au moins un paramètre pertinent est présent
                $validParams = [
                    'station',
                    'search',
                    'mode',
                    'thematics',
                    'lng',
                    'lat',
                    'zoom',
                    'place',
                    'place_show_all',
                    'info_type',
                    'info_id',
                    'rail_type',
                    'rail_place',
                    'period',
                ];
                if ($request->hasAny($validParams)) {
                    $iframeConfig = MapConfiguration::fromArray($request->all());
                }
            }

            return new MapStateService(
                $cache,
                $session,
                $sessionLifetime,
                $iframeConfig
            );
        });
        $this->app->singleton(MapService::class, function (Application $app) {
            return new MapService(
                $app->make(CustomSearchService::class),
                $app->make(MapStateService::class),
            );
        });
    }

    /**
     * Get the services provided by the provider.
     */
    public function provides(): array
    {
        return [MapService::class, MapStateService::class];
    }
}

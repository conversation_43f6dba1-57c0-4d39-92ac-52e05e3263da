<?php

namespace App\Models\Cookies;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class CookieService extends Model
{
    use HasFactory;

    /** @var string */
    protected $table = 'cookie_services';

    /** @var array<int, string> */
    protected $fillable = [
        'unique_key',
        'title',
        'description',
        'cookies',
        'required',
        'enabled_by_default',
        'active',
    ];

    /** @var array<string, string> */
    protected $casts = ['cookies' => 'array'];

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(CookieCategory::class, 'cookie_service_category')->ordered()->withTimestamps();
    }

    public function categoryIds(): Attribute
    {
        return new Attribute(
            get: fn () => $this->categories->pluck('id')->toArray()
        );
    }
}

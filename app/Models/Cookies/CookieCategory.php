<?php

namespace App\Models\Cookies;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\EloquentSortable\Sortable;
use <PERSON>tie\EloquentSortable\SortableTrait;

class CookieCategory extends Model implements Sortable
{
    use HasFactory;
    use SortableTrait;

    public array $sortable = ['order_column_name' => 'position', 'sort_when_creating' => true];

    /** @var string */
    protected $table = 'cookie_categories';

    /** @var array<int, string> */
    protected $fillable = ['unique_key', 'title', 'description', 'position'];

    public function services(): BelongsToMany
    {
        return $this->belongsToMany(CookieService::class, 'cookie_service_category')->withTimestamps();
    }
}

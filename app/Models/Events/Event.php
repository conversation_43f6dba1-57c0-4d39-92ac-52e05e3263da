<?php

namespace App\Models\Events;

use App\Models\Announcements\Announcement;
use App\Models\Audio\Thematic;
use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Map\Point;
use App\Models\Radio\RadioStation;
use App\Models\Traits\HasLocation;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Parsedown;
use Spatie\Feed\Feedable;
use Spatie\Feed\FeedItem;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Event extends Model implements Feedable, HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use HasLocation;
    use InteractsWithMedia;

    public const ANNOUNCEMENT_WIDTH = 120;

    protected $fillable = [
        'thematic_id',
        'user_id',
        'title',
        'subtitle',
        'description',
        'audio_caption',
        'tags',
        'started_at',
        'ended_at',
        'active',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'started_at' => 'datetime',
        'ended_at' => 'datetime',
        'active' => 'boolean',
    ];

    public const PAST = 'events_past';

    public const CURRENT = 'events_current';

    public const NEXT = 'events_next';

    protected static function booted(): void
    {
        static::addGlobalScope('order', static fn (Builder $builder) => $builder->orderBy('started_at'));

        static::deleting(function (Event $event) {
            $event->contentLocations()->delete();
        });
    }

    public function scopeActive(Builder $query): void
    {
        $query->where('active', true);
    }

    public function scopeComing(Builder $query): void
    {
        $query->where('ended_at', '>=', now()->subDay()->startOfDay());
    }

    public static function getFeedItems(): Collection
    {
        return app(self::class)
            ->where('ended_at', '>=', now()->subDay()->startOfDay())
            ->where('active', true)
            ->orderBy('updated_at', 'desc')
            ->with(['media', 'thematic'])
            ->get()
            ->slice(0, 50);
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('seo')
                    ->fit(Manipulations::FIT_CROP, 600, 600)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('background_gradient')
                    ->fit(Manipulations::FIT_CROP, 475, 296)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('player_mobile')
                    ->fit(Manipulations::FIT_CROP, 337, 337)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('card')
                    ->fit(Manipulations::FIT_CROP, 125, 177)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('agenda_card')
                    ->fit(Manipulations::FIT_CROP, 200, 283)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('announcement')
                    ->fit(Manipulations::FIT_CROP, self::ANNOUNCEMENT_WIDTH, 170)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('player_thumb')
                    ->fit(Manipulations::FIT_CROP, 74, 74)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
            });
        $this->addMediaCollection('audio')->singleFile()->acceptsMimeTypes(['audio/mpeg']);
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    public function thematic(): BelongsTo
    {
        return $this->belongsTo(Thematic::class, 'thematic_id', 'id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function radioStations(): BelongsToMany
    {
        return $this->belongsToMany(RadioStation::class)->withTimestamps();
    }

    public function announcement(): MorphOne
    {
        return $this->morphOne(Announcement::class, 'announceable');
    }

    public function radioStationIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->radioStations->sortBy('name')->pluck('id')->values()->toJson()
        );
    }

    public function hasAudio(): Attribute
    {
        return new Attribute(
            get: fn () => $this->getFirstMedia('audio') !== null
        );
    }

    public function audioStream(): Attribute
    {
        return new Attribute(
            get: fn () => [
                'hls' => null,
                'dash' => null,
                'mp3' => $this->getFirstMediaUrl('audio'),
            ]
        );
    }

    public function location(): Place|Point
    {
        $this->loadLocationsFully();

        $contentLocation = $this->contentLocations->first(
            static fn (ContentLocation $item): bool => $item->location_type === Place::class
        );

        if ($contentLocation) {
            /** @var Place */
            return $contentLocation->location;
        }

        $contentLocation = $this->contentLocations->firstOrFail(
            static fn (ContentLocation $item): bool => $item->location_type === Point::class
        );

        /** @var Point */
        return $contentLocation->location;
    }

    public function point(): Point
    {
        if ($this->place()?->currentPoint) {
            /** @var Point */
            return $this->place()->currentPoint;
        }

        $contentLocation = $this->contentLocations->firstOrFail(
            static fn (ContentLocation $item): bool => $item->location_type === Point::class
        );

        /** @var Point */
        return $contentLocation->location;
    }

    public function toFeedItem(): FeedItem
    {
        $media = $this->getFirstMedia('cover');

        return FeedItem::create()
            ->id(ltrim(parse_url(route('app.event.show', $this))['path'], '/'))
            ->title($this->title)
            ->summary(Str::limit(html_entity_decode(strip_tags((new Parsedown())->text($this->description)))))
            ->link(route('app.event.show', $this))
            ->authorName(settings()->email . ' (' . config('app.name') . ')')
            ->category($this->thematic->title ?? 'Actu')
            ->enclosure($media?->getUrl())
            ->enclosureType($media?->mime_type)
            ->enclosureLength($media?->size)
            ->updated($this->updated_at);
    }
}

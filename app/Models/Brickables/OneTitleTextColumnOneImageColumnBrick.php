<?php

namespace App\Models\Brickables;

use <PERSON>ipa\LaravelBrickables\Models\Brick;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class OneTitleTextColumnOneImageColumnBrick extends Brick implements HasMedia
{
    use ExtendsMediaAbilities;
    use InteractsWithMedia;

    /** @var array<int, string> */
    protected $with = ['media'];

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(fn (?Media $media = null) => $this->addMediaConversion('half')
                ->fit(Manipulations::FIT_CROP, 475, 410)
                ->withResponsiveImages()
                ->format(Manipulations::FORMAT_WEBP));
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }
}

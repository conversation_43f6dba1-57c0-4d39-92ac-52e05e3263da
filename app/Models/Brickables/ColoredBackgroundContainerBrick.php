<?php

namespace App\Models\Brickables;

use App\Brickables\Carousel;
use App\Brickables\ColoredBackgroundContainer;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Okipa\LaravelBrickables\Contracts\HasBrickables;
use Okipa\LaravelBrickables\Models\Brick;
use Okipa\LaravelBrickables\Traits\HasBrickablesTrait;

class ColoredBackgroundContainer<PERSON>rick extends Brick implements HasBrickables
{
    use HasBrickablesTrait;
    use HasFactory;

    public array $brickables = [
        'number_of_bricks' => [
            ColoredBackgroundContainer::class => ['max' => 0],
            Carousel::class => ['max' => 0],
        ],
    ];
}

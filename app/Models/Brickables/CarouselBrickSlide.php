<?php

namespace App\Models\Brickables;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use <PERSON><PERSON>\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class CarouselBrickSlide extends Model implements HasMedia, Sortable
{
    use ExtendsMediaAbilities;
    use InteractsWithMedia;
    use SortableTrait;

    public array $sortable = ['order_column_name' => 'position', 'sort_when_creating' => true];

    /** @var string */
    protected $table = 'carousel_brick_slides';

    /** @var array<int, string> */
    protected $fillable = [
        'brick_id',
        'label',
        'caption',
        'url',
        'url_target_blank',
        'active',
        'position',
    ];

    /** @var array<int, string> */
    protected $with = ['media'];

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('containerized')
                    ->fit(Manipulations::FIT_CROP, 1140, 400)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('full')
                    ->fit(Manipulations::FIT_CROP, 2560, 700)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
            });
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    public function brick(): BelongsTo
    {
        return $this->belongsTo(CarouselBrick::class, 'brick_id');
    }

    public function buildSortQuery(): Builder
    {
        return static::query()->where('brick_id', $this->brick->id);
    }
}

<?php

namespace App\Models\Teams;

use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Team extends Model
{
    use HasFactory;

    /** @var array<int, string> */
    protected $fillable = ['name', 'unique_key'];

    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }
}

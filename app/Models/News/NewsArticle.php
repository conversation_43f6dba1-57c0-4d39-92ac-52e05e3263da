<?php

namespace App\Models\News;

use App\Models\Announcements\Announcement;
use App\Models\Audio\Thematic;
use App\Models\Radio\RadioStation;
use App\Models\Traits\HasLocation;
use App\Models\Traits\HasSeoMeta;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Parsedown;
use Spatie\Feed\Feedable;
use Spatie\Feed\FeedItem;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class NewsArticle extends Model implements Feedable, HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use HasLocation {
        locationName as private _locationName;
    }
    use HasSeoMeta;
    use InteractsWithMedia;

    public const ANNOUNCEMENT_WIDTH = 216;

    /** @var string */
    protected $table = 'news_articles';

    /** @var array<int, string> */
    protected $fillable = [
        'title',
        'slug',
        'tags',
        'description',
        'audio_caption',
        'thematic_id',
        'user_id',
        'published_at',
        'active',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'active' => 'boolean',
        'published_at' => 'datetime',
    ];

    protected static function booted(): void
    {
        static::deleting(function (NewsArticle $news) {
            $news->contentLocations()->delete();
        });
    }

    public static function getFeedItems(): Collection
    {
        return self::where('published_at', '<=', Date::now())
            ->where('active', true)
            ->orderBy('published_at', 'desc')
            ->with(['media', 'thematic'])
            ->get()
            ->slice(0, 50);
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('illustrations')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('seo')
                    ->fit(Manipulations::FIT_CROP, 600, 600)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('player_mobile')
                    ->fit(Manipulations::FIT_CROP, 337, 337)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('highlighted_card')
                    ->fit(Manipulations::FIT_CROP, 450, 271)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('card')
                    ->fit(Manipulations::FIT_CROP, 278, 133)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('small_card')
                    ->fit(Manipulations::FIT_CROP, 206, 133)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('background_gradient')
                    ->fit(Manipulations::FIT_CROP, 475, 296)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('announcement')
                    ->fit(Manipulations::FIT_CROP, self::ANNOUNCEMENT_WIDTH, 170)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('player_thumb')
                    ->fit(Manipulations::FIT_CROP, 74, 74)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
            });
        $this->addMediaCollection('audio')->singleFile()->acceptsMimeTypes(['audio/mpeg']);
        $this->registerSeoMetaMediaCollection();
    }

    public function thematic(): BelongsTo
    {
        return $this->belongsTo(Thematic::class, 'thematic_id');
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function radioStations(): BelongsToMany
    {
        return $this->belongsToMany(RadioStation::class, 'news_article_radio_station')->withTimestamps();
    }

    public function announcement(): MorphOne
    {
        return $this->morphOne(Announcement::class, 'announceable');
    }

    public function radioStationIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->radioStations->sortBy('name')->pluck('id')->values()->toJson()
        );
    }

    public function hasAudio(): Attribute
    {
        return new Attribute(
            get: fn () => $this->getFirstMedia('audio') !== null
        );
    }

    public function audioStream(): Attribute
    {
        return new Attribute(
            get: fn () => [
                'hls' => null,
                'dash' => null,
                'mp3' => $this->getFirstMediaUrl('audio'),
            ]
        );
    }

    public function locationName(): string
    {
        if (! $this->hasLocation()) {
            return $this->location_name ?? '';
        }

        return $this->_locationName();
    }

    public function toFeedItem(): FeedItem
    {
        $media = $this->getFirstMedia('illustrations');

        return FeedItem::create()
            ->id(ltrim(parse_url(route('app.news.show', $this))['path'], '/'))
            ->title($this->title)
            ->summary(Str::limit(html_entity_decode(strip_tags((new Parsedown())->text($this->description)))))
            ->link(route('app.news.show', $this))
            ->authorName(settings()->email . ' (' . config('app.name') . ')')
            ->category($this->thematic->title ?? 'Actu')
            ->enclosure($media?->getUrl())
            ->enclosureType($media?->mime_type)
            ->enclosureLength($media?->size)
            ->updated($this->updated_at);
    }

    public function scopeActive(Builder $query): void
    {
        $query->where('active', true);
    }

    public function scopePublished(Builder $query): void
    {
        $query->where('published_at', '<=', Date::now());
    }
}

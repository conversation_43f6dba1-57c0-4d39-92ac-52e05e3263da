<?php

namespace App\Models\Logs;

use Illuminate\Database\Eloquent\Model;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\File;

class LogJoinUsFormMessage extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use InteractsWithMedia;

    /** @var string */
    protected $table = 'log_join_us_form_messages';

    /** @var array<int, string> */
    protected $fillable = ['data'];

    /** @var array<string, string> */
    protected $casts = ['data' => 'array'];

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('attachments')
            ->acceptsMimeTypes([
                'image/jpeg',
                'image/png',
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'application/vnd.oasis.opendocument.text',
                'audio/mpeg',
                'application/vnd.rar',
                'application/zip',
            ])
            ->acceptsFile(function (File $file) {
                return $file->size <= 1024 * 1024 * 100;
            })
            ->useDisk('private');
    }
}

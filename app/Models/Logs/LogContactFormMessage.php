<?php

namespace App\Models\Logs;

use Illuminate\Database\Eloquent\Model;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use <PERSON>tie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\File;

class LogContactFormMessage extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use InteractsWithMedia;

    /** @var string */
    protected $table = 'log_contact_form_messages';

    /** @var array<int, string> */
    protected $fillable = ['data'];

    /** @var array<string, string> */
    protected $casts = ['data' => 'array'];

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('attachments_general')
            ->acceptsMimeTypes([
                'image/jpeg',
                'image/png',
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'audio/mpeg',
                'application/vnd.rar',
                'application/zip',
            ])
            ->acceptsFile(function (File $file) {
                return $file->size <= 1024 * 1024 * 10;
            })
            ->useDisk('private');

        $this->addMediaCollection('attachments_audio')
            ->acceptsMimeTypes([
                'audio/vnd.wave',
                'audio/wav',
                'audio/wave',
                'audio/x-pn-wav',
                'audio/x-wav',
                'audio/flac',
                'audio/x-flac',
            ])
            ->acceptsFile(function (File $file) {
                return $file->size <= 1024 * 1024 * 100;
            })
            ->useDisk('private');

        $this->addMediaCollection('attachments_image')
            ->acceptsMimeTypes([
                'image/jpeg',
                'image/png',
            ])
            ->acceptsFile(function (File $file) {
                return $file->size <= 1024 * 1024 * 10;
            })
            ->useDisk('private');

        $this->addMediaCollection('attachments_audio_cover')
            ->acceptsMimeTypes([
                'image/jpeg',
                'image/png',
            ])
            ->acceptsFile(function (File $file) {
                return $file->size <= 1024 * 1024 * 40;
            })
            ->useDisk('private');
        $this->addMediaCollection('attachments_audio_performer_picture')
            ->acceptsMimeTypes([
                'image/jpeg',
                'image/png',
            ])
            ->acceptsFile(function (File $file) {
                return $file->size <= 1024 * 1024 * 40;
            })
            ->useDisk('private');
    }
}

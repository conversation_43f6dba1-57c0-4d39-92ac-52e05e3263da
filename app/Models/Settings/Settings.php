<?php

namespace App\Models\Settings;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Settings extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use InteractsWithMedia;

    /** @var string */
    protected $table = 'settings';

    /** @var array<int, string> */
    protected $fillable = [
        'email',
        'phone_number',
        'address',
        'zip_code',
        'city',
        'studios_contact',
        'facebook_url',
        'twitter_url',
        'instagram_url',
        'linkedin_url',
        'youtube_url',
        'bluesky_url',
        'mastodon_url',
        'newsletter_url',
        'play_store_app_url',
        'app_store_app_url',
        'matomo_url',
        'matomo_id_site',
        'ga4_tracking_id',
        'login_modal_on_init',
        'map_events_week_interval',
        'map_news_week_interval',
        'map_playlists_week_interval',
        'map_podcasts_week_interval',
        'map_performers_week_interval',
    ];

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('logo_squared')
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->singleFile()
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('nav_admin')
                    ->fit(Manipulations::FIT_CROP, 30, 30)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('mail')
                    ->fit(Manipulations::FIT_CROP, 50, 50)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('auth')
                    ->fit(Manipulations::FIT_CROP, 225, 225)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
            });
        $this->addMediaCollection('program_schedule')
            ->acceptsMimeTypes(['application/pdf'])
            ->singleFile();
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    public function fullPostalAddress(): Attribute
    {
        $fullPostalAddress = '';
        $fullPostalAddress .= $this->address ?: '';
        $fullPostalAddress .= $this->zip_code ? ($fullPostalAddress ? ' ' : '') . $this->zip_code : '';
        $fullPostalAddress .= $this->city ? ($fullPostalAddress ? ' ' : '') . $this->city : '';

        return new Attribute(
            get: fn () => $fullPostalAddress
        );
    }
}

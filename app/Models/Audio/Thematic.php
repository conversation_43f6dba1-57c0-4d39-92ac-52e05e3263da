<?php

namespace App\Models\Audio;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Thematic extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use InteractsWithMedia;

    /** @var string */
    protected $table = 'thematics';

    /** @var array<int, string> */
    protected $fillable = ['title'];

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('illustrations')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('card')
                    ->fit(Manipulations::FIT_CROP, 132, 132)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
            });
    }
}

<?php

namespace App\Models\Audio;

use App\Casts\NormalizeString;
use App\Models\Performers\Album;
use App\Models\Performers\Label;
use App\Models\Performers\Performer;
use App\Models\Users\User;
use App\Models\Winmedia\WinmediaPath;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Song extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use InteractsWithMedia;

    public const ES_ACTION_UP_TO_DATE = 'up_to_date';

    public const ES_ACTION_TO_SYNC = 'to_sync';

    public const ES_ACTION_TO_DELETE = 'to_delete';

    public const ES_ACTION_NOT_INDEXED = 'not_indexed';

    public const AUDIO_EXTRACT_STORAGE_DIR = 'winmedia/extraits/';

    public const CATEGORY_PLATEFORME_MUSICS = 386;

    public const CATEGORY_PLATEFORME_SONORES = 2187;

    public const CATEGORY_PLATEFORME_ARCHIVAGE = 0;

    public const WINMEDIA_PRESENCE_IS_PRESENT = 'is_present';

    public const WINMEDIA_PRESENCE_NOT_PRESENT = 'not_present';

    public const WINMEDIA_PRESENCE_TO_TRANSFER = 'to_transfer';

    /** @var bool */
    public $timestamps = false;

    /** @var bool */
    public $incrementing = true;

    /** @var string */
    protected $table = 'winmedia_media';

    /** @var string */
    protected $primaryKey = 'id';

    /** @var array<int, string> */
    protected $fillable = [
        'id',
        'imedia',
        'album_id',
        'performer',
        'title',
        'version',
        'album',
        'duration', // Milliseconds
        'genre',
        'year',
        'publisher',
        'comment',
        'disc',
        'track',
        '_proprietes',
        '_proprietaire',
        'elasticsearch_action',
        '_artistes_similaires',
        'category_plateforme',
        'archivage_created_at',
        'winmedia_presence',
        '_release_date',
        'label_id',
    ];

    protected $with = ['paths'];

    protected $appends = [
        'created_at',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'archivage_created_at' => 'datetime',
        'title' => NormalizeString::class,
        'performer' => NormalizeString::class,
        'album' => NormalizeString::class,
        'version' => NormalizeString::class,
        'publisher' => NormalizeString::class,
        '_artistes_similaires' => NormalizeString::class,
    ];

    protected $hidden = [
        '_chemin_morceau',
        '_image_cover',
        '_image_artiste',
    ];

    protected static function boot(): void
    {
        parent::boot();
        static::addGlobalScope(
            'musicType',
            static fn (Builder $builder) => $builder->whereNull('imedia')
                ->orWhere(function ($query) {
                    $query->where('category_plateforme', '=', self::CATEGORY_PLATEFORME_MUSICS)->whereNotNull('imedia');
                })
        );
        // ToDO: Indique qu'une modification a été faite depuis Laravel pour les musiques Winmedia
        // (nécessite de répercuter le changement dans Winmedia)
        static::updating(function (Song $model) {
            if ($model->isWinmedia) {
                //$model->winmedia_presence = self::WINMEDIA_PRESENCE_TO_TRANSFER;
            }
        });
    }

    public function paths(): HasMany
    {
        return $this->hasMany(WinmediaPath::class, 'media', 'imedia');
    }

    public function playlists(): BelongsToMany
    {
        return $this->belongsToMany(Playlist::class, 'playlists_songs', 'song_id', 'playlist_id')
            ->withPivot('index')
            ->orderBy('playlists_songs.index')
            ->withTimestamps();
    }

    // Utiliser avec withCount : $song = Song::withCount('favoriteUsers')->find(197); $song->favorite_users_count;
    public function favoriteUsers(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'users_favorite_songs', 'song_id', 'user_id')
            ->orderByDesc('users_favorite_songs.updated_at')
            ->withTimestamps();
    }

    public function performerRelationship(): HasOneThrough
    {
        return $this->hasOneThrough(Performer::class, Album::class, 'id', 'id', 'album_id', 'performer_id');
    }

    public function albumRelationship(): BelongsTo
    {
        return $this->belongsTo(Album::class, 'album_id', 'id');
    }

    public function labelRelationship(): BelongsTo
    {
        return $this->belongsTo(Label::class, 'label_id', 'id');
    }

    public function humanReadableDuration(): Attribute
    {
        $start = Date::now();

        return new Attribute(
            get: fn () => $start
                ->addMilliseconds($this->duration)
                ->diffForHumans($start, CarbonInterface::DIFF_ABSOLUTE, true, 2)
        );
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover_source')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png']);
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb_square')
            ->fit(Manipulations::FIT_CROP, 300, 300)
            ->format(Manipulations::FORMAT_JPG);
        $this->addMediaConversion('middle_square')
            ->fit(Manipulations::FIT_CROP, 600, 600)
            ->format(Manipulations::FORMAT_JPG);
    }

    public function coverThumb(): Attribute
    {
        $defaultCover = 'noimage.jpg';
        if ($this->category_plateforme === self::CATEGORY_PLATEFORME_SONORES) {
            $defaultCover = 'noimagesonore.jpg';
        }

        return new Attribute(
            get: fn () => Storage::exists('public/winmedia/pochettes_thumbnail/' . $this->id . '.jpg')
                ? asset('storage/winmedia/pochettes_thumbnail/' . $this->id . '.jpg')
                : asset('storage/winmedia/pochettes_thumbnail/' . $defaultCover)
        );
    }

    public function coverMiddle(): Attribute
    {
        $defaultCover = 'noimage.jpg';
        if ($this->category_plateforme === self::CATEGORY_PLATEFORME_SONORES) {
            $defaultCover = 'noimagesonore.jpg';
        }

        return new Attribute(
            get: fn () => Storage::exists('public/winmedia/pochettes_middle/' . $this->id . '.jpg')
                ? asset('storage/winmedia/pochettes_middle/' . $this->id . '.jpg')
                : asset('storage/winmedia/pochettes_middle/' . $defaultCover)
        );
    }

    public function coverFull(): Attribute
    {
        $defaultCover = 'noimage.jpg';
        if ($this->category_plateforme === self::CATEGORY_PLATEFORME_SONORES) {
            $defaultCover = 'noimagesonore.jpg';
        }

        return new Attribute(
            get: fn () => Storage::exists('public/winmedia/pochettes_brut/' . $this->id . '.jpg')
                ? asset('storage/winmedia/pochettes_brut/' . $this->id . '.jpg')
                : asset('storage/winmedia/pochettes_brut/' . $defaultCover)
        );
    }

    public function audioStream(): Attribute
    {
        if (! Auth::check()) {
            return new Attribute(
                get: fn () => [
                    'hls' => null,
                    'dash' => null,
                    'mp3' => asset('storage/' . self::AUDIO_EXTRACT_STORAGE_DIR . $this->id . '.mp3'),
                ]
            );
        }

        $streamQuality = Auth::user()->settings->use_flac ? 'HD' : 'SD';

        return new Attribute(
            get: fn () => [
                'hls' => config('services.streaming.url') . "/$streamQuality/$this->id.m4a/playlist.m3u8",
                'dash' => config('services.streaming.url') . "/$streamQuality/$this->id.m4a/manifest.mpd",
                'mp3' => asset('storage/' . self::AUDIO_EXTRACT_STORAGE_DIR . $this->id . '.mp3'),
            ]
        );
    }

    public function createdAt(): Attribute
    {
        return new Attribute(
            get: fn () => $this->imedia !== null ? $this->paths->where('extension', 0)->first()?->modify : ($this->archivage_created_at ?? null)
        );
    }

    public function yearFourDigit(): Attribute
    {
        $yearFourDigit = null;

        if ($this->_release_date && preg_match('/^\d{4}-\d{2}-\d{2}$/', $this->_release_date)) {
            try {
                $yearFourDigit = Date::parse($this->_release_date)->year;
            } catch (\Exception) {
                $yearFourDigit = null;
            }
        }

        if ($this->year && ! $yearFourDigit) {
            try {
                if (Str::length($this->year) === 4) {
                    $yearFourDigit = $this->year;
                } elseif (Str::length($this->year) > 4) {
                    $yearFourDigit = Date::parse($this->year)->year;
                }
            } catch (\Exception) {
                $yearFourDigit = null;
            }
        }

        return new Attribute(
            get: fn () => (int) $yearFourDigit
        );
    }

    // Musiques Winmedia + Musiques Archivage
    public function isMusic(): Attribute
    {
        return new Attribute(
            get: fn () => (
                $this->category_plateforme === self::CATEGORY_PLATEFORME_MUSICS
                && $this->imedia !== null
            ) || $this->imedia === null
        );
    }

    // Sonores Winmedia
    public function isSonore(): Attribute
    {
        return new Attribute(
            get: fn () => $this->category_plateforme === self::CATEGORY_PLATEFORME_SONORES && $this->imedia !== null
        );
    }

    // Musiques Winmedia + Sonores Winmedia
    public function isWinmedia(): Attribute
    {
        return new Attribute(
            get: fn () => $this->imedia !== null
        );
    }

    // Musiques Winmedia
    public function isMusicWinmedia(): Attribute
    {
        return new Attribute(
            get: fn () => $this->category_plateforme === self::CATEGORY_PLATEFORME_MUSICS && $this->imedia !== null
        );
    }

    // Musiques Archivage
    public function isMusicArchivage(): Attribute
    {
        return new Attribute(
            get: fn () => $this->imedia === null
        );
    }

    // Musiques Winmedia + Musiques Archivage
    public function scopeMusicType(Builder $builder): void
    {
        $builder->withoutGlobalScope('musicType')
            ->whereNull('imedia')
            ->orWhere(function ($query) {
                $query->where('category_plateforme', '=', self::CATEGORY_PLATEFORME_MUSICS)->whereNotNull('imedia');
            });
    }

    // Sonores Winmedia
    public function scopeSonoreType(Builder $builder): void
    {
        $builder->withoutGlobalScope('musicType')
            ->where('category_plateforme', '=', self::CATEGORY_PLATEFORME_SONORES)
            ->whereNotNull('imedia');
    }

    // Musiques Winmedia + Sonores Winmedia
    public function scopeWinmediaType(Builder $builder): void
    {
        $builder->withoutGlobalScope('musicType')
            ->whereNotNull('imedia');
    }

    // Musiques Winmedia
    public function scopeMusicWinmediaType(Builder $builder): void
    {
        $builder->withoutGlobalScope('musicType')
            ->where('category_plateforme', '=', self::CATEGORY_PLATEFORME_MUSICS)
            ->whereNotNull('imedia');
    }

    // Musiques Archivage
    public function scopeMusicArchivageType(Builder $builder): void
    {
        $builder->withoutGlobalScope('musicType')
            ->whereNull('imedia');
    }

    // Musiques Winmedia + Sonores Winmedia + Musiques Archivage
    public function scopeAllTypes(Builder $builder): void
    {
        $builder->withoutGlobalScope('musicType');
    }

    // Nouveautés
    public function scopeNews(Builder $builder): void
    {
        $builder->where('_proprietes', config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE', '%/Nouveauté/%')
            ->whereRaw("_release_date ~ '^\d{4}-\d{2}-\d{2}$'")
            ->whereNotNull('_release_date')
            ->where('_release_date', '<=', now()->format('Y-m-d'))
            ->orderByDesc('_release_date');
    }

    // Grand Ouest (local)
    public function scopeLocalGO(Builder $builder): void
    {
        $builder->where('language', '=', 'Grand Ouest')
            ->orWhere('_proprietes', config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE', '%grand ouest%')
            ->orWhere('_proprietes', config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE', '%local%');
    }
}

<?php

namespace App\Models\Audio;

use App\Models\Radio\RadioStation;
use App\Models\Traits\HasLocation;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Facades\Date;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Playlist extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use HasLocation;
    use InteractsWithMedia;

    public const ANNOUNCEMENT_WIDTH = 170;

    /** @var string */
    protected $table = 'playlists';

    /** @var array<string, string> */
    protected $casts = [
        'published_at' => 'datetime',
        'unpublished_at' => 'datetime',
        'active' => 'boolean',
    ];

    /** @var array<int, string> */
    protected $fillable = [
        'user_id',
        'thematic_id',
        'title',
        'tags',
        'rail_displayed',
        'description',
        'active',
        'published_at',
        'unpublished_at',
    ];

    protected static function booted(): void
    {
        static::deleting(function (Playlist $playlist) {
            $playlist->contentLocations()->delete();
        });
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('seo')
                    ->fit(Manipulations::FIT_CROP, 600, 600)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('patchwork')
                    ->fit(Manipulations::FIT_CROP, 300, 300)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('medium')
                    ->fit(Manipulations::FIT_CROP, 122, 122)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('announcement')
                    ->fit(Manipulations::FIT_CROP, self::ANNOUNCEMENT_WIDTH, 170)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
            });
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    public function thematic(): BelongsTo
    {
        return $this->belongsTo(Thematic::class, 'thematic_id', 'id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function radioStations(): BelongsToMany
    {
        return $this->belongsToMany(
            RadioStation::class,
            'playlists_radio_stations',
            'playlist_id',
            'radio_station_id'
        )->withTimestamps();
    }

    public function songs(): BelongsToMany
    {
        return $this->belongsToMany(Song::class, 'playlists_songs', 'playlist_id', 'song_id')
            ->withPivot('index')
            ->orderBy('playlists_songs.index')
            ->withTimestamps();
    }

    public function songsAllTypes(): BelongsToMany
    {
        return $this->belongsToMany(Song::class, 'playlists_songs', 'playlist_id', 'song_id')
            ->allTypes()
            ->withPivot('index')
            ->orderBy('playlists_songs.index')
            ->withTimestamps();
    }

    public function radioStationIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->radioStations->sortBy('name')->pluck('id')->values()->toJson()
        );
    }

    public function scopeActive(Builder $query): void
    {
        $query->where('active', true);
    }

    public function scopePublic(Builder $query): void
    {
        $query->whereNull('user_id');
    }

    public function scopePublished(Builder $query): void
    {
        $query->where(function (Builder $query) {
            $query->where('published_at', '<=', Date::now())
                ->where(function (Builder $query) {
                    $query->whereNull('unpublished_at')
                        ->orWhere('unpublished_at', '>', Date::now());
                });
        });
    }
}

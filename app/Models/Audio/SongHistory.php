<?php

namespace App\Models\Audio;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class SongHistory extends Model
{
    use HasFactory;

    /** @var bool */
    public $timestamps = false;

    /** @var bool */
    public $incrementing = false;

    /** @var string */
    protected $table = 'winmedia_historique';

    /** @var string */
    protected $primaryKey = 'imedia';

    /** @var array<int, string> */
    protected $fillable = ['imedia', 'dateheure_diffusion'];

    /** @var array<string, string> */
    protected $casts = ['dateheure_diffusion' => 'datetime'];

    public function song(): BelongsTo
    {
        return $this->belongsTo(Song::class, 'imedia', 'imedia');
    }
}

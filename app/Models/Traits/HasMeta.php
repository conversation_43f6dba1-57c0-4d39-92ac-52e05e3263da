<?php

namespace App\Models\Traits;

use Illuminate\Foundation\Http\FormRequest;
use Plank\Metable\Metable;

trait HasMeta
{
    use Metable {
        getMeta as traitGetMeta;
    }

    public function saveMetaFromRequest(FormRequest $request, array $metaKeys): void
    {
        foreach ($metaKeys as $metaKey) {
            $this->removeMeta($metaKey);
            if ($request->safe()->offsetExists($metaKey)) {
                $this->setMeta($metaKey, $request->safe()->offsetGet($metaKey));
            }
        }
    }

    public function getMeta(string $key, $default = null): array|string|null
    {
        return $this->traitGetMeta($key, $default);
    }
}

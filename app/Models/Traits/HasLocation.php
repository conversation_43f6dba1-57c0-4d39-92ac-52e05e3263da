<?php

namespace App\Models\Traits;

use App\Models\Map\ContentLocation;
use App\Models\Map\Place;
use App\Models\Map\Point;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HasLocation
{
    public function contentLocations(): MorphMany
    {
        return $this->morphMany(ContentLocation::class, 'content');
    }

    public function hasLocation(): bool
    {
        return $this->contentLocations->isNotEmpty();
    }

    public function location(): Place|Point|null
    {
        $this->loadLocationsFully();

        $contentLocation = $this->contentLocations->first(
            static fn (ContentLocation $item): bool => $item->location_type === Place::class
        );

        if ($contentLocation) {
            /** @var Place */
            return $contentLocation->location;
        }

        $contentLocation = $this->contentLocations->first(
            static fn (ContentLocation $item): bool => $item->location_type === Point::class
        );

        /** @var Point|null */
        return $contentLocation?->location;
    }

    public function locationName(): string
    {
        $location = $this->location();

        return match (true) {
            $location instanceof Place => $location->name,
            $location instanceof Point => $location->former_location_name ?? '',
            // @phpstan-ignore-next-line
            default => '',
        };
    }

    public function place(): ?Place
    {
        $this->loadLocationsFully();

        $contentLocation = $this->contentLocations->first(
            static fn (ContentLocation $item): bool => $item->location_type === Place::class
        );

        /** @var Place|null */
        return $contentLocation?->location;
    }

    public function point(): ?Point
    {
        if ($this->place()?->currentPoint) {
            /** @var Point */
            return $this->place()->currentPoint;
        }

        $contentLocation = $this->contentLocations->first(
            static fn (ContentLocation $item): bool => $item->location_type === Point::class
        );

        /** @var Point|null */
        return $contentLocation?->location;
    }

    private function loadLocationsFully(): void
    {
        $notFullyLoaded = false;

        foreach ($this->contentLocations as $contentLocation) {
            if (! $contentLocation->relationLoaded('location')) {
                $notFullyLoaded = true;
                break;
            }
        }

        if ($notFullyLoaded) {
            $this->contentLocations
                ->loadMissing('location')
                // @phpstan-ignore-next-line
                ->loadMorph('location', [Place::class => ['currentPoint']]);
        }
    }
}

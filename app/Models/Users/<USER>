<?php

namespace App\Models\Users;

use App\Models\Audio\Playlist;
use App\Models\Audio\Song;
use App\Models\Radio\Program;
use App\Models\Teams\Team;
use App\Models\UserSettings\UserSetting;
use App\Notifications\InitializePassword;
use App\Notifications\ResetPassword;
use App\Notifications\VerifyEmail;
use Carbon\CarbonInterface;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Fortify\TwoFactorAuthenticatable;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Spatie\WelcomeNotification\ReceivesWelcomeNotification;

class User extends Authenticatable implements HasMedia, MustVerifyEmail
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use InteractsWithMedia;
    use Notifiable;
    use ReceivesWelcomeNotification;
    use TwoFactorAuthenticatable;

    /** @var array<int, string> */
    protected $fillable = ['team_id', 'username', 'email', 'password', 'address', 'city', 'birth_date'];

    /** @var array<int, string> */
    protected $hidden = ['password', 'remember_token', 'two_factor_recovery_codes', 'two_factor_secret'];

    /** @var array<string, string> */
    protected $casts = ['birth_date' => 'date'];

    public static function boot(): void
    {
        parent::boot();
        static::created(function (User $model) {
            UserSetting::create(['user_id' => $model->id]);
            UserJourney::create(['user_id' => $model->id]);
        });
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('profile_picture')
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->singleFile()
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('top_nav_admin')
                    ->fit(Manipulations::FIT_CROP, 20, 20)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('top_nav_front_mobile')
                    ->fit(Manipulations::FIT_CROP, 32, 32)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('top_nav_front_desktop')
                    ->fit(Manipulations::FIT_CROP, 40, 40)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('profile_front')
                    ->fit(Manipulations::FIT_CROP, 142, 142)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('dedication_thumb_front')
                    ->fit(Manipulations::FIT_CROP, 20, 20)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('dedication_thumb_front_card')
                    ->fit(Manipulations::FIT_CROP, 32, 32)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('dedication_thumb_front_mobile')
                    ->fit(Manipulations::FIT_CROP, 50, 50)
                    ->format(Manipulations::FORMAT_WEBP);
            });
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class);
    }

    public function settings(): HasOne
    {
        return $this->hasOne(UserSetting::class);
    }

    public function journey(): HasOne
    {
        return $this->hasOne(UserJourney::class);
    }

    public function subscribedPrograms(): BelongsToMany
    {
        return $this->belongsToMany(Program::class, 'user_subscribed_programs', 'user_id', 'program_id')
            ->withTimestamps();
    }

    public function favoriteSongs(): BelongsToMany
    {
        return $this->belongsToMany(Song::class, 'users_favorite_songs', 'user_id', 'song_id')
            ->orderByDesc('users_favorite_songs.updated_at')
            ->withTimestamps();
    }

    public function playlists(): HasMany
    {
        return $this->HasMany(Playlist::class, 'user_id')->orderByDesc('updated_at');
    }

    public function sendPasswordResetNotification($token): void
    {
        $this->notify((new ResetPassword($token))->locale(app()->getLocale()));
    }

    public function sendEmailVerificationNotification(): void
    {
        $this->notify((new VerifyEmail())->locale(app()->getLocale()));
    }

    public function sendWelcomeNotification(CarbonInterface $validUntil): void
    {
        $this->notify((new InitializePassword($validUntil))->locale(app()->getLocale()));
    }
}

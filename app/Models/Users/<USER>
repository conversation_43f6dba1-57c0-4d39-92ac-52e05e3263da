<?php

namespace App\Models\Users;

use App\Models\Radio\RadioStation;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserJourney extends Model
{
    use HasFactory;

    /** @var array<int, string> */
    protected $fillable = [
        'user_id',
        'session_id',
        'selected_radio_station_universe_id',
        'current_route_key',
        'played_audio_source_class',
        'played_audio_source_id',
        'played_audio_source_params',
        'played_sub_audio_source_class',
        'played_sub_audio_source_id',
        'player_audio_stream_is_playing',
        'selected_default_setting_radio_station_id',
        'selected_default_setting_webradio_id',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'played_audio_source_params' => 'json',
        'player_audio_stream_is_playing' => 'boolean',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function selectedRadioStation(): BelongsTo
    {
        return $this->belongsTo(RadioStation::class, 'selected_radio_station_universe_id');
    }

    public function selectedDefaultSettingRadioStation(): BelongsTo
    {
        return $this->belongsTo(RadioStation::class, 'selected_default_setting_radio_station_id');
    }

    public function selectedDefaultSettingWebradio(): BelongsTo
    {
        return $this->belongsTo(RadioStation::class, 'selected_default_setting_webradio_id');
    }
}

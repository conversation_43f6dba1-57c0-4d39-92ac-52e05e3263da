<?php

namespace App\Models\Announcements;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\Map\Point;
use App\Models\News\NewsArticle;
use App\Models\Radio\Program;
use App\Models\Radio\RadioStation;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @property Playlist|Podcast|Event|NewsArticle|Program|Point $announceable
 */
class Announcement extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use InteractsWithMedia;
    use SoftDeletes;

    public const ANNOUNCEMENT_WIDTH = 216;

    public const ANNOUNCEABLES = [
        NewsArticle::class => 'Actualité',
        Event::class => 'Évènement',
        Program::class => 'Émission',
        Podcast::class => 'Podcast',
        Playlist::class => 'Playlist',
        Point::class => 'Map (Points)',
    ];

    /** @var string */
    protected $table = 'announcements';

    /** @var array<int, string> */
    protected $fillable = [
        'title',
        'description',
        'announceable_type',
        'announceable_id',
        'subtitle',
        'published_at',
        'unpublished_at',
        'active',
        'url',
    ];

    /** @var array<string, string> */
    protected $casts = ['published_at' => 'datetime', 'unpublished_at' => 'datetime', 'active' => 'boolean'];

    public function radioStations(): BelongsToMany
    {
        return $this->belongsToMany(RadioStation::class, 'announcements_radio_stations')->withTimestamps();
    }

    public function announceable(): MorphTo
    {
        return $this->morphTo();
    }

    public function displayableMedia(): Attribute
    {
        return new Attribute(
            get: fn () => match ($this->announceable::class) {
                NewsArticle::class => $this->announceable->getFirstMedia('illustrations'),
                Event::class,
                Program::class => $this->announceable->getFirstMedia('cover'),
                Podcast::class => $this->announceable->getFirstMedia('cover'),
                Playlist::class => $this->announceable->getFirstMedia('cover'),
                Point::class => $this->getFirstMedia('cover'),
                default => null,
            }
        );
    }

    public function displayableMediaSizes(): Attribute
    {
        return new Attribute(
            get: fn () => match ($this->announceable::class) {
                NewsArticle::class => NewsArticle::ANNOUNCEMENT_WIDTH . 'px',
                Event::class => Event::ANNOUNCEMENT_WIDTH . 'px',
                Program::class => Program::ANNOUNCEMENT_WIDTH . 'px',
                Podcast::class => Podcast::ANNOUNCEMENT_WIDTH . 'px',
                Playlist::class => Playlist::ANNOUNCEMENT_WIDTH . 'px',
                Point::class => self::ANNOUNCEMENT_WIDTH . 'px',
                default => self::ANNOUNCEMENT_WIDTH . 'px',
            }
        );
    }

    public function displayableTitle(): Attribute
    {
        return new Attribute(
            get: fn () => $this->getRawOriginal('title') ?: $this->announceable->title
        );
    }

    public function displayableDescription(): Attribute
    {
        return new Attribute(
            get: fn () => $this->getRawOriginal('description') ?: $this->announceable->description
        );
    }

    public function hasAudio(): Attribute
    {
        return new Attribute(
            get: fn () => match ($this->announceable::class) {
                NewsArticle::class => $this->announceable->getFirstMedia('audio') !== null,
                Event::class => $this->announceable->getFirstMedia('audio') !== null,
                Program::class => true,
                Podcast::class => $this->announceable->getFirstMedia('audio') !== null || $this->announceable->winmedia_audio_source_uploaded,
                Playlist::class => true,
                Point::class => false,
                default => false,
            }
        );
    }

    public function radioStationIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->radioStations->pluck('id')->values()->toJson()
        );
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('seo')
                    ->fit(Manipulations::FIT_CROP, 600, 600)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('background_gradient')
                    ->fit(Manipulations::FIT_CROP, 475, 296)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('player_mobile')
                    ->fit(Manipulations::FIT_CROP, 337, 337)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('card')
                    ->fit(Manipulations::FIT_CROP, 125, 177)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('agenda_card')
                    ->fit(Manipulations::FIT_CROP, 200, 283)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('announcement')
                    ->fit(Manipulations::FIT_CROP, self::ANNOUNCEMENT_WIDTH, 170)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('player_thumb')
                    ->fit(Manipulations::FIT_CROP, 74, 74)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
            });
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }
}

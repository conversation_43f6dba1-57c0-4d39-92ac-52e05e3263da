<?php

namespace App\Models\Radio;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class ProgramRecurrence extends Model
{
    use HasFactory;

    public const BROADCAST_WEEK = [1 => 'Toutes les semaines', 2 => 'Semaines paires', 3 => 'Semaines impaires'];

    /** @var string */
    protected $table = 'program_recurrences';

    /** @var array<int, string> */
    protected $fillable = [
        'label',
        'program_id',
        'months',
        'month_days',
        'week_days',
        'time',
        'local_time',
        'local_time_tz',
        'month_week',
        'broadcast_week',
        'holidays_break',
    ];

    /** @var array<string, string> */
    protected $casts = [
        'months' => 'json',
        'month_days' => 'json',
        'week_days' => 'json',
        'time' => 'datetime:H:i',
        'local_time' => 'datetime:H:i',
        'month_week' => 'json',
        'holidays_break' => 'boolean',
    ];

    public function program(): BelongsTo
    {
        return $this->belongsTo(Program::class, 'program_id');
    }

    public function subPrograms(): BelongsToMany
    {
        return $this->belongsToMany(
            Program::class,
            'program_recurrences_sub_programs',
            'program_recurrence_id',
            'sub_program_id'
        )->orderBy('local_time')->withPivot(['time', 'local_time', 'local_time_tz'])->withTimestamps();
    }

    public function radioStations(): BelongsToMany
    {
        return $this->belongsToMany(
            RadioStation::class,
            'program_recurrences_radio_stations',
            'program_recurrence_id',
            'radio_station_id'
        )->withTimestamps();
    }

    public function subProgramIds(): Attribute
    {
        return new Attribute(
            get: fn () => $this->subPrograms->pluck('id')->toArray()
        );
    }

    public function radioStationIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->radioStations->sortBy('name')->pluck('id')->values()->toJson()
        );
    }

    public function monthsString(): Attribute
    {
        return new Attribute(
            get: fn () => json_encode($this->months, JSON_THROW_ON_ERROR)
        );
    }

    public function monthDaysString(): Attribute
    {
        return new Attribute(
            get: fn () => json_encode($this->month_days, JSON_THROW_ON_ERROR)
        );
    }

    public function weekDaysString(): Attribute
    {
        return new Attribute(
            get: fn () => json_encode($this->week_days, JSON_THROW_ON_ERROR)
        );
    }

    public function monthWeekString(): Attribute
    {
        return new Attribute(
            get: fn () => json_encode($this->month_week, JSON_THROW_ON_ERROR)
        );
    }
}

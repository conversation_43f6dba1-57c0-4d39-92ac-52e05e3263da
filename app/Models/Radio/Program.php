<?php

namespace App\Models\Radio;

use App\Models\Announcements\Announcement;
use App\Models\Audio\Podcast;
use App\Models\Audio\Thematic;
use App\Models\Traits\HasLocation;
use App\Models\Users\User;
use Carbon\CarbonInterface;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Support\Facades\Date;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class Program extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use HasLocation;
    use InteractsWithMedia;

    public const ANNOUNCEMENT_WIDTH = 170;

    /** @var string */
    protected $table = 'programs';

    /** @var array<int, string> */
    protected $fillable = [
        'main_program_id',
        'thematic_id',
        'title',
        'tags',
        'description',
        'duration', // Seconds
        'start_podcast_artist',
        'start_podcast_title',
        'start_podcast_version',
        'end_podcast_artist',
        'end_podcast_title',
        'end_podcast_version',
        'theorical_timing_live_display',
    ];

    protected static function booted(): void
    {
        static::deleting(function (Program $program) {
            $program->contentLocations()->delete();
        });
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('seo')
                    ->fit(Manipulations::FIT_CROP, 600, 600)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('card')
                    ->fit(Manipulations::FIT_CROP, 122, 122)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('announcement')
                    ->fit(Manipulations::FIT_CROP, self::ANNOUNCEMENT_WIDTH, 170)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('itunes')
                    ->fit(Manipulations::FIT_CROP, 1400, 1400)
                    ->format(Manipulations::FORMAT_JPG);
                $this->addMediaConversion('patchwork')
                    ->fit(Manipulations::FIT_CROP, 228, 228)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('large')
                    ->fit(Manipulations::FIT_CROP, 172, 172)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('medium')
                    ->fit(Manipulations::FIT_CROP, 122, 122)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
            });
    }

    public function thematic(): BelongsTo
    {
        return $this->belongsTo(Thematic::class, 'thematic_id', 'id');
    }

    public function authors(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'programs_authors', 'program_id', 'user_id')
            ->withPivot('index')
            ->orderBy('programs_authors.index')
            ->withTimestamps();
    }

    public function mainProgram(): BelongsTo
    {
        return $this->belongsTo(self::class, 'main_program_id');
    }

    public function subPrograms(): HasMany
    {
        return $this->hasMany(self::class, 'main_program_id');
    }

    public function recurrences(): HasMany
    {
        return $this->hasMany(ProgramRecurrence::class, 'program_id');
    }

    public function podcasts(): HasMany
    {
        return $this->hasMany(Podcast::class, 'program_id');
    }

    public function announcement(): MorphOne
    {
        return $this->morphOne(Announcement::class, 'announceable');
    }

    public function authorsIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->authors->sortBy('index')->pluck('id')->values()->toJson()
        );
    }

    public function humanReadableDuration(): Attribute
    {
        $start = Date::now();

        return new Attribute(
            get: fn () => $start
                ->addSeconds($this->duration)
                ->diffForHumans($start, CarbonInterface::DIFF_ABSOLUTE, true, 2)
        );
    }
}

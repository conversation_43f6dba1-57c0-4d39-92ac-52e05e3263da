<?php

namespace App\Models\Radio;

use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\Traits\HasLocation;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Auth;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class RadioStation extends Model implements HasMedia, Sortable
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use HasLocation;
    use InteractsWithMedia;
    use SortableTrait;

    public array $sortable = ['order_column_name' => 'position', 'sort_when_creating' => true];

    /** @var string */
    protected $table = 'radio_stations';

    /** @var array<int, string> */
    protected $fillable = [
        'name',
        'label',
        'winmedia_id',
        'stream_url_ld',
        'stream_url_hd',
        'color',
        'active',
        'position',
        'map_events_week_interval',
        'map_news_week_interval',
        'map_playlists_week_interval',
        'map_podcasts_week_interval',
        'map_performers_week_interval',
    ];

    /** @var array<string, string> */
    protected $casts = ['active' => 'boolean'];

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('cover')
            ->singleFile()
            ->acceptsMimeTypes(['image/webp', 'image/jpeg', 'image/png'])
            ->registerMediaConversions(function (?Media $media = null) {
                $this->addMediaConversion('universe_selector')
                    ->fit(Manipulations::FIT_CROP, 24, 24)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('stream_selector')
                    ->fit(Manipulations::FIT_CROP, 48, 48)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('player_mobile')
                    ->fit(Manipulations::FIT_CROP, 337, 337)
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('small')
                    ->fit(Manipulations::FIT_CROP, 74, 74)
                    ->withResponsiveImages()
                    ->format(Manipulations::FORMAT_WEBP);
                $this->addMediaConversion('card')
                    ->fit(Manipulations::FIT_CROP, 140, 140)
                    ->format(Manipulations::FORMAT_WEBP);
            });
    }

    public function broadcasts(): HasMany
    {
        return $this->hasMany(LiveBroadcasting::class, 'winmedia_radio_station_id', 'winmedia_id')
            ->orderByDesc('started_at');
    }

    public function latestBroadcast(): HasOne
    {
        return $this->hasOne(LiveBroadcasting::class, 'winmedia_radio_station_id', 'winmedia_id')
            ->with(['song', 'podcast'])
            ->latestOfMany('started_at');
    }

    public function podcasts(): BelongsToMany
    {
        return $this->belongsToMany(Podcast::class, 'podcasts_radio_stations')->withTimestamps();
    }

    public function events(): BelongsToMany
    {
        return $this->belongsToMany(Event::class, 'event_radio_station');
    }

    public function audioStream(): Attribute
    {
        return new Attribute(
            get: fn () => [
                'hls' => null,
                'dash' => null,
                'mp3' => Auth::user()?->settings->use_flac ? $this->stream_url_hd : $this->stream_url_ld,
            ]
        );
    }
}

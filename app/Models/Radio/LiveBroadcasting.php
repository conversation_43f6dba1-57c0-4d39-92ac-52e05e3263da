<?php

namespace App\Models\Radio;

use App\Models\Audio\Podcast;
use App\Models\Audio\Song;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class LiveBroadcasting extends Model
{
    use HasFactory;

    /** @var string */
    protected $table = 'live_broadcastings';

    /** @var array<int, string> */
    protected $fillable = [
        'winmedia_radio_station_id',
        'song_id',
        'podcast_id',
        'dedication_user_id',
        'real_duration',
        'started_at',
    ];

    /** @var array<string, string> */
    protected $casts = ['started_at' => 'datetime'];

    public function song(): BelongsTo
    {
        return $this->belongsTo(Song::class, 'song_id', 'id')->withCount('favoriteUsers')->withoutGlobalScope('musicType');
    }

    public function podcast(): BelongsTo
    {
        return $this->belongsTo(Podcast::class, 'podcast_id');
    }

    public function dedicationUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'dedication_user_id');
    }
}

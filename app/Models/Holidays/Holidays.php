<?php

namespace App\Models\Holidays;

use App\Models\Radio\Program;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Holidays extends Model
{
    use HasFactory;

    /** @var string */
    protected $table = 'holidays';

    /** @var array<int, string> */
    protected $fillable = ['label', 'started_at', 'ended_at', 'active'];

    /** @var array<string, string> */
    protected $casts = ['started_at' => 'date', 'ended_at' => 'date', 'active' => 'boolean'];

    public function programs(): BelongsToMany
    {
        return $this->belongsToMany(Program::class, 'holidays_programs', 'holiday_id', 'program_id')->withTimestamps();
    }

    public function programsString(): Attribute
    {
        return new Attribute(
            get: fn () => $this->programs->sortBy('title')->pluck('id')->values()->toJson()
        );
    }
}

<?php

namespace App\Models\PageContents;

use App\Brickables\Carousel;
use App\Models\Traits\HasSeoMeta;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Okipa\LaravelBrickables\Contracts\HasBrickables;
use <PERSON>ipa\LaravelBrickables\Traits\HasBrickablesTrait;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PageContent extends Model implements HasBrickables, HasMedia
{
    use ExtendsMediaAbilities;
    use HasBrickablesTrait;
    use HasFactory;
    use HasSeoMeta;
    use InteractsWithMedia;

    public array $brickables = [
        'number_of_bricks' => [
            Carousel::class => ['max' => 1],
        ],
    ];

    /** @var string */
    protected $table = 'page_contents';

    /** @var array<int, string> */
    protected $fillable = ['unique_key'];

    /** @SuppressWarnings(PHPMD.UnusedFormalParameter) */
    public function registerMediaCollections(): void
    {
        $this->registerSeoMetaMediaCollection();
    }

    /**
     * @throws \Spatie\Image\Exceptions\InvalidManipulation
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
    }
}

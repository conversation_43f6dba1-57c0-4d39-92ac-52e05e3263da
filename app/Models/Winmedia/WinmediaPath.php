<?php

namespace App\Models\Winmedia;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class WinmediaPath extends Model
{
    use HasFactory;

    /** @var bool */
    public $timestamps = false;

    /** @var bool */
    public $incrementing = false;

    /** @var string */
    protected $table = 'winmedia_path';

    /** @var array<int, string> */
    protected $fillable = ['ipath', 'media', 'extension', 'modify'];

    /** @var array<string, string> */
    protected $casts = ['modify' => 'datetime'];
}

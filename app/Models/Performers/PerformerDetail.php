<?php

namespace App\Models\Performers;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class PerformerDetail extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use HasFactory;
    use InteractsWithMedia;

    /** @var string */
    protected $table = 'performer_details';

    protected $fillable = [
        'performer_id',
        'performer_name',
        'musicbrainz_id',
        'musicbrainz_type',
        'biography',
        'alias_name',
        'begin_city',
        'current_city',
        'begin_date',
        'end_date',
        'genres',
        'url_web',
        'url_wiki',
        'url_bandcamp',
        'url_discogs',
        'url_instagram',
        'url_facebook',
        'url_youtube',
    ];

    public function performer(): BelongsTo
    {
        return $this->belongsTo(Performer::class);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('artist_picture_source')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png']);
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('jpg_conversion')
            ->format(Manipulations::FORMAT_JPG);
    }

    public static function allGenresJsonOptions(): string
    {
        return self::pluck('genres')
            ->flatMap(function ($genres) {
                return array_map('trim', explode(',', $genres));
            })
            ->filter(function ($genre) {
                return $genre !== '';
            })
            ->unique()
            ->sort()
            ->values()
            ->map(fn ($genre) => [
                'value' => $genre,
                'label' => $genre,
            ])
            ->toJson();
    }
}

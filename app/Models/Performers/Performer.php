<?php

namespace App\Models\Performers;

use App\Casts\NormalizeString;
use App\Models\Audio\Song;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Performer extends Model
{
    use HasFactory;

    /** @var string */
    protected $table = 'performers';

    /** @var array<int, string> */
    protected $fillable = ['name'];

    /** @var array<int, string> */
    protected $with = ['detail', 'albums'];

    /** @var array<string, string> */
    protected $casts = [
        'name' => NormalizeString::class,
    ];

    public static function boot(): void
    {
        parent::boot();
        static::created(function (Performer $model) {
            PerformerDetail::create([
                'performer_id' => $model->id,
                'performer_name' => $model->name,
            ]);
        });

        static::updating(function (Performer $model) {
            $model->detail()->update([
                'performer_name' => $model->name,
            ]);
        });
    }

    public function detail(): HasOne
    {
        return $this->hasOne(PerformerDetail::class);
    }

    public function members(): BelongsToMany
    {
        return $this->belongsToMany(Member::class, 'performers_members', 'performer_id', 'member_id')
            ->using(PerformerMember::class)
            ->withPivot('id', 'index', 'roles', 'begin_date', 'end_date')
            ->orderBy('performers_members.index')
            ->withTimestamps();
    }

    public function songs(): HasManyThrough
    {
        return $this->hasManyThrough(Song::class, Album::class);
    }

    public function albums(): HasMany
    {
        return $this->hasMany(Album::class);
    }

    public function labels(): HasManyThrough
    {
        return $this->hasManyThrough(Label::class, Album::class, 'performer_id', 'id', 'id', 'label_id')->distinct();
    }

    public function coverThumb(): Attribute
    {
        if ($this->detail?->getMedia('artist_picture_source')->isNotEmpty()) {
            return new Attribute(
                get: fn () => $this->detail->getFirstMediaUrl('artist_picture_source', 'jpg_conversion')
            );
        }

        $performerNameSlug = Str::slug(Str::ascii($this->name));
        $performerThumbExists = Storage::exists("public/winmedia/performers_thumbnail/$performerNameSlug.jpg");

        return new Attribute(
            get: fn () => $performerThumbExists
                ? asset("storage/winmedia/performers_thumbnail/$performerNameSlug.jpg")
                : asset('storage/winmedia/performers_thumbnail/noimage.jpg')
        );
    }

    public function coverThumbOrAlbum(): Attribute
    {
        if ($this->detail?->getMedia('artist_picture_source')->isNotEmpty()) {
            return new Attribute(
                get: fn () => $this->detail->getFirstMediaUrl('artist_picture_source', 'jpg_conversion')
            );
        }

        $performerNameSlug = Str::slug(Str::ascii($this->name));
        $performerThumbExists = Storage::exists("public/winmedia/performers_thumbnail/$performerNameSlug.jpg");

        if ($performerThumbExists) {
            return new Attribute(
                get: fn () => asset("storage/winmedia/performers_thumbnail/$performerNameSlug.jpg")
            );
        }

        $albums = $this->albums()->orderBy('published_at', 'desc')->get(['id', 'name', 'performer_id']);
        // @phpstan-ignore-next-line
        $albumCoverThumbPaths = $albums->map->cover_thumb_path_or_null->filter();

        foreach ($albumCoverThumbPaths as $albumCoverThumbPath) {
            if ($albumCoverThumbPath) {
                return new Attribute(
                    get: fn () => asset($albumCoverThumbPath)
                );
            }
        }

        return new Attribute(
            get: fn () => asset('storage/winmedia/performers_thumbnail/noimage.jpg')
        );
    }

    public function playlists(): Attribute
    {
        return new Attribute(
            get: fn () => $this->songs
                ->pluck('playlists')
                ->flatten(1)
                ->unique('id')
                ->filter()
        );
    }

    public function musicStyles(): Attribute
    {
        if ($this->detail?->genres) {
            return new Attribute(
                get: fn () => implode(', ', array_map('ucfirst', explode(',', $this->detail->genres)))
            );
        }

        return new Attribute(
            get: fn () => $this->albums->pluck('genre')->filter()->unique()->take(3)->implode(', ')
        );
    }

    public function membersIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->members->sortBy('index')->pluck('id')->values()->toJson()
        );
    }
}

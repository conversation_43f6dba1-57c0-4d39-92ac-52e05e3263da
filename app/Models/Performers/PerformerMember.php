<?php

namespace App\Models\Performers;

use Illuminate\Database\Eloquent\Relations\Pivot;

class PerformerMember extends Pivot
{
    protected $table = 'performers_members';

    public static function allRolesJsonOptions(): string
    {
        return self::pluck('roles')
            ->flatMap(function ($roles) {
                return array_map('trim', explode(',', $roles));
            })
            ->filter(function ($role) {
                return $role !== '';
            })
            ->unique()
            ->sort()
            ->values()
            ->map(fn ($role) => [
                'value' => $role,
                'label' => $role,
            ])
            ->toJson();
    }
}

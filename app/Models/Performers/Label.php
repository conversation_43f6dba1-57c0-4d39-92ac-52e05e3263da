<?php

namespace App\Models\Performers;

use App\Casts\NormalizeString;
use App\Models\Audio\Song;
use App\Models\Traits\HasLocation;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;

class Label extends Model
{
    use HasFactory;
    use HasLocation;

    /** @var string */
    protected $table = 'labels';

    /** @var array<int, string> */
    protected $fillable = ['name'];

    protected $with = ['contentLocations'];

    /** @var array<string, string> */
    protected $casts = [
        'name' => NormalizeString::class,
    ];

    protected static function booted(): void
    {
        static::deleting(function (Label $label) {
            $label->contentLocations()->delete();
        });
    }

    public function songs(): HasMany
    {
        return $this->hasMany(Song::class);
    }

    public function albums(): HasMany
    {
        return $this->hasMany(Album::class);
    }

    public function performers(): HasManyThrough
    {
        return $this->hasManyThrough(Performer::class, Album::class, 'label_id', 'id', 'id', 'performer_id')->distinct();
    }
}

<?php

namespace App\Models\Performers;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Member extends Model
{
    use HasFactory;

    protected $fillable = [
        'first_name',
        'last_name',
        'nick_name',
    ];

    public function performers(): BelongsToMany
    {
        return $this->belongsToMany(Performer::class, 'performers_members', 'member_id', 'performer_id')
            ->using(PerformerMember::class)
            ->withPivot('id', 'roles', 'begin_date', 'end_date')
            ->withTimestamps();
    }

    public function performersIdsJson(): Attribute
    {
        return new Attribute(
            get: fn () => $this->performers->sortBy('name')->pluck('id')->values()->toJson()
        );
    }
}

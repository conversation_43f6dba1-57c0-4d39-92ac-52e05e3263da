<?php

namespace App\Models\Performers;

use App\Casts\NormalizeString;
use App\Models\Audio\Song;
use App\Models\Traits\HasLocation;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Album extends Model
{
    use HasFactory;
    use HasLocation;

    /** @var string */
    protected $table = 'albums';

    /** @var array<int, string> */
    protected $fillable = ['performer_id', 'name', 'genre', 'published_at', 'label_id'];

    /** @var array<int, string> */
    protected $with = ['compilation', 'label'];

    /** @var array<string, string> */
    protected $casts = [
        'published_at' => 'datetime',
        'name' => NormalizeString::class,
    ];

    protected static function booted(): void
    {
        static::deleting(function (Album $album) {
            $album->contentLocations()->delete();
        });
    }

    public static function boot(): void
    {
        parent::boot();
        static::created(function (Album $model) {
            AlbumDetail::create([
                'album_id' => $model->id,
                'album_name' => $model->name,
            ]);
        });

        static::updating(function (Album $model) {
            $model->detail()->update([
                'album_name' => $model->name,
            ]);
        });
    }

    public function detail(): HasOne
    {
        return $this->hasOne(AlbumDetail::class);
    }

    public function performer(): BelongsTo
    {
        return $this->belongsTo(Performer::class);
    }

    public function songsRelationship(): HasMany
    {
        return $this->hasMany(Song::class);
    }

    public function label(): HasOne
    {
        return $this->hasOne(Label::class, 'id', 'label_id');
    }

    public function songs(): HasMany|HasManyThrough
    {
        $orderByRawDisc = "(CASE WHEN disc = '' OR disc = 0 OR disc IS NULL THEN 1 ELSE 0 END), CAST(disc AS UNSIGNED) ASC";
        $orderByRawTrack = "(CASE WHEN track = '' OR track = 0 OR track IS NULL THEN 1 ELSE 0 END), CAST(track AS UNSIGNED) ASC";
        if (config('database.default') === 'pgsql') {
            $orderByRawDisc = "(CASE WHEN disc = '' OR disc = '0' OR disc IS NULL THEN 1 ELSE 0 END), COALESCE(NULLIF(regexp_replace(disc, '[^0-9]', '', 'g'), '')::BIGINT, 0) ASC";
            $orderByRawTrack = "(CASE WHEN track = '' OR track = '0' OR track IS NULL THEN 1 ELSE 0 END), COALESCE(NULLIF(regexp_replace(track, '[^0-9]', '', 'g'), '')::BIGINT, 0) ASC";
        }
        if ($this->compilation) {
            return $this->compilation
                ->songs()
                ->orderByRaw($orderByRawDisc)
                ->orderByRaw($orderByRawTrack)
                ->orderBy('id', 'ASC');
        } else {
            return $this->songsRelationship()
                ->orderByRaw($orderByRawDisc)
                ->orderByRaw($orderByRawTrack)
                ->orderBy('id', 'ASC');
        }
    }

    public function compilation(): BelongsTo
    {
        return $this->belongsTo(Compilation::class, 'compilation_id');
    }

    public function coverThumb(): Attribute
    {
        $performerNamesSlug = Str::of($this->performer()->first()?->name)->ascii()->slug();
        $albumNameSlug = Str::of($this->name)->ascii()->slug();
        $coverThumbExists = Storage::exists("public/winmedia/images_albums/$performerNamesSlug/$albumNameSlug.jpg");

        return new Attribute(
            get: fn () => $coverThumbExists
                ? asset("storage/winmedia/images_albums/$performerNamesSlug/$albumNameSlug.jpg")
                : asset('storage/winmedia/images_albums/noimage.jpg')
        );
    }

    public function coverThumbPath(): Attribute
    {
        $performerNamesSlug = Str::of($this->performer()->first()?->name)->ascii()->slug();
        $albumNameSlug = Str::of($this->name)->ascii()->slug();
        $coverThumbExists = Storage::exists("public/winmedia/images_albums/$performerNamesSlug/$albumNameSlug.jpg");

        return new Attribute(
            get: fn () => $coverThumbExists
                ? "storage/winmedia/images_albums/$performerNamesSlug/$albumNameSlug.jpg"
                : 'storage/winmedia/images_albums/noimage.jpg'
        );
    }

    public function coverThumbPathOrNull(): Attribute
    {
        $performerNamesSlug = Str::of($this->performer()->first()?->name)->ascii()->slug();
        $albumNameSlug = Str::of($this->name)->ascii()->slug();
        $coverThumbExists = Storage::exists("public/winmedia/images_albums/$performerNamesSlug/$albumNameSlug.jpg");

        return new Attribute(
            get: fn () => $coverThumbExists
                ? "storage/winmedia/images_albums/$performerNamesSlug/$albumNameSlug.jpg"
                : null
        );
    }
}

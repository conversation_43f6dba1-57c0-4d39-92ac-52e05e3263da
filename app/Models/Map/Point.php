<?php

namespace App\Models\Map;

use App\Models\Traits\HasContent;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Matan<PERSON>adaev\EloquentSpatial\Objects\Point as EloquentSpatialPoint;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;

/**
 * @property-read int $id
 * @property EloquentSpatialPoint $coord
 * @property int $place_id
 * @property \DateTimeImmutable $since
 * @property \DateTimeImmutable $until
 * @property string $former_location_name
 */
class Point extends Model
{
    use HasContent;
    use HasFactory;
    use HasSpatial;

    /** @var bool */
    public $timestamps = false;

    /** @var string */
    protected $table = 'points';

    /** @var array<int, string> */
    protected $fillable = [
        'coord',
        'place_id',
        'since',
        'until',
    ];

    protected $casts = [
        'coord' => EloquentSpatialPoint::class,
        'since' => 'immutable_datetime',
        'until' => 'immutable_datetime',
    ];

    /** @return BelongsTo<Place, self> */
    public function place(): BelongsTo
    {
        return $this->belongsTo(Place::class, 'place_id');
    }

    public function getLongitude(): float
    {
        return $this->coord->longitude;
    }

    public function getLatitude(): float
    {
        return $this->coord->latitude;
    }

    /**
     * @return float[] [longitude, latitude]
     */
    public function getCoordinates(): array
    {
        return $this->coord->getCoordinates();
    }
}

<?php

namespace App\Models\Map;

use App\Casts\NormalizeString;
use App\Models\Traits\HasContent;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Okipa\MediaLibraryExt\ExtendsMediaAbilities;
use Spatie\Image\Manipulations;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

/**
 * @property-read int $id
 * @property string $name
 * @property PlaceType $type
 * @property string $geotype
 * @property string $addr_street1
 * @property string $addr_street2
 * @property string $addr_city
 * @property string $addr_zip
 * @property bool $enabled
 */
class Place extends Model implements HasMedia
{
    use ExtendsMediaAbilities;
    use HasContent;
    use HasFactory;
    use InteractsWithMedia;

    /** @var string */
    protected $table = 'places';

    protected $fillable = [
        'name',
        'type',
        'geotype',
        'addr_street1',
        'addr_street2',
        'addr_city',
        'addr_zip',
        'enabled',
        'informations',
        'description',
        'url',
        'source_id',
        'source_name',
    ];

    protected $casts = [
        'type' => PlaceType::class,
        'name' => NormalizeString::class,
    ];

    protected static function booted(): void
    {
        static::deleting(function (Place $place) {
            $place->contentLocations()->delete();
        });
    }

    /** @return HasMany<Point> */
    public function points(): HasMany
    {
        return $this->hasMany(Point::class);
    }

    /** @return HasOne<Point> */
    public function currentPoint(): HasOne
    {
        return $this->hasOne(Point::class)->ofMany([], function (Builder $query) {
            $query
                ->where(function (Builder $query) {
                    $query->where('since', '<=', now())
                        ->orWhereNull('since');
                })
                ->where(function (Builder $query) {
                    $query->where('until', '>=', now())
                        ->orWhereNull('until');
                });
        });
    }

    /**
     * Create a new Eloquent Collection of Place instances.
     *
     * @param  Place[]  $models
     */
    public function newCollection(array $models = []): PlaceCollection
    {
        return new PlaceCollection($models);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('place_picture')
            ->singleFile()
            ->acceptsMimeTypes(['image/jpeg', 'image/png']);
    }

    public function registerMediaConversions(?Media $media = null): void
    {
        $this->addMediaConversion('large_square')
            ->fit(Manipulations::FIT_CROP, 1000, 1000)
            ->format(Manipulations::FORMAT_JPG);
        $this->addMediaConversion('thumb')
            ->fit(Manipulations::FIT_CROP, 40, 40)
            ->format(Manipulations::FORMAT_WEBP);
        $this->addMediaConversion('background_gradient')
            ->fit(Manipulations::FIT_CROP, 475, 296)
            ->withResponsiveImages()
            ->format(Manipulations::FORMAT_WEBP);
        $this->addMediaConversion('seo')
            ->fit(Manipulations::FIT_CROP, 600, 600)
            ->format(Manipulations::FORMAT_WEBP);
    }
}

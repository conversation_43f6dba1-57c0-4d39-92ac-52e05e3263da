<?php

namespace App\Models\Map;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property-read int $id
 * @property string $content_type
 * @property int $content_id
 * @property string $location_type
 * @property int $location_id
 */
class ContentLocation extends Model
{
    use HasFactory;

    /** @var bool */
    public $timestamps = false;

    /** @var string */
    protected $table = 'content_location';

    protected $fillable = [
        'content_type',
        'content_id',
        'location_type',
        'location_id',
    ];

    public function content(): MorphTo
    {
        return $this->morphTo();
    }

    public function location(): MorphTo
    {
        return $this->morphTo();
    }
}

<?php

namespace App\Models\Map;

use Illuminate\Database\Eloquent\Collection;

/**
 * @template TKey of array-key
 * @template TModel of \App\Models\Map\Place
 *
 * @extends \Illuminate\Database\Eloquent\Collection<TKey, TModel>
 */
class PlaceCollection extends Collection
{
    /**
     * Return an array of GeoJSON feature structures built from the places
     * of the collection.
     */
    public function toGeoJson(): array
    {
        return $this->map(function (Place $place): array {
            \assert($place->currentPoint instanceof Point);

            $properties = [
                'id' => $place->id,
                'label' => $place->name,
                'type' => $place->type->value,
            ];

            //$properties['cacheKey'] = \hash('sha256', \implode('', $properties));
            $properties['contentCounts'] = [
                'event' => $place->event_count ?? 0,
                'news' => $place->news_count ?? 0,
                'performer' => $place->performer_count ?? 0,
                'playlist' => $place->playlist_count ?? 0,
                'podcast' => $place->podcast_count ?? 0,
            ];

            $properties['todaysContentCount'] = $place->todays_content_count ?? 0;

            return [
                'type' => 'Feature',
                'properties' => $properties,
                'geometry' => [
                    'type' => 'Point',
                    'coordinates' => [
                        $place->currentPoint->getLongitude(),
                        $place->currentPoint->getLatitude(),
                    ],
                ],
            ];
        })->toArray();
    }
}

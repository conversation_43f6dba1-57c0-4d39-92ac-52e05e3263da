<?php

declare(strict_types=1);

namespace App\Models\Map;

enum PlaceType: string
{
    case Bar = 'bar';
    case Library = 'bibliotheque';
    case CulturalCenter = 'centre_culturel';
    case Cinema = 'cinema';
    case SportComplex = 'complexe_sportif';
    case RecordShop = 'disquaire';
    case NaturalArea = 'espace_naturel';
    case School = 'etablissement_scolaire';
    case Festival = 'festival';
    case Institution = 'institution';
    case Label = 'label';
    case Bookshop = 'librairie';
    case Market = 'marche';
    case Museum = 'musee';
    case TouristOffice = 'office_de_tourisme';
    case Opera = 'opera';
    case AttractionPark = 'parc_attractions';
    case Garden = 'parc_jardin';
    case Restaurant = 'restaurant';
    case ConcertHall = 'salle_de_concert';
    case HistoricSite = 'site_historique';
    case RadioStudio = 'studio_radio';
    case RecordStudio = 'studio_enregistrement';
    case PodcastStudio = 'studio_podcast';
    case Theater = 'theatre';
    case Other = 'autre';

    /** @return array<string, string> */
    public static function list(): array
    {
        return [
            self::Bar->value => 'Bar',
            self::Library->value => 'Bibliothèque ou médiathèque',
            self::CulturalCenter->value => 'Centre culturel',
            self::Cinema->value => 'Cinéma',
            self::SportComplex->value => 'Complexe sportif',
            self::RecordShop->value => 'Disquaire',
            self::NaturalArea->value => 'Espace naturel',
            self::School->value => 'Établissement scolaire',
            self::Festival->value => 'Festival',
            self::Institution->value => 'Institution',
            self::Label->value => 'Label',
            self::Bookshop->value => 'Librairie',
            self::Market->value => 'Marché',
            self::Museum->value => 'Musée',
            self::TouristOffice->value => 'Office de tourisme',
            self::Opera->value => 'Opéra',
            self::AttractionPark->value => 'Parc d\'attractions',
            self::Garden->value => 'Parc ou jardin',
            self::Restaurant->value => 'Restaurant',
            self::ConcertHall->value => 'Salle de concert',
            self::HistoricSite->value => 'Site ou monument historique',
            self::RadioStudio->value => 'Studio de radio',
            self::RecordStudio->value => 'Studio d\'enregistrement',
            self::PodcastStudio->value => 'Studio de podcast',
            self::Theater->value => 'Théâtre',
            self::Other->value => 'Autre',
        ];
    }

    /** @return array<string, string> */
    protected static function typesMapping(): array
    {
        return [
            'archaeological_site' => self::HistoricSite->value,
            'arenas' => self::SportComplex->value,
            'arts_centre' => self::CulturalCenter->value,
            'attraction' => self::AttractionPark->value,
            'bar' => self::Bar->value,
            'Bibliothèque' => self::Library->value,
            'cafe' => self::Bar->value,
            'castle' => self::HistoricSite->value,
            'Centre culturel' => self::CulturalCenter->value,
            'Centre d\'art' => self::CulturalCenter->value,
            'Centre de création artistique' => self::CulturalCenter->value,
            'Centre de création musicale' => self::ConcertHall->value,
            'cinema' => self::Cinema->value,
            'Cinéma' => self::Cinema->value,
            'citywalls' => self::HistoricSite->value,
            'Conservatoire' => self::CulturalCenter->value,
            'Espace protégé' => self::NaturalArea->value,
            'exhibit' => self::Museum->value,
            'gallery' => self::CulturalCenter->value,
            'hotel' => self::Other->value,
            'industrial' => self::Other->value,
            'information' => self::Other->value,
            'Librairie' => self::Bookshop->value,
            'library' => self::Library->value,
            'Lieu archéologique' => self::HistoricSite->value,
            'Lieu de mémoire' => self::HistoricSite->value,
            'monastery' => self::HistoricSite->value,
            'Monument' => self::HistoricSite->value,
            'museum' => self::Museum->value,
            'Musée' => self::Museum->value,
            'nightclub' => self::ConcertHall->value,
            'Parc et jardin' => self::Garden->value,
            'ruins' => self::HistoricSite->value,
            'Scène' => self::ConcertHall->value,
            'Service d\'archives' => self::Other->value,
            'theatre' => self::Theater->value,
            'Théâtre' => self::Theater->value,
            'wayside_cross' => self::HistoricSite->value,
            'wayside_shrine' => self::HistoricSite->value,
            'Établissement d\'enseignement supérieur' => self::School->value,
        ];
    }

    public static function getTypeFromMapping(string $importType): string
    {
        if (array_key_exists($importType, self::list())) {
            return $importType;
        }

        return self::typesMapping()[$importType] ?? self::Other->value;
    }
}

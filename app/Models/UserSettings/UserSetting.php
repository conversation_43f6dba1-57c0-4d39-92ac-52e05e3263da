<?php

namespace App\Models\UserSettings;

use App\Enums\ColorModesEnum;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserSetting extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'use_flac',
        'dark_mode',
        'dark_mode_enum',
        'use_geolocation',
        'default_home_page',
        'default_radio_station_id',
        'default_webradio_id',
    ];

    protected $attributes = [
        'use_flac' => false,
        'dark_mode' => false,
        'use_geolocation' => false,
        'default_home_page' => 'browse',
    ];

    protected $casts = [
        'dark_mode_enum' => ColorModesEnum::class,
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function defaultRadioStation(): BelongsTo
    {
        return $this->belongsTo(RadioStation::class, 'default_radio_station_id');
    }

    public function defaultWebradio(): BelongsTo
    {
        return $this->belongsTo(RadioStation::class, 'default_webradio_id');
    }
}

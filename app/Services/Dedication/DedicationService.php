<?php

namespace App\Services\Dedication;

use Browser;
use Exception;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Request;

class DedicationService
{
    protected string $endpoint;

    public function __construct()
    {
        $this->endpoint = config('services.dedication.endpoint');
    }

    /** @throws \Illuminate\Http\Client\RequestException */
    public function verifyUserEligibility(int $userId, int $winmediaRadioId): array
    {
        try {
            $response = Http::get($this->endpoint . config('services.dedication.verification_user_eligibility_path'), [
                'user_id' => $userId,
                'radio_station_winmedia_id' => $winmediaRadioId,
                'timestamp' => Date::now()->timestamp,
                'platform_os' => Browser::platformFamily(),
                'platform_ip' => Request::ip(),
            ]);

            return rescue(static fn () => $response->throw()->json(), [
                'success' => false,
                'message' => 'Server error',
            ], false);
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Server error',
            ];
        }
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \JsonException
     */
    public function verifySongsEligibility(int $winmediaRadioId, ?array $winmediaSongIds = null): array
    {
        try {
            $response = Http::get(
                $this->endpoint . config('services.dedication.verify_songs_eligibility_path'),
                [
                    'radio_station_winmedia_id' => $winmediaRadioId,
                    'song_winmedia_ids' => $winmediaSongIds ? json_encode(array_values(array_filter($winmediaSongIds)), JSON_THROW_ON_ERROR) : null,
                ]
            );

            // FABRICE > HACK pour dev ##########
            /*$songsArray = [
                5628,
                2108,
                3080
            ];

            return $songsArray;*/
            // END HACk ##########

            return rescue(static fn () => $response->throw()->json(), [], false);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \Exception
     */
    public function getTimestampSlots(int $winmediaSongId, int $winmediaRadioId): array
    {
        try {
            $response = Http::get(
                $this->endpoint . config('services.dedication.timestamp_slots_path'),
                [
                    'radio_station_winmedia_id' => $winmediaRadioId,
                    'song_winmedia_id' => $winmediaSongId,
                ]
            );

            return rescue(static fn () => $response->throw()->json(), [], false);
        } catch (Exception $e) {
            return [];
        }
    }

    /**
     * @throws \Illuminate\Http\Client\RequestException
     * @throws \Exception
     */
    public function validate(int $userId, int $winmediaRadioId, int $winmediaSongId, string $timestamp, string $dedicationMessage): array
    {
        $response = Http::post(
            $this->endpoint . config('services.dedication.validation_path'),
            [
                'user_id' => $userId,
                'radio_station_winmedia_id' => $winmediaRadioId,
                'song_winmedia_id' => $winmediaSongId,
                'timestamp' => $timestamp,
                'dedicationMessage' => $dedicationMessage,
                'platform_os' => Browser::platformFamily(),
                'platform_ip' => Request::ip(),
            ]
        )->throw();

        return $response->json();
    }
}

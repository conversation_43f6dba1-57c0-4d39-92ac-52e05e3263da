<?php

namespace App\Services\Cache;

use Illuminate\Support\Facades\Cache;

class CacheService
{
    public function clearBrowseNewsArticlesCache(?int $stationId = null): void
    {
        if ($stationId) {
            Cache::forget("browse_news_articles_station_{$stationId}");
        } else {
            $stations = radioStations();

            foreach ($stations as $station) {
                Cache::forget("browse_news_articles_station_{$station->id}");
            }
        }
    }

    public function clearBrowseEventsCache(?int $stationId = null): void
    {
        if ($stationId) {
            Cache::forget("browse_events_station_{$stationId}");
        } else {
            $stations = radioStations();

            foreach ($stations as $station) {
                Cache::forget("browse_events_station_{$station->id}");
            }
        }
    }

    public function clearBrowsePodcastsCache(?int $stationId = null): void
    {
        if ($stationId) {
            Cache::forget("browse_podcasts_station_{$stationId}");
        } else {
            $stations = radioStations();

            foreach ($stations as $station) {
                Cache::forget("browse_podcasts_station_{$station->id}");
            }
        }
    }

    public function clearBrowsePlaylistsCache(?int $stationId = null): void
    {
        if ($stationId) {
            Cache::forget("browse_playlists_station_{$stationId}");
        } else {
            $stations = radioStations();

            foreach ($stations as $station) {
                Cache::forget("browse_playlists_station_{$station->id}");
            }
        }
    }

    public function clearBrowseAnnouncementsCache(?int $stationId = null): void
    {
        if ($stationId) {
            Cache::forget("browse_announcements_station_{$stationId}");
        } else {
            $stations = radioStations();

            foreach ($stations as $station) {
                Cache::forget("browse_announcements_station_{$station->id}");
            }
        }
    }

    public function clearBrowseSongsCache(?int $stationId = null): void
    {
        if ($stationId) {
            Cache::forget("browse_songs_station_{$stationId}");
        } else {
            $stations = radioStations();

            foreach ($stations as $station) {
                Cache::forget("browse_songs_station_{$station->id}");
            }
        }
    }
}

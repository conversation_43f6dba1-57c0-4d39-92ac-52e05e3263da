<?php

namespace App\Services\Seo;

use App\Http\Livewire\Router;
use App\Services\Router\RouterService;
use App\Services\Users\UserJourneysService;
use Artesaos\SEOTools\Facades\SEOTools;
use Browser;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Livewire\Component;
use SEO;

class SeoMetaService
{
    public function generateSeoMetaOnAppLoad(string $initialUrl): void
    {
        $initialRoute = app(RouterService::class)->getInitialRoute($initialUrl);
        $urlParams = app(RouterService::class)->getParamsFromUrl($initialUrl);
        $routeParams = array_merge($urlParams, $initialRoute['params'] ?? []);
        $initialRouteParams = app(RouterService::class)->instantiateParamBindings($routeParams);
        $this->generateSeoMeta(routeKey: $initialRoute['key'], routeParams: $initialRouteParams);
    }

    public function generateSeoMeta(
        string $routeKey,
        array $routeParams = [],
        ?Component $livewireComponent = null
    ): void {
        $route = Arr::first(Router::ROUTES, static fn (array $route) => $route['key'] === $routeKey);
        $seoData = $this->generateSeoMetaFromRoute($route, $routeParams);
        $this->setAppSeoMeta(
            title: Arr::get($seoData, 'title') ?: config('seotools.meta.defaults.title'),
            url: route($route['name'], $routeParams),
            desc: Arr::get($seoData, 'description') ?: config('seotools.meta.defaults.description'),
            images: Arr::get($seoData, 'images') ?: config('seotools.opengraph.defaults.images'),
            livewireComponent: $livewireComponent
        );
    }

    /** @throws \Exception */
    protected function generateSeoMetaFromRoute(array $route, array $routeParams): array
    {
        return match ($route['key']) {
            'browse' => ['title' => 'Parcourir'],
            'dedicace' => ['title' => 'Dédicacer le prochain titre'],
            'map' => ['title' => 'Se balader'],
            'programs' => ['title' => 'Programmes'],
            'program_details' => [
                'title' => $routeParams['program']->title,
                'description' => $routeParams['program']->description,
                'images' => [$routeParams['program']->getFirstMediaUrl('cover', 'seo')],
            ],
            'events' => ['title' => 'Agenda'],
            'event_details' => [
                'title' => $routeParams['event']->title,
                'description' => $routeParams['event']->description,
                'images' => [$routeParams['event']->getFirstMediaUrl('cover', 'seo')],
            ],
            'search_results' => ['title' => 'Rechercher'],
            'search_song_results' => ['title' => 'Rechercher > Titres'],
            'search_podcast_results' => ['title' => 'Rechercher > Podcasts'],
            'search_event_results' => ['title' => 'Rechercher > Agenda'],
            'search_playlist_results' => ['title' => 'Rechercher > Playlists'],
            'search_performer_results' => ['title' => 'Rechercher > Artistes'],
            'search_album_results' => ['title' => 'Rechercher > Albums'],
            'podcasts' => ['title' => 'Podcasts'],
            'podcast_details' => [
                'title' => $routeParams['podcast']->title . ' - ' . $routeParams['podcast']->published_at->setTimezone('Europe/Paris')->isoFormat('D MMMM Y'),
                'description' => $routeParams['podcast']->description,
                'images' => [$routeParams['podcast']->getFirstMediaUrl('cover', 'seo')],
            ],
            'place_details' => [
                'title' => $routeParams['place']->name,
                'description' => $routeParams['place']->description,
                'images' => [$routeParams['place']->getFirstMediaUrl('place_picture', 'seo')],
            ],
            'favorite_songs' => [
                'title' => 'Titres aimés',
                'description' => 'Retrouvez vos morceaux préférés dans votre playlist "Titres aimés" !',
                'images' => Auth::user()
                    ? Auth::user()->favoriteSongs
                        ->take(Browser::isDesktop() ? 5 : 3)
                        ->pluck('cover_thumb')
                        ->toArray()
                    : null,
            ],
            'playlists' => ['title' => 'Playlists'],
            'playlist_details' => [
                'title' => $routeParams['playlist']->title,
                'description' => 'Retrouvez vos morceaux préférés dans votre playlist "'
                    . $routeParams['playlist']->title . '" !',
                'images' => Arr::wrap($routeParams['playlist']->getFirstMediaUrl('cover', 'seo')
                    ?: $routeParams['playlist']->songs
                        ->take(Browser::isDesktop() ? 5 : 3)
                        ->pluck('cover_thumb')
                        ->toArray()),
            ],
            'news' => ['title' => 'Actualités'],
            'news_details' => [
                'title' => $routeParams['article']->getMeta('meta_title') ?: $routeParams['article']->title,
                'description' => $routeParams['article']->getMeta('meta_description') ?: $routeParams['article']->description,
                'images' => [$routeParams['article']->getFirstMediaUrl('seo') ?: $routeParams['article']->getFirstMediaUrl('illustrations', 'seo')],
            ],
            'songs' => ['title' => 'Musiques'],
            'performer_details' => [
                'title' => $routeParams['performer']->name,
                'description' => 'Artiste ' . $routeParams['performer']->name . '. Album(s) disponible(s) : '
                    . $routeParams['performer']->albums->implode('name', ', ') . '.',
                'images' => [$routeParams['performer']->cover_thumb],
            ],
            'album_details' => [
                'title' => $routeParams['album']->performer ?
                    ($routeParams['album']->performer->name . ' - ' . $routeParams['album']->name) :
                    $routeParams['album']->name,
                'description' => 'Album ' . $routeParams['album']->name,
                'images' => [$routeParams['album']->cover_thumb],
            ],
            'previous_broadcast_songs' => [
                'title' => 'Précédemment sur ' . radioStations()->firstOrFail(
                    'id',
                    app(UserJourneysService::class)->getSelectedRadioStationUniverseId()
                )->name,
            ],
            'profile_information' => ['title' => 'Vos informations personnelles'],
            'profile_params' => ['title' => 'Vos paramètres'],
            default => [],
        };
    }

    protected function setAppSeoMeta(
        string $title,
        string $url,
        ?string $desc = null,
        array $images = [],
        ?Component $livewireComponent = null,
    ): void {
        SEOTools::setTitle($title);
        SEOTools::setCanonical($url);
        SEOTools::opengraph()->setUrl($url);
        if ($desc) {
            SEOTools::setDescription(Str::limit(strip_tags($desc), 150));
        }
        if ($images) {
            SEOTools::addImages($images);
        }
        $livewireComponent?->emit('seo:meta:update', SEO::generate(app()->environment() === 'production'));
    }
}

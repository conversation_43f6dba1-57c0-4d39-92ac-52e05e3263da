<?php

declare(strict_types=1);

namespace App\Services\Map;

enum MapSearchMode: string
{
    case All = 'all';
    case Event = 'event';
    case News = 'news';
    case Performer = 'performer';
    case Playlist = 'playlist';
    case Podcast = 'podcast';

    /** @return array<string, string> */
    public static function list(): array
    {
        return [
            self::All->value => 'Tout',
            self::News->value => 'Actualités',
            self::Performer->value => 'Artistes',
            self::Event->value => 'Évènements',
            self::Playlist->value => 'Playlists',
            self::Podcast->value => 'Podcasts',
        ];
    }
}

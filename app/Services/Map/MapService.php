<?php

declare(strict_types=1);

namespace App\Services\Map;

use App\Models\Audio\Playlist;
use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\Map\Place;
use App\Models\Map\PlaceCollection;
use App\Models\News\NewsArticle;
use App\Models\Performers\Album;
use App\Models\Performers\Label;
use App\Models\Performers\Performer;
use App\Services\Elasticsearch\CustomSearchService;
use DateInterval;
use Illuminate\Database\Eloquent\Builder as EloquentBuilder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Query\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\Date;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class MapService
{
    private const DEFAULT_EVENTS_INTERVAL = 3;

    private const DEFAULT_NEWS_INTERVAL = 3;

    private const DEFAULT_PERFORMERS_INTERVAL = 5;

    private const DEFAULT_PLAYLISTS_INTERVAL = 4;

    private const DEFAULT_PODCASTS_INTERVAL = 2;

    private const RAIL_LIMIT = 30;

    private ?int $musicThematicId = null;

    public function __construct(
        private CustomSearchService $searchService,
        private MapStateService $stateService,
    ) {
        $this->musicThematicId = thematics()->where('title', 'Musique')->first()?->id;
    }

    public function state(): MapStateService
    {
        return $this->stateService;
    }

    /** @return PlaceCollection<int, Place> */
    public function loadPlaces(): PlaceCollection
    {
        if ($this->stateService->getSearch()) {
            $places = $this->searchPlaces();
        } else {
            $places = $this->selectPlaces();
        }

        $this->stateService->save();

        return $places;
    }

    /** @return EloquentCollection<int, covariant Event> */
    public function loadEvents(int $placeId): EloquentCollection
    {
        $now = Date::now()->toImmutable()->setTimezone('Europe/Paris');

        if (
            $this->stateService->doesActiveSearchExist() &&
            \in_array($this->stateService->getSearchMode(), [MapSearchMode::All, MapSearchMode::Event])
        ) {
            $searchResult = $this->stateService->getSearchResult();

            if (isset($searchResult[$placeId][MapSearchMode::Event->value])) {
                $eventIds = $searchResult[$placeId][MapSearchMode::Event->value];

                return Event::with(['media', 'radioStations'])
                    ->whereIn('id', $eventIds)
                    ->orderByRaw('CASE WHEN ended_at >= ? THEN 0 ELSE 1 END', [now()->subDay()->startOfDay()])
                    ->orderByRaw('abs((events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date - ?)', [$now->format('Y-m-d')])
                    ->orderBy('started_at')
                    ->take(self::RAIL_LIMIT)
                    ->get();
            } else {
                return new EloquentCollection();
            }
        }

        $query = Event::with(['media', 'radioStations'])
            ->active()
            ->whereHas('contentLocations', function (EloquentBuilder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->orderByRaw('CASE WHEN ended_at >= ? THEN 0 ELSE 1 END', [now()->subDay()->startOfDay()])
            ->orderByRaw('abs((events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date - ?)', [$now->format('Y-m-d')])
            ->orderBy('started_at');

        $radioStationIds = $this->stateService->getDisplayedRadioStation();
        if (! empty($radioStationIds)) {
            $query->where(function (EloquentBuilder $query) use ($radioStationIds): void {
                $query->whereHas('radioStations', function (EloquentBuilder $query) use ($radioStationIds) {
                    $query->whereIn('id', $radioStationIds);
                })->orDoesntHave('radioStations');
            });
        }

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            $query->whereIn('thematic_id', $thematicsEnabled);
        }

        if ($period = $this->stateService->getCustomPeriod()) {
            [$start, $end] = $period;

            $query->where(function (EloquentBuilder $query) use ($start, $end): void {
                $query->where(function (EloquentBuilder $query) use ($start, $end): void {
                    $query->whereRaw('(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date >= ?', $start->format('Y-m-d'))
                        ->whereRaw('(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date <= ?', $end->format('Y-m-d'));
                });
                $query->orWhere(function (EloquentBuilder $query) use ($start, $end): void {
                    $query->whereRaw('(events.ended_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date >= ?', $start->format('Y-m-d'))
                        ->whereRaw('(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date <= ?', $end->format('Y-m-d'));
                });
            });
        }

        return $query->take(self::RAIL_LIMIT)->get();
    }

    /** @return EloquentCollection<int, covariant NewsArticle> */
    public function loadNews(int $placeId): EloquentCollection
    {
        $now = Date::now()->toImmutable();

        if (
            $this->stateService->doesActiveSearchExist() &&
            \in_array($this->stateService->getSearchMode(), [MapSearchMode::All, MapSearchMode::News])
        ) {
            $searchResult = $this->stateService->getSearchResult();

            if (isset($searchResult[$placeId][MapSearchMode::News->value])) {
                $newsIds = $searchResult[$placeId][MapSearchMode::News->value];

                return NewsArticle::with(['media', 'radioStations'])
                    ->whereIn('id', $newsIds)
                    ->orderByRaw('abs(extract(epoch from news_articles.published_at - ?))', [$now])
                    ->take(self::RAIL_LIMIT)
                    ->get();
            } else {
                return new EloquentCollection();
            }
        }

        $query = NewsArticle::with(['media', 'radioStations'])
            ->active()
            ->where('published_at', '<=', $now)
            ->whereHas('contentLocations', function (EloquentBuilder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->orderByRaw('abs(extract(epoch from news_articles.published_at - ?))', [$now]);

        $radioStationIds = $this->stateService->getDisplayedRadioStation();
        if (! empty($radioStationIds)) {
            $query->where(function (EloquentBuilder $query) use ($radioStationIds): void {
                $query->whereHas('radioStations', function (EloquentBuilder $query) use ($radioStationIds) {
                    $query->whereIn('id', $radioStationIds);
                })->orDoesntHave('radioStations');
            });
        }

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            $query->whereIn('thematic_id', $thematicsEnabled);
        }

        if ($period = $this->stateService->getCustomPeriod()) {
            [$start, $end] = $period;

            if ($now >= $start) {
                $query->where('news_articles.published_at', '>=', $start)
                    ->where('news_articles.published_at', '<=', ($now < $end) ? $now : $end);
            } else {
                return new EloquentCollection();
            }
        }

        return $query->take(self::RAIL_LIMIT)->get();
    }

    /** @return EloquentCollection<int, covariant Performer> */
    public function loadPerformers(int $placeId): EloquentCollection
    {
        if (
            $this->stateService->doesActiveSearchExist() &&
            \in_array($this->stateService->getSearchMode(), [MapSearchMode::All, MapSearchMode::Performer])
        ) {
            $searchResult = $this->stateService->getSearchResult();

            if (isset($searchResult[$placeId][MapSearchMode::Performer->value])) {
                $performerIds = $searchResult[$placeId][MapSearchMode::Performer->value];

                return Performer::with(['albums', 'labels'])
                    ->selectRaw('performers.*')
                    ->whereIn('id', $performerIds)
                    ->withCount(['albums as albums_max_published_at' => function (EloquentBuilder $query) {
                        $query->select(\DB::raw('max(published_at)'));
                    }])
                    ->withCount(['albums as albums_max_created_at_truncated' => function (EloquentBuilder $query) {
                        $query->select(\DB::raw('DATE_TRUNC(\'minute\', max(created_at))'));
                    }])
                    ->withCount(['songs as songs_count'])
                    ->withCount(['albums as albums_max_created_at' => function (EloquentBuilder $query) {
                        $query->select(\DB::raw('max(created_at)'));
                    }])
                    ->orderByDesc('albums_max_published_at')
                    ->orderByDesc('albums_max_created_at_truncated')
                    ->orderByDesc('songs_count')
                    ->orderByDesc('albums_max_created_at')
                    ->take(self::RAIL_LIMIT)
                    ->get();
            } else {
                return new EloquentCollection();
            }
        }

        // Vérification des filtres thématiques
        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            if (! in_array($this->musicThematicId, $thematicsEnabled)) {
                return new EloquentCollection();
            }
        }

        // Construction de la sous-requête pour pré-calculer les agrégats des albums
        $validAlbumsQuery = Album::query()
            ->select('performer_id')
            ->selectRaw('MAX(published_at) as max_published_at')
            ->selectRaw('DATE_TRUNC(\'minute\', MAX(created_at)) as max_created_at_truncated')
            ->selectRaw('MAX(created_at) as max_created_at')
            ->where(function (EloquentBuilder $query) use ($placeId) {
                $query->whereHas('contentLocations', function (EloquentBuilder $query) use ($placeId): void {
                    $query->where('location_id', $placeId)
                        ->where('location_type', Place::class);
                })
                    ->orWhereHas('label', function (EloquentBuilder $query) use ($placeId): void {
                        $query->whereHas('contentLocations', function (EloquentBuilder $query) use ($placeId): void {
                            $query->where('location_id', $placeId)
                                ->where('location_type', Place::class);
                        });
                    });
            });

        // Appliquer la condition de période si définie, sinon filtrer sur les compilations
        if ($period = $this->stateService->getCustomPeriod()) {
            [$start, $end] = $period;
            $validAlbumsQuery->where(function (EloquentBuilder $query) use ($start, $end) {
                $query->whereBetween('published_at', [$start, $end])
                    ->orWhereNull('compilation_id');
            });
        } else {
            $validAlbumsQuery->whereNull('compilation_id');
        }

        $validAlbumsQuery->groupBy('performer_id');

        // Requête principale sur les performers en joignant la sous-requête pré-agrégée
        $query = Performer::with(['albums', 'labels'])
            ->select('performers.*')
            ->joinSub($validAlbumsQuery, 'album_stats', function ($join) {
                $join->on('performers.id', '=', 'album_stats.performer_id');
            })
            ->where(function (EloquentBuilder $query) use ($placeId) {
                // Conditions sur les albums selon le lieu et éventuellement la période
                $query->whereHas('albums', function (EloquentBuilder $query) use ($placeId) {
                    if ($period = $this->stateService->getCustomPeriod()) {
                        [$start, $end] = $period;
                        $query->whereBetween('published_at', [$start, $end]);
                    } else {
                        $query->whereNull('compilation_id');
                    }
                    $query->whereHas('contentLocations', function (EloquentBuilder $query) use ($placeId): void {
                        $query->where('location_id', $placeId)
                            ->where('location_type', Place::class);
                    });
                })
                    ->orWhereHas('labels', function (EloquentBuilder $query) use ($placeId) {
                        $query->whereHas('contentLocations', function (EloquentBuilder $query) use ($placeId): void {
                            $query->where('location_id', $placeId)
                                ->where('location_type', Place::class);
                        })
                            ->whereHas('albums', function (EloquentBuilder $query): void {
                                if ($period = $this->stateService->getCustomPeriod()) {
                                    [$start, $end] = $period;
                                    $query->whereBetween('published_at', [$start, $end]);
                                } else {
                                    $query->whereNull('compilation_id');
                                }
                                $query->whereColumn('albums.performer_id', 'performers.id');
                            });
                    });
            })
            ->withCount('songs')
            ->orderByRaw('album_stats.max_published_at DESC NULLS LAST')
            ->orderByDesc('album_stats.max_created_at_truncated')
            ->orderByDesc('songs_count')
            ->orderByDesc('album_stats.max_created_at');

        return $query->take(self::RAIL_LIMIT)->get();
    }

    /** @return EloquentCollection<int, covariant Playlist> */
    public function loadPlaylists(int $placeId): EloquentCollection
    {
        $now = Date::now()->toImmutable();

        if (
            $this->stateService->doesActiveSearchExist() &&
            \in_array($this->stateService->getSearchMode(), [MapSearchMode::All, MapSearchMode::Playlist])
        ) {
            $searchResult = $this->stateService->getSearchResult();

            if (isset($searchResult[$placeId][MapSearchMode::Playlist->value])) {
                $playlistIds = $searchResult[$placeId][MapSearchMode::Playlist->value];

                return Playlist::with(['media', 'radioStations', 'songs'])
                    ->whereIn('id', $playlistIds)
                    ->orderByRaw('abs(extract(epoch from playlists.published_at - ?))', [$now])
                    ->take(self::RAIL_LIMIT)
                    ->get();
            } else {
                return new EloquentCollection();
            }
        }

        $query = Playlist::with(['media', 'radioStations', 'songs'])
            ->active()
            ->public()
            ->where('published_at', '<=', $now)
            ->where(function (EloquentBuilder $subWhereQuery) use ($now) {
                $subWhereQuery->whereNull('unpublished_at')
                    ->orWhere('unpublished_at', '>=', $now);
            })
            ->whereHas('contentLocations', function (EloquentBuilder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->orderByRaw('abs(extract(epoch from playlists.published_at - ?))', [$now]);

        $radioStationIds = $this->stateService->getDisplayedRadioStation();
        if (! empty($radioStationIds)) {
            $query->where(function (EloquentBuilder $query) use ($radioStationIds): void {
                $query->whereHas('radioStations', function (EloquentBuilder $query) use ($radioStationIds) {
                    $query->whereIn('id', $radioStationIds);
                })->orDoesntHave('radioStations');
            });
        }

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            $query->whereIn('thematic_id', $thematicsEnabled);
        }

        if ($period = $this->stateService->getCustomPeriod()) {
            [$start, $end] = $period;

            if ($now >= $start) {
                $query->where(function (EloquentBuilder $query) use ($now, $start, $end): void {
                    $query->where('playlists.published_at', '>=', $start)
                        ->where('playlists.published_at', '<=', ($now < $end) ? $now : $end)
                        ->where(function (EloquentBuilder $query) use ($now): void {
                            $query->whereNull('playlists.unpublished_at')
                                ->orWhere('playlists.unpublished_at', '>=', $now);
                        });
                });
            } else {
                return new EloquentCollection();
            }
        }

        return $query->take(self::RAIL_LIMIT)->get();
    }

    /** @return EloquentCollection<int, covariant Podcast> */
    public function loadPodcasts(int $placeId): EloquentCollection
    {
        $now = Date::now()->toImmutable();

        if (
            $this->stateService->doesActiveSearchExist() &&
            \in_array($this->stateService->getSearchMode(), [MapSearchMode::All, MapSearchMode::Podcast])
        ) {
            $searchResult = $this->stateService->getSearchResult();

            if (isset($searchResult[$placeId][MapSearchMode::Podcast->value])) {
                $podcastIds = $searchResult[$placeId][MapSearchMode::Podcast->value];

                return Podcast::with(['media', 'radioStations', 'program'])
                    ->whereIn('id', $podcastIds)
                    ->orderByRaw('abs(extract(epoch from podcasts.published_at - ?))', [$now])
                    ->take(self::RAIL_LIMIT)
                    ->get();
            } else {
                return new EloquentCollection();
            }
        }

        $query = Podcast::with(['media', 'radioStations', 'program'])
            ->active()
            ->hasAudio()
            ->where('published_at', '<=', $now)
            ->whereHas('contentLocations', function (EloquentBuilder $query) use ($placeId): void {
                $query->where('location_id', $placeId)
                    ->where('location_type', Place::class);
            })
            ->orderByRaw('abs(extract(epoch from podcasts.published_at - ?))', [$now]);

        $radioStationIds = $this->stateService->getDisplayedRadioStation();
        if (! empty($radioStationIds)) {
            $query->where(function (EloquentBuilder $query) use ($radioStationIds): void {
                $query->whereHas('radioStations', function (EloquentBuilder $query) use ($radioStationIds) {
                    $query->whereIn('id', $radioStationIds);
                })->orDoesntHave('radioStations');
            });
        }

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            $query->whereIn('thematic_id', $thematicsEnabled);
        }

        if ($period = $this->stateService->getCustomPeriod()) {
            [$start, $end] = $period;

            if ($now >= $start) {
                $query->where('podcasts.published_at', '>=', $start)
                    ->where('podcasts.published_at', '<=', ($now < $end) ? $now : $end);
            } else {
                return new EloquentCollection();
            }
        }

        return $query->take(self::RAIL_LIMIT)->get();
    }

    /**
     * Charger les lieux depuis la base de données relationnelle.
     *
     * @return PlaceCollection<int, Place>
     */
    private function selectPlaces(): PlaceCollection
    {
        $searchMode = $this->stateService->getSearchMode();

        if ($searchMode === MapSearchMode::All) {
            $subqueries = \array_filter([
                $this->buildPlacesWithEventQuery(),
                $this->buildPlacesWithNewsQuery(),
                $this->buildPlacesWithPerformerQuery(),
                $this->buildPlacesWithPlaylistQuery(),
                $this->buildPlacesWithPodcastQuery(),
            ]);

            $baseQuery = \array_shift($subqueries);
            foreach ($subqueries as $subquery) {
                $baseQuery->unionAll($subquery);
            }

            $aggregationQuery = DB::query()
                ->fromSub($baseQuery, 'bq')
                ->addSelect('bq.location_id')
                ->selectRaw('count(case when bq.content_type = ? then 1 end) as event_count', [Event::class])
                ->selectRaw('count(case when bq.content_type = ? then 1 end) as news_count', [NewsArticle::class])
                ->selectRaw('count(case when bq.content_type = ? then 1 end) as performer_count', [Performer::class])
                ->selectRaw('count(case when bq.content_type = ? then 1 end) as playlist_count', [Playlist::class])
                ->selectRaw('count(case when bq.content_type = ? then 1 end) as podcast_count', [Podcast::class])
                ->selectRaw('count(case when bq.today then 1 end) as todays_content_count')
                ->groupBy('bq.location_id')
                ->having(function (Builder $query): void {
                    $query->havingRaw('count(case when bq.content_type = ? then 1 end) > 0', [Event::class])
                        ->orHavingRaw('count(case when bq.content_type = ? then 1 end) > 0', [NewsArticle::class])
                        ->orHavingRaw('count(case when bq.content_type = ? then 1 end) > 0', [Performer::class])
                        ->orHavingRaw('count(case when bq.content_type = ? then 1 end) > 0', [Playlist::class])
                        ->orHavingRaw('count(case when bq.content_type = ? then 1 end) > 0', [Podcast::class]);
                });

            $places = Place::withWhereHas('currentPoint')
                ->joinSub($aggregationQuery, 'aq', 'places.id', '=', 'aq.location_id')
                ->where('places.enabled', true)
                ->select([
                    'places.*',
                    'aq.event_count',
                    'aq.news_count',
                    'aq.performer_count',
                    'aq.playlist_count',
                    'aq.podcast_count',
                    'aq.todays_content_count',
                ])
                ->get();

            return $places;
        }

        $baseQuery = match ($searchMode) {
            MapSearchMode::Event => $this->buildPlacesWithEventQuery(),
            MapSearchMode::News => $this->buildPlacesWithNewsQuery(),
            MapSearchMode::Performer => $this->buildPlacesWithPerformerQuery(),
            MapSearchMode::Playlist => $this->buildPlacesWithPlaylistQuery(),
            MapSearchMode::Podcast => $this->buildPlacesWithPodcastQuery(),
        };

        if (! $baseQuery) {
            return new PlaceCollection();
        }

        $aggregationQuery = DB::query()
            ->fromSub($baseQuery, 'bq')
            ->addSelect('bq.location_id')
            ->selectRaw('count(*) as count')
            ->selectRaw('count(case when bq.today then 1 end) as todays_content_count')
            ->groupBy('bq.location_id');

        $places = Place::withWhereHas('currentPoint')
            ->joinSub($aggregationQuery, 'aq', 'places.id', '=', 'aq.location_id')
            ->where('places.enabled', true)
            ->select(['places.*', 'aq.todays_content_count'])
            ->selectRaw("aq.count as {$searchMode->value}_count")
            ->get();

        return $places;
    }

    private function buildPlacesWithEventQuery(): Builder
    {
        $now = Date::now()->toImmutable()->setTimezone('Europe/Paris');
        $eventsInterval = 'P'
            . ($this->stateService->getRadioStation()?->map_events_week_interval ?? settings()->map_events_week_interval ?? self::DEFAULT_EVENTS_INTERVAL)
            . 'W';

        $query = DB::table('content_location', 'cl')
            ->select(['cl.location_id', 'cl.content_type', 'cl.content_id'])
            ->selectRaw('(DATE(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\') <= ? AND DATE(events.ended_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\') >= ?) as today', [$now->format('Y-m-d'), $now->format('Y-m-d')])
            ->join('events', function (JoinClause $join) {
                $join->where('cl.content_type', '=', Event::class)
                    ->on('cl.content_id', '=', 'events.id');
            })
            ->where('cl.location_type', Place::class)
            ->where('events.active', true);

        $radioStationIds = $this->stateService->getDisplayedRadioStation();
        if (! empty($radioStationIds)) {
            $query->leftJoin('event_radio_station', 'events.id', '=', 'event_radio_station.event_id');
            $query->where(function (Builder $query) use ($radioStationIds): void {
                $query->whereIn('event_radio_station.radio_station_id', $radioStationIds)
                    ->orWhereNull('event_radio_station.radio_station_id');
            });
        }

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            $query->whereIn('thematic_id', $thematicsEnabled);
        }

        if ($period = $this->stateService->getCustomPeriod()) {
            [$start, $end] = $period;
            $query->where(function (Builder $query) use ($start, $end): void {
                $query->where(function (Builder $query) use ($start, $end): void {
                    $query->whereRaw('(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date >= ?', $start->format('Y-m-d'))
                        ->whereRaw('(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date <= ?', $end->format('Y-m-d'));
                });
                $query->orWhere(function (Builder $query) use ($start, $end): void {
                    $query->whereRaw('(events.ended_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date >= ?', $start->format('Y-m-d'))
                        ->whereRaw('(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date <= ?', $end->format('Y-m-d'));
                });
            });
        } else {
            $activePlaceWithAllContents = $this->stateService->getActivePlaceWithAllContents();
            $query->where(function (Builder $query) use ($now, $eventsInterval, $activePlaceWithAllContents): void {
                $end = $now->add(new DateInterval($eventsInterval));
                $query->where(function (Builder $query) use ($now, $end): void {
                    $query->whereRaw('(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date >= ?', $now->format('Y-m-d'))
                        ->whereRaw('(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date <= ?', $end->format('Y-m-d'));
                });
                $query->orWhere(function (Builder $query) use ($now, $end): void {
                    $query->whereRaw('(events.ended_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date >= ?', $now->format('Y-m-d'))
                        ->whereRaw('(events.started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date <= ?', $end->format('Y-m-d'));
                });
                if ($activePlaceWithAllContents) {
                    $query->orWhere('cl.location_id', $activePlaceWithAllContents->id);
                }
            });
        }

        return $query;
    }

    private function buildPlacesWithNewsQuery(): ?Builder
    {
        $now = Date::now()->toImmutable();
        $newsInterval = 'P'
            . ($this->stateService->getRadioStation()?->map_news_week_interval ?? settings()->map_news_week_interval ?? self::DEFAULT_NEWS_INTERVAL)
            . 'W';

        $query = DB::table('content_location', 'cl')
            ->select(['cl.location_id', 'cl.content_type', 'cl.content_id'])
            ->selectRaw('(news_articles.published_at::date = ?) as today', [$now->format('Y-m-d')])
            ->join('news_articles', function (JoinClause $join) {
                $join->where('cl.content_type', '=', NewsArticle::class)
                    ->on('cl.content_id', '=', 'news_articles.id');
            })
            ->where('cl.location_type', Place::class)
            ->where('news_articles.active', true);

        $radioStationIds = $this->stateService->getDisplayedRadioStation();
        if (! empty($radioStationIds)) {
            $query->leftJoin('news_article_radio_station', 'news_articles.id', '=', 'news_article_radio_station.news_article_id');
            $query->where(function (Builder $query) use ($radioStationIds): void {
                $query->whereIn('news_article_radio_station.radio_station_id', $radioStationIds)
                    ->orWhereNull('news_article_radio_station.radio_station_id');
            });
        }

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            $query->whereIn('thematic_id', $thematicsEnabled);
        }

        if ($period = $this->stateService->getCustomPeriod()) {
            if ($now < $period[0]) {
                return null;
            }

            $query->where('news_articles.published_at', '>=', $period[0])
                ->where('news_articles.published_at', '<=', ($now < $period[1]) ? $now : $period[1]);
        } else {
            $activePlaceWithAllContents = $this->stateService->getActivePlaceWithAllContents();
            $query->where('news_articles.published_at', '<=', $now)
                ->where(function (Builder $query) use ($now, $newsInterval, $activePlaceWithAllContents): void {
                    $query->where('news_articles.published_at', '>=', $now->sub(new DateInterval($newsInterval)));
                    if ($activePlaceWithAllContents) {
                        $query->orWhere('cl.location_id', $activePlaceWithAllContents->id);
                    }
                });
        }

        return $query;
    }

    private function buildPlacesWithPerformerQuery(): Builder
    {
        $now = Date::now()->toImmutable();
        $performersInterval = 'P'
            . ($this->stateService->getRadioStation()?->map_performers_week_interval ?? settings()->map_performers_week_interval ?? self::DEFAULT_PERFORMERS_INTERVAL)
            . 'W';

        $query = DB::table('content_location', 'cl')
            ->distinct()
            ->addSelect('cl.location_id')
            ->selectRaw('? as content_type', [Performer::class])
            ->addSelect(['content_id' => 'performers.id'])
            ->where('cl.location_type', Place::class)
            ->leftJoin('labels', function ($join) {
                $join->on('labels.id', '=', 'cl.content_id')
                    ->where('cl.content_type', '=', Label::class);
            })
            ->where(function ($query) {
                // Premier cas : via les albums directement
                $query->where(function ($subQuery) {
                    $subQuery->where('cl.content_type', Album::class)
                        ->whereColumn('cl.content_id', 'albums.id');
                })
                // Deuxième cas : via les labels
                    ->orWhere(function ($subQuery) {
                        $subQuery->where('cl.content_type', Label::class)
                            ->whereColumn('cl.content_id', 'labels.id');
                    });
            })
            ->join('albums', function ($join) {
                $join->on(function ($on) {
                    $on->on('albums.id', '=', 'cl.content_id')
                        ->where('cl.content_type', '=', Album::class);
                })->orOn(function ($on) {
                    $on->on('albums.label_id', '=', 'labels.id')
                        ->where('cl.content_type', '=', Label::class);
                });
            })
            ->selectRaw('(albums.created_at::date = ? AND albums.published_at >= ?) as today', [
                $now->format('Y-m-d'),
                $now->sub(new DateInterval('P1Y')),
            ])
            ->join('performers', 'performers.id', '=', 'albums.performer_id');

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            // Ne pas afficher les artistes si la thématique "Musique" n'est pas activée
            if (! in_array($this->musicThematicId, $thematicsEnabled)) {
                $query->whereRaw('1 = 0');
            }
        }

        if ($period = $this->stateService->getCustomPeriod()) {
            $query->where('albums.published_at', '>=', $period[0])
                ->where('albums.published_at', '<=', $period[1]);
        } else {
            $activePlaceWithAllContents = $this->stateService->getActivePlaceWithAllContents();
            $query->where('albums.created_at', '<=', $now)
                ->where(function (Builder $query) use ($now, $performersInterval, $activePlaceWithAllContents): void {
                    $query->where('albums.created_at', '>=', $now->sub(new DateInterval($performersInterval)));
                    if ($activePlaceWithAllContents) {
                        $query->orWhere('cl.location_id', $activePlaceWithAllContents->id);
                    }
                });
        }

        return $query;
    }

    private function buildPlacesWithPlaylistQuery(): ?Builder
    {
        $now = Date::now()->toImmutable();
        $playlistsInterval = 'P'
            . ($this->stateService->getRadioStation()?->map_playlists_week_interval ?? settings()->map_playlists_week_interval ?? self::DEFAULT_PLAYLISTS_INTERVAL)
            . 'W';

        $query = DB::table('content_location', 'cl')
            ->select(['cl.location_id', 'cl.content_type', 'cl.content_id'])
            ->selectRaw('(playlists.published_at::date = ?) as today', [$now->format('Y-m-d')])
            ->join('playlists', function (JoinClause $join) {
                $join->where('cl.content_type', '=', Playlist::class)
                    ->on('cl.content_id', '=', 'playlists.id');
            })
            ->where('cl.location_type', Place::class)
            ->where('playlists.active', true)
            ->whereNull('playlists.user_id');

        $radioStationIds = $this->stateService->getDisplayedRadioStation();
        if (! empty($radioStationIds)) {
            $query->leftJoin('playlists_radio_stations', 'playlists.id', '=', 'playlists_radio_stations.playlist_id');
            $query->where(function (Builder $query) use ($radioStationIds): void {
                $query->whereIn('playlists_radio_stations.radio_station_id', $radioStationIds)
                    ->orWhereNull('playlists_radio_stations.radio_station_id');
            });
        }

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            $query->whereIn('thematic_id', $thematicsEnabled);
        }

        if ($period = $this->stateService->getCustomPeriod()) {
            [$start, $end] = $period;

            if ($now < $start) {
                return null;
            }

            $query->where(function (Builder $query) use ($now, $start, $end): void {
                $query->where('playlists.published_at', '>=', $start)
                    ->where('playlists.published_at', '<=', ($now < $end) ? $now : $end)
                    ->where(function (Builder $query) use ($now): void {
                        $query->whereNull('playlists.unpublished_at')
                            ->orWhere('playlists.unpublished_at', '>=', $now);
                    });
            });
        } else {
            $activePlaceWithAllContents = $this->stateService->getActivePlaceWithAllContents();
            $query->where(function (Builder $query) use ($now, $playlistsInterval, $activePlaceWithAllContents): void {
                $query->where('playlists.published_at', '<=', $now)
                    ->where(function (Builder $query) use ($now, $playlistsInterval, $activePlaceWithAllContents): void {
                        $query->where('playlists.published_at', '>=', $now->sub(new DateInterval($playlistsInterval)));
                        if ($activePlaceWithAllContents) {
                            $query->orWhere('cl.location_id', $activePlaceWithAllContents->id);
                        }
                    })
                    ->where(function (Builder $query) use ($now): void {
                        $query->whereNull('playlists.unpublished_at')
                            ->orWhere('playlists.unpublished_at', '>=', $now);
                    });
            });
        }

        return $query;
    }

    private function buildPlacesWithPodcastQuery(): ?Builder
    {
        $now = Date::now()->toImmutable();
        $podcastsInterval = 'P'
            . ($this->stateService->getRadioStation()?->map_podcasts_week_interval ?? settings()->map_podcasts_week_interval ?? self::DEFAULT_PODCASTS_INTERVAL)
            . 'W';

        $query = DB::table('content_location', 'cl')
            ->select(['cl.location_id', 'cl.content_type', 'cl.content_id'])
            ->selectRaw('(podcasts.published_at::date = ?) as today', [$now->format('Y-m-d')])
            ->join('podcasts', function (JoinClause $join) {
                $join->where('cl.content_type', '=', Podcast::class)
                    ->on('cl.content_id', '=', 'podcasts.id');
            })
            ->where('cl.location_type', Place::class)
            ->where('podcasts.active', true)
            ->whereIn('podcasts.id', function ($query) {
                $query->select('id')
                    ->from('podcasts')
                    ->where(function ($query) {
                        $query->whereIn('id', function (Builder $query) {
                            $query->select('media.model_id')
                                ->from('media')
                                ->join('podcasts', 'podcasts.id', '=', 'media.model_id')
                                ->where('media.model_type', Podcast::class)
                                ->where('media.collection_name', 'audio')
                                ->groupBy('media.model_id');
                        });
                        $query->orWhere('winmedia_audio_source_uploaded', true);
                    });
            });

        $radioStationIds = $this->stateService->getDisplayedRadioStation();
        if (! empty($radioStationIds)) {
            $query->leftJoin('podcasts_radio_stations', 'podcasts.id', '=', 'podcasts_radio_stations.podcast_id');
            $query->where(function (Builder $query) use ($radioStationIds): void {
                $query->whereIn('podcasts_radio_stations.radio_station_id', $radioStationIds)
                    ->orWhereNull('podcasts_radio_stations.radio_station_id');
            });
        }

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            $query->whereIn('thematic_id', $thematicsEnabled);
        }

        if ($period = $this->stateService->getCustomPeriod()) {
            if ($now < $period[0]) {
                return null;
            }

            $query->where('podcasts.published_at', '>=', $period[0])
                ->where('podcasts.published_at', '<=', ($now < $period[1]) ? $now : $period[1]);
        } else {
            $activePlaceWithAllContents = $this->stateService->getActivePlaceWithAllContents();
            $query->where('podcasts.published_at', '<=', $now)
                ->where(function (Builder $query) use ($now, $podcastsInterval, $activePlaceWithAllContents): void {
                    $query->where('podcasts.published_at', '>=', $now->sub(new DateInterval($podcastsInterval)));
                    if ($activePlaceWithAllContents) {
                        $query->orWhere('cl.location_id', $activePlaceWithAllContents->id);
                    }
                });
        }

        return $query;
    }

    /**
     * Charger les lieux depuis le moteur de recherche Elasticsearch.
     *
     * @return PlaceCollection<int, Place>
     */
    private function searchPlaces(): PlaceCollection
    {
        $searchResult = [];
        $searchMode = $this->stateService->getSearchMode();

        if ($searchMode === MapSearchMode::All) {
            $specificResults = [
                MapSearchMode::Event->value => $this->searchPlacesWithEvent(),
                MapSearchMode::News->value => $this->searchPlacesWithNews(),
                MapSearchMode::Performer->value => $this->searchPlacesWithPerformer(),
                MapSearchMode::Playlist->value => $this->searchPlacesWithPlaylist(),
                MapSearchMode::Podcast->value => $this->searchPlacesWithPodcast(),
            ];
        } else {
            $specificResults = [$searchMode->value => match ($searchMode) {
                MapSearchMode::Event => $this->searchPlacesWithEvent(),
                MapSearchMode::News => $this->searchPlacesWithNews(),
                MapSearchMode::Performer => $this->searchPlacesWithPerformer(),
                MapSearchMode::Playlist => $this->searchPlacesWithPlaylist(),
                MapSearchMode::Podcast => $this->searchPlacesWithPodcast(),
            }];
        }

        foreach ($specificResults as $contentType => $specificResult) {
            foreach ($specificResult as $placeId => $placeResult) {
                $searchResult[$placeId][$contentType] = $placeResult['list'];
                if (! isset($searchResult[$placeId]['todays_content_count'])) {
                    $searchResult[$placeId]['todays_content_count'] = $placeResult['todays_content_count'];

                    continue;
                }
                $searchResult[$placeId]['todays_content_count'] += $placeResult['todays_content_count'];
            }
        }

        $this->stateService->setSearchResult($searchResult);

        $places = Place::withWhereHas('currentPoint')
            ->whereIn('id', array_filter(array_keys($searchResult), fn ($id) => ! empty($id)))
            ->where('enabled', true)
            ->get();

        $places->each(static function (Place $place) use ($searchResult, $searchMode) {
            if ($searchMode === MapSearchMode::All) {
                /** @phpstan-ignore-next-line */
                $place->todays_content_count = 0;

                foreach ([
                    MapSearchMode::Event,
                    MapSearchMode::News,
                    MapSearchMode::Performer,
                    MapSearchMode::Playlist,
                    MapSearchMode::Podcast,
                ] as $mode) {
                    $propertyName = $mode->value . '_count';
                    if (isset($searchResult[$place->id][$mode->value])) {
                        /** @phpstan-ignore-next-line */
                        $place->$propertyName = \count($searchResult[$place->id][$mode->value]);
                        $place->todays_content_count += $searchResult[$place->id]['todays_content_count'];
                    } else {
                        /** @phpstan-ignore-next-line */
                        $place->$propertyName = 0;
                    }
                }
            } else {
                /** @phpstan-ignore-next-line */
                $place->todays_content_count = $searchResult[$place->id]['todays_content_count'];
            }
        });

        return $places;
    }

    private function searchPlacesWithEvent(): array
    {
        if (Str::startsWith($this->stateService->getSearch(), [
            CustomSearchService::FILTER_PROGRAM,
            CustomSearchService::FILTER_GENRE,
        ])) {
            return [];
        }

        $filters = $this->getCommonSearchFilters();
        $filters[] = ['term' => ['active' => true]];

        $searchResult = $this->searchService->searchEvents(
            $this->stateService->getSearch(), 50, filterCondition: $filters
        );

        $result = [];
        $today = Date::now()->setTimezone('Europe/Paris')->format('Y-m-d');

        foreach ($searchResult['hits']['hits'] as $item) {
            $placeId = $item['_source']['location_id'];
            $eventId = $item['_source']['id'];
            $startedAt = Date::parse($item['_source']['started_at'])->setTimezone('Europe/Paris')->format('Y-m-d');
            $endedAt = Date::parse($item['_source']['ended_at'])->setTimezone('Europe/Paris')->format('Y-m-d');

            $isToday = ($today >= substr($startedAt, 0, 10)) && ($today <= substr($endedAt, 0, 10));

            if (empty($result[$placeId])) {
                $result[$placeId] = [
                    'list' => [$eventId],
                    'todays_content_count' => (int) $isToday,
                ];

                continue;
            }

            $result[$placeId]['list'][] = $eventId;
            if ($isToday) {
                $result[$placeId]['todays_content_count']++;
            }
        }

        return $result;
    }

    private function searchPlacesWithNews(): array
    {
        if (Str::startsWith($this->stateService->getSearch(), [
            CustomSearchService::FILTER_PROGRAM,
            CustomSearchService::FILTER_GENRE,
        ])) {
            return [];
        }

        $filters = $this->getCommonSearchFilters();
        $filters[] = ['term' => ['active' => true]];

        $searchResult = $this->searchService->searchNews(
            $this->stateService->getSearch(), 50, filterCondition: $filters
        );

        $result = [];
        $today = Date::now()->format('Y-m-d');

        foreach ($searchResult['hits']['hits'] as $item) {
            $placeId = $item['_source']['location_id'];
            $newsId = $item['_source']['id'];
            $publishedAt = $item['_source']['published_at'];

            if (empty($result[$placeId])) {
                $result[$placeId] = [
                    'list' => [$newsId],
                    'todays_content_count' => (int) \str_starts_with($publishedAt, $today),
                ];

                continue;
            }

            $result[$placeId]['list'][] = $newsId;
            if (\str_starts_with($publishedAt, $today)) {
                $result[$placeId]['todays_content_count']++;
            }
        }

        return $result;
    }

    private function searchPlacesWithPerformer(): array
    {
        if (Str::startsWith($this->stateService->getSearch(), [
            CustomSearchService::FILTER_AUTHOR,
            CustomSearchService::FILTER_DATE,
            CustomSearchService::FILTER_PROGRAM,
        ])) {
            return [];
        }

        $searchResult = $this->searchService->searchPerformers(
            value: $this->stateService->getSearch(),
            filterCondition: [
                ['bool' => [
                    'should' => [
                        [
                            'nested' => [
                                'path' => 'albums',
                                'query' => [
                                    'term' => ['albums.location_type' => Place::class],
                                ],
                            ],
                        ],
                        [
                            'nested' => [
                                'path' => 'labels',
                                'query' => [
                                    'term' => ['labels.location_type' => Place::class],
                                ],
                            ],
                        ],
                    ],
                ]],
            ],
            size: Str::startsWith($this->stateService->getSearch(), app(CustomSearchService::class)::FILTER_GENRE) ? 1000 : 50,
        );

        $result = [];
        $today = Date::now()->format('Y-m-d');
        $oneYearAgo = Date::now()->sub(new DateInterval('P1Y'))->format('Y-m-d');

        foreach ($searchResult['hits']['hits'] as $item) {
            $performerId = $item['_source']['id'];

            $albumPlaceIds = \array_unique(\array_column(
                $item['_source']['albums'] ?? [], 'location_id'
            ));
            $labelPlaceIds = \array_unique(\array_column(
                $item['_source']['labels'] ?? [], 'location_id'
            ));
            $placeIds = array_unique(array_merge($albumPlaceIds, $labelPlaceIds));

            // TODO date check
            foreach ($placeIds as $placeId) {
                $albums = \array_filter($item['_source']['albums'], function ($item) use ($placeId) {
                    return $item['location_id'] === $placeId;
                });

                if (empty($result[$placeId])) {
                    $todayCount = 0;
                    foreach ($albums as $album) {
                        if (
                            \str_starts_with($album['created_at'], $today) &&
                            $album['published_at'] >= $oneYearAgo
                        ) {
                            $todayCount++;
                        }
                    }
                    $result[$placeId] = [
                        'list' => [$performerId],
                        'todays_content_count' => $todayCount,
                    ];

                    continue;
                }

                if (
                    ! Str::startsWith($this->stateService->getSearch(), app(CustomSearchService::class)::FILTER_GENRE)
                    || count($result[$placeId]['list']) < self::RAIL_LIMIT
                ) {
                    $result[$placeId]['list'][] = $performerId;

                    foreach ($albums as $album) {
                        if (
                            str_starts_with($album['created_at'], $today) &&
                            $album['published_at'] >= $oneYearAgo
                        ) {
                            $result[$placeId]['todays_content_count']++;
                        }
                    }
                }
            }
        }

        return $result;
    }

    private function searchPlacesWithPlaylist(): array
    {
        if (Str::startsWith($this->stateService->getSearch(), [
            CustomSearchService::FILTER_AUTHOR,
            CustomSearchService::FILTER_DATE,
            CustomSearchService::FILTER_PROGRAM,
            CustomSearchService::FILTER_GENRE,
        ])) {
            return [];
        }

        $filters = $this->getCommonSearchFilters();
        $filters[] = ['term' => ['active' => true]];
        $filters[] = [
            'bool' => [
                'must_not' => ['exists' => ['field' => 'user_id']],
                'should' => [
                    [
                        'bool' => [
                            'must' => [
                                ['range' => ['published_at' => ['lte' => 'now']]],
                                ['bool' => ['must_not' => ['exists' => ['field' => 'unpublished_at']]]],
                            ],
                        ],
                    ], [
                        'bool' => [
                            'must' => [
                                ['range' => ['published_at' => ['lte' => 'now']]],
                                ['range' => ['unpublished_at' => ['gte' => 'now']]],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $searchResult = $this->searchService->searchPlaylists(
            $this->stateService->getSearch(), 50, filterCondition: $filters
        );

        $result = [];
        $today = Date::now()->format('Y-m-d');

        foreach ($searchResult['hits']['hits'] as $item) {
            $placeId = $item['_source']['location_id'];
            $playlistId = $item['_source']['id'];
            $publishedAt = $item['_source']['published_at'];

            if (empty($result[$placeId])) {
                $result[$placeId] = [
                    'list' => [$playlistId],
                    'todays_content_count' => (int) \str_starts_with($publishedAt, $today),
                ];

                continue;
            }

            $result[$placeId]['list'][] = $playlistId;
            if (\str_starts_with($publishedAt, $today)) {
                $result[$placeId]['todays_content_count']++;
            }
        }

        return $result;
    }

    private function searchPlacesWithPodcast(): array
    {
        if (Str::startsWith($this->stateService->getSearch(), [
            CustomSearchService::FILTER_GENRE,
        ])) {
            return [];
        }

        $searchResult = $this->searchService->searchPodcasts(
            value: $this->stateService->getSearch(),
            filterCondition: $this->getCommonSearchFilters(),
            size: 50,
        );

        $result = [];
        $today = Date::now()->format('Y-m-d');

        foreach ($searchResult['hits']['hits'] as $item) {
            $placeId = $item['_source']['location_id'];
            $podcastId = $item['_source']['id'];
            $publishedAt = $item['_source']['published_at'];

            if (empty($result[$placeId])) {
                $result[$placeId] = [
                    'list' => [$podcastId],
                    'todays_content_count' => (int) \str_starts_with($publishedAt, $today),
                ];

                continue;
            }

            $result[$placeId]['list'][] = $podcastId;
            if (\str_starts_with($publishedAt, $today)) {
                $result[$placeId]['todays_content_count']++;
            }
        }

        return $result;
    }

    private function getCommonSearchFilters(): array
    {
        $filters = [['term' => ['location_type' => Place::class]]];

        $radioStationIds = $this->stateService->getDisplayedRadioStation();
        if (! empty($radioStationIds)) {
            $filters[] = [
                'bool' => [
                    'should' => [
                        ['terms' => ['radio_stations.id' => $radioStationIds]],
                        [
                            'bool' => [
                                'must_not' => [
                                    ['exists' => ['field' => 'radio_stations']],
                                ],
                            ],
                        ],
                    ],
                ],
            ];
        }

        $thematicsEnabled = $this->stateService->getFiltersEnable(MapFilterType::Thematic);
        if (! empty($thematicsEnabled)) {
            $filters[] = ['terms' => ['thematic_id' => $thematicsEnabled]];
        }

        return $filters;
    }
}

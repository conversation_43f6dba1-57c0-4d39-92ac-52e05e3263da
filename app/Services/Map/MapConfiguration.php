<?php

declare(strict_types=1);

namespace App\Services\Map;

use DateTimeImmutable;
use InvalidArgumentException;

class MapConfiguration
{
    public readonly ?float $longitude;

    public readonly ?float $latitude;

    public readonly ?float $zoom;

    public readonly array $filters;

    /** @SuppressWarnings(PHPMD.ExcessiveParameterList) */
    public function __construct(
        ?float $longitude = null,
        ?float $latitude = null,
        ?float $zoom = null,
        public readonly ?int $stationWinmediaId = null,
        public readonly ?DateTimeImmutable $since = null,
        public readonly ?DateTimeImmutable $until = null,
        public readonly ?int $activePlaceId = null,
        public readonly ?int $activePlaceAllContents = null,
        public readonly ?string $search = null,
        public readonly ?MapSearchMode $searchMode = null,
        array $filters = [],
        public readonly ?string $infoPanelTargetType = null,
        public readonly ?int $infoPanelTargetId = null,
        public readonly ?string $railContentType = null,
        public readonly ?int $railPlaceId = null,
    ) {
        $this->longitude = $longitude ? \round($longitude, 6) : null;
        $this->latitude = $latitude ? \round($latitude, 6) : null;
        $this->zoom = $zoom ? \round($zoom, 6) : null;

        foreach ($filters as $type => &$idList) {
            if (! MapFilterType::tryFrom($type)) {
                throw new InvalidArgumentException('Invalid filter type.');
            }
            if (! \is_array($idList)) {
                throw new InvalidArgumentException(
                    'Each value of the $filters argument must be an array of integers.'
                );
            }
            foreach ($idList as $index => $id) {
                if (! \is_int($id) && ! (\is_string($id) && \ctype_digit($id))) {
                    throw new InvalidArgumentException(
                        'Each value of the $filters argument must contain only integers.'
                    );
                }
                if (\is_string($id)) {
                    $idList[$index] = (int) $id;
                }
            }
        }

        $this->filters = $filters;
    }

    public static function fromArray(array $data): self
    {
        if (isset($data['period'])) {
            $since = new DateTimeImmutable($data['period'] . '-01-01 00:00:00');
            $until = new DateTimeImmutable($data['period'] . '-12-31 23:59:59');
        }

        return new self(
            longitude: isset($data['lng']) ? (float) $data['lng'] : null,
            latitude: isset($data['lat']) ? (float) $data['lat'] : null,
            zoom: isset($data['zoom']) ? (float) $data['zoom'] : null,
            stationWinmediaId: isset($data['station']) ? (int) $data['station'] : null,
            since: $since ?? null,
            until: $until ?? null,
            activePlaceId: isset($data['place']) ? (int) $data['place'] : null,
            activePlaceAllContents: isset($data['place_show_all']) ? (int) $data['place_show_all'] : null,
            search: $data['search'] ?? '',
            searchMode: isset($data['mode']) ? MapSearchMode::from($data['mode']) : MapSearchMode::All,
            filters: [MapFilterType::Thematic->value => $data['thematics'] ?? []],
            infoPanelTargetType: $data['info_type'] ?? null,
            infoPanelTargetId: isset($data['info_id']) ? (int) $data['info_id'] : null,
            railContentType: $data['rail_type'] ?? null,
            railPlaceId: isset($data['rail_place']) ? (int) $data['rail_place'] : null,
        );
    }
}

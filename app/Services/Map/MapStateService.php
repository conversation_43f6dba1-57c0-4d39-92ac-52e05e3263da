<?php

declare(strict_types=1);

namespace App\Services\Map;

use App\Models\Map\Place;
use App\Models\Radio\RadioStation;
use Illuminate\Cache\CacheManager;
use Illuminate\Session\SessionManager;
use Illuminate\Support\Str;
use InvalidArgumentException;

/** @SuppressWarnings(PHPMD.TooManyFields) */
class MapStateService
{
    public const SESSION_KEY = 'map_state_cache_key';

    private const IFRAME_CONFIG_KEY = 'map_iframe_config';

    private const IFRAME_CONFIG_LIFETIME_SECONDS = 5;

    private ?string $cacheKey = null;

    private ?RadioStation $radioStation = null;

    /** @var array<int, int> */
    private array $displayedRadioStations = [];

    private ?string $search = null;

    private MapSearchMode $searchMode = MapSearchMode::All;

    /** @var array<int, array<string, int[]>> */
    private array $searchResult = [];

    private ?\DateTimeImmutable $since = null;

    private ?\DateTimeImmutable $until = null;

    /** @var array<string, array<int, bool>> */
    public array $filters = [];

    private ?Place $activePlace = null;

    private bool $activePlaceAllContents = false;

    public ?string $railContentType = null;

    public ?int $railPlaceId = null;

    public ?string $infoPanelTargetType = null;

    public ?int $infoPanelTargetId = null;

    public array $coordinates = [];

    public ?float $zoom = null;

    public function __construct(
        private CacheManager $cache,
        private SessionManager $session,
        private int $sessionLifetime = 120,
        private ?MapConfiguration $iframeConfig = null
    ) {
        if ($iframeConfig) {
            // Stocker la configuration iframe dans le cache
            $this->cache->put(
                self::IFRAME_CONFIG_KEY,
                $iframeConfig,
                now()->addSeconds(self::IFRAME_CONFIG_LIFETIME_SECONDS)
            );
        } else {
            // Tenter de récupérer la configuration iframe du cache
            $this->iframeConfig = $this->cache->get(self::IFRAME_CONFIG_KEY);
        }

        if ($this->iframeConfig) {
            $this->initFromIframeConfig($this->iframeConfig);

            return;
        }

        $this->cache->forget(self::IFRAME_CONFIG_KEY);

        // Si pas de config iframe, charger depuis le cache normal
        $this->cacheKey = $this->session->get(self::SESSION_KEY);

        if ($this->cacheKey && $this->cache->has($this->cacheKey)) {
            /** @var array<string, mixed> $state */
            $state = $this->cache->get($this->cacheKey, []);

            if (isset($state['radio_station'])) {
                $this->radioStation = RadioStation::find($state['radio_station']);
                $this->setDisplayedRadioStations($this->radioStation);
            }
            if (isset($state['search'])) {
                $this->search = $state['search'];
            }
            if (isset($state['search_mode'])) {
                $this->searchMode = MapSearchMode::from($state['search_mode']);
            }
            if (isset($state['search_result'])) {
                $this->searchResult = $state['search_result'];
            }
            if (! empty($state['date_period'])) {
                if (isset($state['date_period'][0])) {
                    $this->since = \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $state['date_period'][0] . ' 00:00:00');
                }
                if (isset($state['date_period'][1])) {
                    $this->until = \DateTimeImmutable::createFromFormat('Y-m-d H:i:s', $state['date_period'][1] . ' 23:59:59');
                }
            }
            if (isset($state['active_place'])) {
                $this->activePlace = Place::find($state['active_place']);
            }
            if (isset($state['active_place_all_contents'])) {
                $this->activePlaceAllContents = $state['active_place_all_contents'] == 1;
            }
            if (isset($state['rail_content_type']) && isset($state['rail_place_id'])) {
                $this->railContentType = $state['rail_content_type'];
                $this->railPlaceId = $state['rail_place_id'];
            }
            if (isset($state['info_panel_target_type']) && isset($state['info_panel_target_id'])) {
                $this->infoPanelTargetType = $state['info_panel_target_type'];
                $this->infoPanelTargetId = $state['info_panel_target_id'];
            }
            if (isset($state['coordinates'])) {
                $this->coordinates = $state['coordinates'];
            }
            if (isset($state['zoom'])) {
                $this->zoom = $state['zoom'];
            }

            foreach (MapFilterType::cases() as $type) {
                $this->filters[$type->value] = $state['filters'][$type->value];
            }
        } else {
            if (! isset($this->filters['thematic']) || ! $this->filters['thematic']) {
                foreach (MapFilterType::cases() as $type) {
                    $this->filters[$type->value] = \array_fill_keys($type->loadValues(), 0);
                }
            }
        }
    }

    private function initFromIframeConfig(MapConfiguration $config): void
    {
        // Radio station
        $this->radioStation = $config->stationWinmediaId
            ? RadioStation::where('winmedia_id', $config->stationWinmediaId)->first()
            : null;
        $this->setDisplayedRadioStations($this->radioStation);

        // Search
        $this->search = $config->search;
        $this->searchMode = $config->searchMode ?? MapSearchMode::All;
        $this->searchResult = [];

        // Period
        $this->since = $config->since;
        $this->until = $config->until;

        // Place
        $this->activePlace = $config->activePlaceId ? Place::findOrFail($config->activePlaceId) : null;
        $this->activePlaceAllContents = $config->activePlaceAllContents == 1;

        // Rail
        $this->railContentType = $config->railContentType;
        $this->railPlaceId = $config->railPlaceId;

        // Info panel
        $this->infoPanelTargetType = $config->infoPanelTargetType;
        $this->infoPanelTargetId = $config->infoPanelTargetId;

        // Map position
        $this->zoom = $config->zoom;
        if ($config->longitude && $config->latitude) {
            $this->coordinates = [$config->longitude, $config->latitude];
        }

        // Filters
        foreach (MapFilterType::cases() as $type) {
            $this->filters[$type->value] = $config->filters[$type->value] ?? array_fill_keys($type->loadValues(), 0);
        }
    }

    public function setRadioStation(?RadioStation $radioStation): self
    {
        $this->radioStation = $radioStation;
        $this->setDisplayedRadioStations($radioStation);

        return $this;
    }

    public function setDisplayedRadioStations(?RadioStation $radioStation): self
    {
        if ($radioStation?->point()) {
            $stationsWithLocation = radioStations()->filter(function ($station) {
                return $station->contentLocations->isNotEmpty();
            });

            $this->displayedRadioStations = array_values(array_unique([$radioStation->id, ...$stationsWithLocation->pluck('id')]));
        } elseif ($radioStation) {
            $this->displayedRadioStations = [$radioStation->id];
        } else {
            $this->displayedRadioStations = [];
        }

        return $this;
    }

    public function getRadioStation(): ?RadioStation
    {
        return $this->radioStation;
    }

    public function getDisplayedRadioStation(): array
    {
        return $this->displayedRadioStations;
    }

    public function setActivePlace(?Place $place): self
    {
        $this->activePlace = $place;

        return $this;
    }

    public function getActivePlace(): ?Place
    {
        return $this->activePlace;
    }

    public function getActivePlaceWithAllContents(): ?Place
    {
        return $this->activePlaceAllContents ? $this->activePlace : null;
    }

    public function setCustomPeriod(\DateTimeInterface $since, \DateTimeInterface $until): self
    {
        $this->since = \DateTimeImmutable::createFromInterface($since);
        $this->until = \DateTimeImmutable::createFromInterface($until);

        return $this;
    }

    public function forgetCustomPeriod(): self
    {
        $this->since = $this->until = null;

        return $this;
    }

    /**
     * @return \DateTimeImmutable[]
     */
    public function getCustomPeriod(): array
    {
        if (! $this->since || ! $this->until) {
            return [];
        }

        return [$this->since, $this->until];
    }

    public function setSearch(?string $search): self
    {
        $this->search = $search ? \trim($search) : null;

        if (empty($this->search)) {
            $this->searchResult = [];
        }

        return $this;
    }

    public function getSearch(): ?string
    {
        return $this->search;
    }

    public function setSearchMode(MapSearchMode $mode): self
    {
        $this->searchMode = $mode;

        return $this;
    }

    public function getSearchMode(): MapSearchMode
    {
        return $this->searchMode;
    }

    public function setSearchResult(array $result): self
    {
        $this->searchResult = $result;

        return $this;
    }

    public function getSearchResult(): array
    {
        return $this->searchResult;
    }

    public function doesActiveSearchExist()
    {
        return ! empty($this->search);
    }

    public function doesSearchModeSelected()
    {
        return $this->searchMode != MapSearchMode::All;
    }

    public function enableFilter(MapFilterType $type, int $id): self
    {
        if (! isset($this->filters[$type->value][$id])) {
            throw new InvalidArgumentException('Invalid filter identifier.');
        }

        $this->filters[$type->value][$id] = true;

        return $this;
    }

    public function disableFilter(MapFilterType $type, int $id): self
    {
        if (! isset($this->filters[$type->value][$id])) {
            throw new InvalidArgumentException('Invalid filter identifier.');
        }

        $this->filters[$type->value][$id] = false;

        return $this;
    }

    public function enableAllFilters(MapFilterType $type): self
    {
        $this->filters[$type->value] = \array_fill_keys($type->loadValues(), 1);
        $this->save();

        return $this;
    }

    public function disableAllFilters(MapFilterType $type): self
    {
        $this->filters[$type->value] = \array_fill_keys($type->loadValues(), 0);
        $this->save();

        return $this;
    }

    public function toggleFilter(MapFilterType $type, int $id): self
    {
        if (! isset($this->filters[$type->value][$id])) {
            throw new InvalidArgumentException('Invalid filter identifier.');
        }
        $this->filters[$type->value][$id] = (int) ! $this->filters[$type->value][$id];
        $this->save();

        return $this;
    }

    /** @return array<int, bool> */
    public function getFilters(MapFilterType $type): array
    {
        return $this->filters[$type->value];
    }

    /** @return int[] */
    public function getFiltersEnable(MapFilterType $type): array
    {
        return \array_keys(\array_filter($this->filters[$type->value]));
    }

    public function isFilterEnabled(MapFilterType $type, int $id): bool
    {
        if (! isset($this->filters[$type->value][$id])) {
            throw new InvalidArgumentException('Invalid filter identifier.');
        }

        return (bool) $this->filters[$type->value][$id];
    }

    public function setRailParams(string $contentType, int $placeId): self
    {
        $this->railContentType = $contentType;
        $this->railPlaceId = $placeId;

        return $this;
    }

    public function forgetRailParams(): self
    {
        $this->railContentType = null;
        $this->railPlaceId = null;

        return $this;
    }

    public function getRailParams(): array
    {
        if ($this->railPlaceId && $this->railContentType) {
            return [
                'content_type' => $this->railContentType,
                'place_id' => $this->railPlaceId,
            ];
        }

        return [];
    }

    public function setInfoPanelParams(string $targetType, int $targetId): self
    {
        $this->infoPanelTargetType = $targetType;
        $this->infoPanelTargetId = $targetId;

        return $this;
    }

    public function forgetInfoPanelParams(): self
    {
        $this->infoPanelTargetType = null;
        $this->infoPanelTargetId = null;

        return $this;
    }

    public function getInfoPanelParams(): array
    {
        if ($this->infoPanelTargetType && $this->infoPanelTargetId) {
            return [
                'target_type' => $this->infoPanelTargetType,
                'target_id' => $this->infoPanelTargetId,
            ];
        }

        return [];
    }

    public function setCoordinates(float $lng, float $lat): self
    {
        $this->coordinates = [\round($lng, 6), \round($lat, 6)];

        return $this;
    }

    public function getCoordinates(): array
    {
        return $this->coordinates;
    }

    public function setZoom(float $zoom): self
    {
        $this->zoom = \round($zoom, 6);

        return $this;
    }

    public function getZoom(): ?float
    {
        return $this->zoom;
    }

    public function save(): void
    {
        $filters = [];
        //        foreach (MapFilterType::cases() as $type) {
        //            $filters[$type->value] = $this->getFilters($type);
        //        }
        $filters['thematic'] = $this->filters['thematic'];

        if (! $this->cacheKey) {
            $this->cacheKey = (string) Str::uuid();
            $this->session->put(self::SESSION_KEY, $this->cacheKey);
        }

        $this->cache->put($this->cacheKey, [
            'radio_station' => $this->radioStation?->id,
            'search' => $this->search,
            'search_mode' => $this->searchMode->value,
            'search_result' => $this->searchResult,
            'date_period' => ($this->since && $this->until)
                ? [$this->since->format('Y-m-d'), $this->until->format('Y-m-d')]
                : [],
            'filters' => $filters,
            'active_place' => $this->activePlace?->id,
            'active_place_all_contents' => $this->activePlaceAllContents ? 1 : 0,
            'rail_content_type' => $this->railContentType,
            'rail_place_id' => $this->railPlaceId,
            'info_panel_target_type' => $this->infoPanelTargetType,
            'info_panel_target_id' => $this->infoPanelTargetId,
            'coordinates' => $this->coordinates,
            'zoom' => $this->zoom,
        ], ($this->sessionLifetime + 3) * 60);
    }

    public function forget(): void
    {
        foreach (MapFilterType::cases() as $type) {
            foreach (\array_keys($this->filters[$type->value]) as $id) {
                $this->filters[$type->value][$id] = true;
            }
        }

        $this->radioStation = null;
        $this->search = null;
        $this->searchMode = MapSearchMode::All;
        $this->searchResult = [];
        $this->since = null;
        $this->until = null;
        $this->activePlace = null;
        $this->activePlaceAllContents = false;
        $this->railContentType = null;
        $this->railPlaceId = null;
        $this->infoPanelTargetType = null;
        $this->infoPanelTargetId = null;
        $this->coordinates = [];
        $this->zoom = null;

        $this->cache->forget($this->cacheKey);
        $this->session->forget(self::SESSION_KEY);
    }

    public function reset(?MapConfiguration $config = null): void
    {
        foreach (MapFilterType::cases() as $type) {

            if (! empty($config?->filters[$type->value])) {
                $this->filters[$type->value] = $config->filters[$type->value];
            }
        }

        if ($config?->stationWinmediaId) {
            $this->radioStation = RadioStation::where('winmedia_id', $config->stationWinmediaId)->firstOrFail();
            $this->setDisplayedRadioStations($this->radioStation);
        } else {
            $this->radioStation = null;
            $this->setDisplayedRadioStations($this->radioStation);
        }

        $this->search = $config?->search;
        $this->searchMode = $config?->searchMode ?? MapSearchMode::All;
        $this->searchResult = [];
        $this->since = $config?->since;
        $this->until = $config?->until;
        $this->activePlace = $config?->activePlaceId ? Place::findOrFail($config->activePlaceId) : null;
        $this->activePlaceAllContents = $config?->activePlaceAllContents == 1;
        $this->railContentType = $config?->railContentType;
        $this->railPlaceId = $config?->railPlaceId;
        $this->infoPanelTargetType = $config?->infoPanelTargetType;
        $this->infoPanelTargetId = $config?->infoPanelTargetId;
        $this->zoom = $config?->zoom;

        if ($config->longitude && $config->latitude) {
            $this->coordinates = [$config->longitude, $config->latitude];
        }

        $this->save();
    }
}

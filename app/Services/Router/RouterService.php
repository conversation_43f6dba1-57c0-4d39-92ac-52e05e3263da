<?php

namespace App\Services\Router;

use App\Http\Livewire\Router;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class RouterService
{
    public function getInitialRoute(string $initialUrl): array
    {
        // When requesting a specific URL
        $url = $this->cleanUrlFromParams($initialUrl);
        if (url($url) !== route('app.index')) {
            return $this->getRouteFromUrl($url);
        }

        // When requesting base app route
        return Auth::check()
            ? collect(Router::ROUTES)->where('key', Auth::user()->settings->default_home_page)->firstOrFail()
            : collect(Router::ROUTES)->first();
    }

    public function cleanUrlFromParams(string $url)
    {
        ['path' => $cleanedUrl] = parse_url($url);

        return $cleanedUrl;
    }

    public function getRouteFromUrl(string $url): array
    {
        $routeWithNoBinding = collect(Router::ROUTES)->where('url', $url)->first();
        if ($routeWithNoBinding) {
            return $routeWithNoBinding;
        }
        $routeWithBinding = $this->getRouteWithBindingFromBoundUrl($url);
        if ($routeWithBinding) {
            return $routeWithBinding;
        }
        abort(404);
    }

    protected function getRouteWithBindingFromBoundUrl(string $boundUrl): ?array
    {
        $routeWithBinding = null;
        foreach (Router::ROUTES as $route) {
            if (! array_key_exists('bindings', $route)) {
                continue;
            }
            $routeUrl = $route['url'];
            $routeBindingKeys = array_keys($route['bindings']);
            $bindingValues = $this->getRouteBindingValuesFromUrl($boundUrl, $routeUrl, $routeBindingKeys);
            if (! $bindingValues) {
                continue;
            }
            $routeUrlWithBindingsReplacement = $this->getUrlWithBindingsSubstituted($boundUrl, $bindingValues);
            if ($routeUrlWithBindingsReplacement === $boundUrl) {
                foreach ($routeBindingKeys as $routeBindingKey) {
                    $route['params']['bindings'][$routeBindingKey] = [
                        'model' => $route['bindings'][$routeBindingKey],
                        'id' => $bindingValues[$routeBindingKey],
                    ];
                }
                $routeWithBinding = $route;
                break;
            }
        }

        return $routeWithBinding;
    }

    protected function getRouteBindingValuesFromUrl(string $url, string $routeUrl, array $routeBindingKeys): ?array
    {
        $routeUrlContainsBindingKeys = Str::contains($routeUrl, $routeBindingKeys);
        if (! $routeUrlContainsBindingKeys) {
            return null;
        }
        $routeUrlWithRegex = $this->convertUrlBindingPartsInRegex($routeUrl, $routeBindingKeys);
        $bindings = [];
        preg_match('/' . str_replace('/', '\/', $routeUrlWithRegex) . '/', $url, $bindings);

        return array_filter($bindings, 'is_string', ARRAY_FILTER_USE_KEY);
    }

    protected function convertUrlBindingPartsInRegex(string $url, $bindingKeys): string
    {
        foreach ($bindingKeys as $bindingKey) {
            $url = preg_replace('/{' . $bindingKey . '}/', '(?<' . $bindingKey . '>\w+)', $url);
        }

        return $url;
    }

    public function getUrlWithBindingsSubstituted(string $url, array $bindings): string
    {
        $bindingsToSubstitute = [];
        foreach ($bindings as $bindingKey => $bindingValue) {
            $bindingsToSubstitute['{' . $bindingKey . '}'] = (string) $bindingValue;
        }

        return Str::replace(array_keys($bindingsToSubstitute), array_values($bindingsToSubstitute), $url);
    }

    public function getParamsFromUrl(string $url): array
    {
        $urlData = parse_url($url);
        $paramsString = data_get($urlData, 'query');
        if (! $paramsString) {
            return [];
        }
        parse_str($paramsString, $params);

        return $params;
    }

    public function instantiateParamBindings(array $params): array
    {
        if (! array_key_exists('bindings', $params)) {
            return $params;
        }
        foreach (array_keys($params['bindings']) as $bindingKey) {
            $model = $params['bindings'][$bindingKey]['model'];
            $id = $params['bindings'][$bindingKey]['id'];
            $params[$bindingKey] = app($model)::findOrFail($id);
        }
        unset($params['bindings']);

        return $params;
    }
}

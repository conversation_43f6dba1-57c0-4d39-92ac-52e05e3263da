<?php

namespace App\Services\Users;

use App\Models\Radio\RadioStation;
use App\Models\Users\UserJourney;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class UserJourneysService
{
    use ManagesUserJourneyData;

    protected const ATTRIBUTES_TO_MIGRATE = [
        'selected_radio_station_universe_id',
        'current_route_key',
        'played_audio_source_class',
        'played_audio_source_id',
        'played_audio_source_params',
        'played_sub_audio_source_class',
        'played_sub_audio_source_id',
        'player_audio_stream_is_playing',
    ];

    public function setSelectedRadioStationUniverse(RadioStation $radioStation): void
    {
        $this->saveUserJourneyData(['selected_radio_station_universe_id' => $radioStation->id]);
    }

    /** @throws \Exception */
    public function getSelectedRadioStationUniverseId(): int
    {
        $selectedRadioStationUniverseId = $this->getUserJourneyData(['selected_radio_station_universe_id']);

        return Arr::first($selectedRadioStationUniverseId) ?: radioStations()->first()->id;
    }

    public function setSelectedDefaultSettingRadioStationId(?int $radioStationId): void
    {
        $this->saveUserJourneyData(['selected_default_setting_radio_station_id' => $radioStationId]);
    }

    /** @throws \Exception */
    public function getSelectedDefaultSettingRadioStationId(): ?int
    {
        $selectedDefaultSettingRadioId = $this->getUserJourneyData(['selected_default_setting_radio_station_id']);

        return Arr::first($selectedDefaultSettingRadioId) ?: null;
    }

    public function setSelectedDefaultSettingWebradioId(?int $radioStationId): void
    {
        $this->saveUserJourneyData(['selected_default_setting_webradio_id' => $radioStationId]);
    }

    /** @throws \Exception */
    public function getSelectedDefaultSettingWebradioId(): ?int
    {
        $selectedDefaultSettingRadioId = $this->getUserJourneyData(['selected_default_setting_webradio_id']);

        return Arr::first($selectedDefaultSettingRadioId) ?: null;
    }

    public function setCurrentRouteKey(string $routeKey): void
    {
        $this->saveUserJourneyData(['current_route_key' => $routeKey]);
    }

    public function getCurrentRouteKey(): ?string
    {
        return Arr::first($this->getUserJourneyData(['current_route_key']));
    }

    public function setPlayedAudioSource(
        string $audioSourceClass,
        int $audioSourceId,
        ?array $audioSourceParams = null,
    ): void {
        $this->saveUserJourneyData([
            'played_audio_source_class' => $audioSourceClass,
            'played_audio_source_id' => $audioSourceId,
            'played_audio_source_params' => $audioSourceParams,
        ]);
    }

    public function getPlayedAudioSource(): array
    {
        return $this->getUserJourneyData([
            'played_audio_source_class',
            'played_audio_source_id',
            'played_audio_source_params',
        ]);
    }

    public function setPlayedSubAudioSource(?string $subAudioSourceClass, ?int $subAudioSourceId): void
    {
        $this->saveUserJourneyData([
            'played_sub_audio_source_class' => $subAudioSourceClass,
            'played_sub_audio_source_id' => $subAudioSourceId,
        ]);
    }

    public function getPlayedSubAudioSource(): array
    {
        return $this->getUserJourneyData(['played_sub_audio_source_class', 'played_sub_audio_source_id']);
    }

    public function setPlayerPlayingStatus(bool $playerIsPlaying): void
    {
        $this->saveUserJourneyData(['player_audio_stream_is_playing' => $playerIsPlaying]);
    }

    public function getPlayerPlayingStatus(): bool
    {
        return (bool) Arr::first($this->getUserJourneyData(['player_audio_stream_is_playing']));
    }

    public function migrateAllUserJourneyDataFromSessionToAuthUser(string $loggedOutSessionId): void
    {
        $sessionUserJourney = UserJourney::firstWhere('session_id', $loggedOutSessionId);
        $dataToMigrateFromSession = [];
        foreach (self::ATTRIBUTES_TO_MIGRATE as $attribute) {
            $dataToMigrateFromSession[$attribute] = $sessionUserJourney?->{$attribute};
        }
        UserJourney::updateOrCreate(['user_id' => Auth::id()], $dataToMigrateFromSession);
        $sessionUserJourney?->delete();
    }

    public function migrateAllUserJourneyDataFromAuthUserToSession(int $loggedOutUserId): void
    {
        $loggedOutUserJourney = UserJourney::firstWhere('user_id', $loggedOutUserId);
        $dataToMigrateFromAuthUser = [];
        foreach (self::ATTRIBUTES_TO_MIGRATE as $attribute) {
            $dataToMigrateFromAuthUser[$attribute] = $loggedOutUserJourney?->{$attribute};
        }
        UserJourney::updateOrCreate(['session_id' => Session::getId()], $dataToMigrateFromAuthUser);
    }
}

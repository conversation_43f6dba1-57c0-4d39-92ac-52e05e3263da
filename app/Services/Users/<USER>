<?php

namespace App\Services\Users;

use App\Models\Users\UserJourney;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Session;

trait ManagesUserJourneyData
{
    protected function saveUserJourneyData(array $payload): void
    {
        $sessionId = Session::getId();
        $userJourney = $this->getUserJourney($sessionId);
        if ($userJourney) {
            $userJourney->update($payload);

            return;
        }
        $this->performCreate($sessionId, $payload);
    }

    protected function getUserJourney(string $sessionId): ?UserJourney
    {
        return Auth::check()
            ? UserJourney::firstWhere('user_id', Auth::id())
            : UserJourney::firstWhere('session_id', $sessionId);
    }

    protected function performCreate(string $sessionId, array $payload = []): UserJourney
    {
        try {
            DB::beginTransaction();
            $userJourney = UserJourney::create(array_merge(
                Auth::check() ? ['user_id' => Auth::id()] : ['session_id' => $sessionId],
                $payload,
            ));
            DB::commit();

            return $userJourney;
        } catch (QueryException) {
            DB::rollBack();
            $userJourney = $this->getUserJourney($sessionId);
            $userJourney->update($payload);

            return $userJourney;
        }
    }

    protected function getUserJourneyData(array $attributes): array
    {
        $sessionId = Session::getId();
        $userJourney = $this->getUserJourney($sessionId);
        if (! $userJourney) {
            $userJourney = $this->performCreate($sessionId);
        }

        return $userJourney->only($attributes);
    }
}

<?php

namespace App\Services\Users;

use App\Models\Users\User;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;

class UsersService
{
    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function saveProfilePictureFromRequest(Request $request, User $user, bool $isUpdateRequest): void
    {
        if ($request->remove_profile_picture || $request->file('profile_picture') || ! $isUpdateRequest) {
            $file = $request->remove_profile_picture ? null : $request->file('profile_picture');
            $this->saveAvatarFromUploadedFile($file, $user);
        }
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function saveAvatarFromUploadedFile(?UploadedFile $file, User $user): void
    {
        $file
            ? $user->addMedia($file)->toMediaCollection('profile_picture')
            : $this->setDefaultAvatar($user);
    }

    /**
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function setDefaultAvatar(User $user): void
    {
        $avatars = array_values(array_diff(scandir(resource_path('seeds/default-avatars')), ['.', '..']));
        $randomAvatar = $avatars[rand(0, count($avatars) - 1)];
        $user->addMedia(resource_path('seeds/default-avatars') . '/' . $randomAvatar)
            ->preservingOriginal()
            ->toMediaCollection('profile_picture');
    }
}

<?php

namespace App\Services\Elasticsearch;

use App\Models\Audio\Podcast;
use App\Models\Events\Event;
use App\Models\News\NewsArticle;
use Illuminate\Support\Collection;

class SuggestedContentService
{
    protected ElasticsearchService $elasticsearchService;

    protected string $elasticsearchIndex;

    public function __construct()
    {
        $this->elasticsearchService = app(ElasticsearchService::class);
    }

    public function getSuggestedNewsAndPodcasts(string $esSourceContentIndex, string $esSourceContentId): Collection
    {
        $suggestedContents = collect();
        $moreLikeThisContent = $this->elasticsearchService->searchMoreLikeThisAdvanced(
            $esSourceContentIndex,
            $esSourceContentId,
            ['title', 'description', 'tags'],
            collect([
                config('services.elasticsearch.indexes.news_articles'),
                config('services.elasticsearch.indexes.podcasts'),
            ])->implode(','),
            [
                ['range' => ['published_at' => ['boost' => 5, 'lte' => 'now', 'gt' => 'now-365d']]],
                ['range' => ['published_at' => ['boost' => 2, 'lte' => 'now-365d', 'gt' => 'now-999d']]],
                ['range' => ['published_at' => ['boost' => 0, 'lte' => 'now-999d']]],
            ],
            [],
            10
        );
        foreach ($moreLikeThisContent['hits']['hits'] as $item) {
            if ($item['_index'] === config('services.elasticsearch.indexes.news_articles')) {
                $news = NewsArticle::find($item['_source']['id']);
                if ($news) {
                    $suggestedContents->push($news);
                }
            } elseif ($item['_index'] === config('services.elasticsearch.indexes.podcasts')) {
                $podcast = Podcast::find($item['_source']['id']);
                if ($podcast) {
                    $suggestedContents->push($podcast);
                }
            }
        }

        return $suggestedContents;
    }

    public function getSuggestedEvents(string $esSourceContentIndex, string $esSourceContentId): Collection
    {
        $suggestedEvents = collect();
        $moreLikeThisContent = $this->elasticsearchService->searchMoreLikeThisAdvanced(
            $esSourceContentIndex,
            $esSourceContentId,
            ['title', 'description', 'tags'],
            config('services.elasticsearch.indexes.events'),
            [],
            [
                'range' => ['started_at' => ['gt' => 'now']],
            ],
            6
        );
        foreach ($moreLikeThisContent['hits']['hits'] as $item) {
            $event = Event::find($item['_source']['id']);
            if ($event) {
                $suggestedEvents->push($event);
            }
        }

        return $suggestedEvents;
    }
}

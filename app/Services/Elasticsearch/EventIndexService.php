<?php

namespace App\Services\Elasticsearch;

use App\Models\Events\Event;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;

class EventIndexService
{
    protected ElasticsearchService $elasticsearchService;

    protected string $elasticsearchIndex;

    public function __construct()
    {
        $this->elasticsearchService = app(ElasticsearchService::class);
        $this->elasticsearchIndex = config('services.elasticsearch.indexes.events');
    }

    public function updateOrCreate(Event $event): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $event->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->updateDocument(
                $this->elasticsearchIndex,
                $esEntry['hits']['hits'][0]['_id'],
                $this->getEventData($event)
            );

            return;
        }
        $this->elasticsearchService->indexDocument($this->elasticsearchIndex, $this->getEventData($event));
    }

    public function delete(Event $event): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $event->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->deleteDocument($this->elasticsearchIndex, $esEntry['hits']['hits'][0]['_id']);
        }
    }

    protected function getEventData(Event $event): array
    {
        return [
            'id' => $event->id,
            'audio_source_class' => $event::class,
            'title' => $event->title,
            'description' => $event->description,
            'tags' => $event->tags,
            'cover_thumb' => $event->getFirstMediaUrl('cover', 'card'),
            'location_id' => $event->location()->id,
            'location_type' => $event->location()::class,
            'location_name' => $event->locationName(),
            'location_latitude' => $event->point()->getLatitude(),
            'location_longitude' => $event->point()->getLongitude(),
            'active' => $event->active,
            'started_at' => $event->started_at->toDatetimeString(),
            'ended_at' => $event->ended_at->toDatetimeString(),
            'thematic_id' => $event->thematic_id,
            'thematic_title' => $event->thematic?->title,
            'radio_stations' => $event->radioStations
                ->map(static fn (RadioStation $radio) => ['id' => $radio->id, 'name' => $radio->name]),
            'authors' => $event->author()->get()
                ->map(static fn (User $user) => ['id' => $user->id, 'username' => $user->username]),
            'has_audio' => $event->has_audio,
        ];
    }
}

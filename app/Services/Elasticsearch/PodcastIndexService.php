<?php

namespace App\Services\Elasticsearch;

use App\Models\Audio\Podcast;
use App\Models\Users\User;

class PodcastIndexService
{
    protected ElasticsearchService $elasticsearchService;

    protected string $elasticsearchIndex;

    public function __construct()
    {
        $this->elasticsearchService = app(ElasticsearchService::class);
        $this->elasticsearchIndex = config('services.elasticsearch.indexes.podcasts');
    }

    public function updateOrCreate(Podcast $podcast): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $podcast->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->updateDocument(
                $this->elasticsearchIndex,
                $esEntry['hits']['hits'][0]['_id'],
                $this->getPodcastData($podcast)
            );

            return;
        }
        $this->elasticsearchService->indexDocument($this->elasticsearchIndex, $this->getPodcastData($podcast));
    }

    protected function getPodcastData(Podcast $podcast): array
    {
        $location = $podcast->location();

        return [
            'id' => $podcast->id,
            'audio_source_class' => $podcast::class,
            'cover_thumb' => $podcast->getFirstMediaUrl('cover', 'medium'),
            'title' => $podcast->title,
            'tags' => $podcast->tags,
            'description' => $podcast->description,
            'songs_performers' => $podcast->songs()->pluck('performer')->implode(', '),
            'songs_titles' => $podcast->songs()->pluck('title')->implode(', '),
            'human_readable_duration' => $podcast->human_readable_duration,
            'thematic_id' => $podcast->thematic_id,
            'thematic_title' => $podcast->thematic?->title,
            'program_id' => $podcast->program_id,
            'program_title' => $podcast->program->title,
            'program_tags' => $podcast->program->tags,
            'location_type' => $location ? $location::class : null,
            'location_id' => $location?->id,
            'published_at' => $podcast->published_at,
            'authors' => $podcast->authors()->get()
                ->map(static fn (User $user) => ['id' => $user->id, 'username' => $user->username]),
        ];
    }

    public function delete(Podcast $podcast): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $podcast->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->deleteDocument($this->elasticsearchIndex, $esEntry['hits']['hits'][0]['_id']);
        }
    }
}

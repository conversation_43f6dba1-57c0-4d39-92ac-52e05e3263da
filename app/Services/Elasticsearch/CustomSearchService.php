<?php

namespace App\Services\Elasticsearch;

use Illuminate\Support\Facades\Date;
use Illuminate\Support\Str;

class CustomSearchService
{
    protected ElasticsearchService $elasticsearchService;

    public const FILTER_AUTHOR = 'auteur:';

    public const FILTER_TAGS = 'tag:';

    public const FILTER_DATE = 'date:';

    public const FILTER_PROGRAM = 'emission:';

    public const FILTER_GENRE = 'genre:';

    public function __construct()
    {
        $this->elasticsearchService = app(ElasticsearchService::class);
    }

    public function searchPerformers(
        string $value,
        int $size = 7,
        int $from = 0,
        array $filterCondition = [],
    ): array {
        $index = config('services.elasticsearch.indexes.performers');

        if (Str::startsWith($value, self::FILTER_TAGS)) {
            $query = [
                'bool' => [
                    'must' => [
                        'term' => [
                            'name.keyword' => [
                                'value' => Str::replaceFirst(self::FILTER_TAGS, '', $value),
                                'case_insensitive' => true,
                            ],
                        ],
                    ],
                ],
            ];

            if ($filterCondition) {
                $query['bool']['filter'] = $filterCondition;
            }

            return $this->elasticsearchService->searchFilterCustom($index, $size, $query, [], $from);
        } elseif (Str::startsWith($value, self::FILTER_GENRE)) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_phrase' => ['music_styles' => Str::replaceFirst(self::FILTER_GENRE, '', $value)],
                    ],
                ],
            ];

            if ($filterCondition) {
                $query['bool']['filter'] = $filterCondition;
            }

            return $this->elasticsearchService->searchFilterCustom($index, $size, $query, [], $from);
        }

        $mustCondition = [];

        $shouldCondition = [
            [
                'match_phrase' => [
                    'name' => [
                        'query' => $value,
                        'boost' => 10,
                    ],
                ],
            ],
            [
                'match' => [
                    'name' => [
                        'query' => $value,
                        'operator' => 'and',
                        'auto_generate_synonyms_phrase_query' => false,
                        'fuzzy_transpositions' => false,
                        'fuzziness' => '0',
                        'max_expansions' => '10',
                        'boost' => 2,
                    ],
                ],
            ],
            [
                'match' => [
                    'name' => [
                        'query' => $value,
                        'operator' => 'or',
                        'auto_generate_synonyms_phrase_query' => false,
                        'fuzzy_transpositions' => false,
                        'fuzziness' => '1',
                        'boost' => 0.5,
                    ],
                ],
            ],
            [
                'nested' => [
                    'path' => 'albums',
                    'query' => [
                        'match' => [
                            'albums.name' => [
                                'query' => $value,
                                'operator' => 'or',
                                'auto_generate_synonyms_phrase_query' => false,
                                'fuzzy_transpositions' => false,
                                'fuzziness' => '1',
                            ],
                        ],
                    ],
                    'boost' => 0.2,
                ],
            ],
            [
                'nested' => [
                    'path' => 'albums',
                    'query' => [
                        'match' => [
                            'albums.name' => [
                                'query' => $value,
                                'operator' => 'and',
                                'auto_generate_synonyms_phrase_query' => false,
                                'fuzzy_transpositions' => false,
                                'fuzziness' => '0',
                            ],
                        ],
                    ],
                    'boost' => 0.8,
                ],
            ],
        ];

        return $this->elasticsearchService->searchAdvancedCustom(
            $index,
            $size,
            $mustCondition,
            $filterCondition,
            $shouldCondition,
            $from,
            minimumShouldMatch: 1
        );
    }

    public function searchSongs(
        string $value,
        int $size = 5,
        int $from = 0,
        ?array $pluckFields = null,
        bool $withHidden = false,
    ): array {
        $hiddenFilter = $withHidden ? [] : ['term' => ['hidden' => false]];
        if (Str::startsWith($value, self::FILTER_TAGS)) {
            return $this->elasticsearchService->searchFilterCustom(
                config('services.elasticsearch.indexes.songs'),
                $size,
                [
                    'bool' => [
                        'must' => [
                            'term' => [
                                'performer_name.keyword' => [
                                    'value' => Str::replaceFirst(self::FILTER_TAGS, '', $value),
                                    'case_insensitive' => true,
                                ],
                            ],
                        ],
                        'filter' => $hiddenFilter,
                    ],
                ],
                [],
                $from
            );
        } elseif (Str::startsWith($value, self::FILTER_GENRE)) {
            return $this->elasticsearchService->searchFilterCustom(
                config('services.elasticsearch.indexes.songs'),
                $size,
                [
                    'bool' => [
                        'must' => [
                            'match_phrase' => ['genre' => Str::replaceFirst(self::FILTER_GENRE, '', $value)],
                        ],
                        'filter' => $hiddenFilter,
                    ],
                ],
                [],
                $from
            );
        }

        $mustCondition = [];
        $filterCondition = $hiddenFilter;
        $shouldCondition = [
            [
                'match_phrase' => [
                    'performer_name' => [
                        'query' => $value,
                        'boost' => 10,
                    ],
                ],
            ],
            [
                'match_phrase' => [
                    'title' => [
                        'query' => $value,
                        'boost' => 3,
                    ],
                ],
            ],
            [
                'match' => [
                    'performer_name' => [
                        'query' => $value,
                        'operator' => 'or',
                        'auto_generate_synonyms_phrase_query' => false,
                        'fuzzy_transpositions' => false,
                        'fuzziness' => '1',
                        'boost' => 2,
                    ],
                ],
            ],
            [
                'match' => [
                    'title' => [
                        'query' => $value,
                        'operator' => 'or',
                        'auto_generate_synonyms_phrase_query' => false,
                        'fuzzy_transpositions' => false,
                        'fuzziness' => '1',
                        'boost' => 2,
                    ],
                ],
            ],
            [
                'match' => [
                    'album_name' => [
                        'query' => $value,
                        'operator' => 'or',
                        'auto_generate_synonyms_phrase_query' => false,
                        'fuzzy_transpositions' => true,
                        'fuzziness' => '2',
                        'boost' => 0.5,
                    ],
                ],
            ],
        ];

        return $this->elasticsearchService->searchAdvancedCustom(
            config('services.elasticsearch.indexes.songs'),
            $size,
            $mustCondition,
            $filterCondition,
            $shouldCondition,
            $from,
            $pluckFields
        );
    }

    public function searchPodcasts(
        string $value,
        int $size = 14,
        int $from = 0,
        array $filterCondition = [],
    ): array {
        $index = config('services.elasticsearch.indexes.podcasts');

        if (Str::startsWith($value, self::FILTER_AUTHOR)) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_phrase' => ['authors.username' => Str::replaceFirst(self::FILTER_AUTHOR, '', $value)],
                    ],
                    'filter' => \array_merge($filterCondition, [
                        ['range' => ['published_at' => ['lt' => 'now']]],
                    ]),
                ],
            ];
        } elseif (Str::startsWith($value, self::FILTER_TAGS)) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_phrase' => ['tags' => Str::replaceFirst(self::FILTER_TAGS, '', $value)],
                    ],
                    'filter' => \array_merge($filterCondition, [
                        ['range' => ['published_at' => ['lt' => 'now']]],
                    ]),
                ],
            ];
        } elseif (Str::startsWith($value, self::FILTER_DATE) && $this->validateDate(Str::replaceFirst(self::FILTER_DATE, '', $value))) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_all' => (object) [],
                    ],
                    'filter' => \array_merge($filterCondition, [
                        [
                            'range' => [
                                'published_at' => [
                                    'gte' => Str::replaceFirst(self::FILTER_DATE, '', $value),
                                    'lte' => Str::replaceFirst(self::FILTER_DATE, '', $value),
                                ],
                            ],
                        ],
                        [
                            'range' => [
                                'published_at' => [
                                    'lt' => 'now',
                                ],
                            ],
                        ],
                    ]),
                ],
            ];
        } elseif (Str::startsWith($value, self::FILTER_PROGRAM)) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_phrase' => ['program_id' => (int) Str::replaceFirst(self::FILTER_PROGRAM, '', $value)],
                    ],
                    'filter' => \array_merge($filterCondition, [
                        ['range' => ['published_at' => ['lt' => 'now']]],
                    ]),
                ],
            ];
        }

        if (isset($query)) {
            return $this->elasticsearchService->searchFilterCustom(
                $index,
                $size,
                $query,
                [
                    'published_at' => [
                        'order' => 'desc',
                        'format' => 'strict_date_optional_time_nanos',
                    ],
                ],
                $from
            );
        }

        $mustCondition = [
            [
                'bool' => [
                    'should' => [
                        [
                            'match_phrase' => [
                                'program_title' => [
                                    'query' => $value,
                                    'boost' => 10,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'program_title' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '2',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match_phrase' => [
                                'title' => [
                                    'query' => $value,
                                    'boost' => 6,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'title' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'title' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '1',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match_phrase' => [
                                'tags' => [
                                    'query' => $value,
                                    'boost' => 8,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'tags' => [
                                    'query' => $value,
                                    'operator' => 'or',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '1',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'tags' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 3,
                                ],
                            ],
                        ],
                        [
                            'match_phrase' => [
                                'program_tags' => [
                                    'query' => $value,
                                    'boost' => 6,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'program_tags' => [
                                    'query' => $value,
                                    'operator' => 'or',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '1',
                                    'boost' => 1,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'program_tags' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match_phrase' => [
                                'songs_performers' => [
                                    'query' => $value,
                                    'boost' => 3,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'songs_performers' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'description' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 0.5,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'description' => [
                                    'query' => $value,
                                    'operator' => 'or',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 0.1,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $filterCondition = \array_merge($filterCondition, [
            ['range' => ['published_at' => ['lt' => 'now']]],
        ]);

        $shouldCondition = [
            ['range' => ['published_at' => ['boost' => 100, 'lte' => 'now', 'gt' => 'now-1d']]],
            ['range' => ['published_at' => ['boost' => 95, 'lte' => 'now-1d', 'gt' => 'now-2d']]],
            ['range' => ['published_at' => ['boost' => 90, 'lte' => 'now-2d', 'gt' => 'now-3d']]],
            ['range' => ['published_at' => ['boost' => 85, 'lte' => 'now-3d', 'gt' => 'now-7d']]],
            ['range' => ['published_at' => ['boost' => 60, 'lte' => 'now-7d', 'gt' => 'now-14d']]],
            ['range' => ['published_at' => ['boost' => 50, 'lte' => 'now-14d', 'gt' => 'now-21d']]],
            ['range' => ['published_at' => ['boost' => 40, 'lte' => 'now-21d', 'gt' => 'now-33d']]],
            ['range' => ['published_at' => ['boost' => 30, 'lte' => 'now-33d', 'gt' => 'now-120d']]],
            ['range' => ['published_at' => ['boost' => 20, 'lte' => 'now-120d', 'gt' => 'now-365d']]],
            ['range' => ['published_at' => ['boost' => 10, 'lte' => 'now-365d', 'gt' => 'now-550d']]],
            ['range' => ['published_at' => ['boost' => 5, 'lte' => 'now-550d', 'gt' => 'now-730d']]],
            ['range' => ['published_at' => ['boost' => 0, 'lte' => 'now-730d']]],
        ];

        return $this->elasticsearchService->searchAdvancedCustom(
            $index,
            $size,
            $mustCondition,
            $filterCondition,
            $shouldCondition,
            $from
        );
    }

    public function searchPlaylists(
        string $value,
        int $size = 7,
        int $from = 0,
        array $filterCondition = [],
    ): array {
        if (Str::startsWith($value, self::FILTER_TAGS)) {
            $mustCondition = [
                'bool' => [
                    'must' => [
                        'match_phrase' => [
                            'songs_performers' => Str::replaceFirst(self::FILTER_TAGS, '', $value),
                        ],
                    ],
                ],
            ];
        } else {
            $mustCondition = [
                [
                    'bool' => [
                        'should' => [
                            [
                                'match_phrase' => [
                                    'title' => [
                                        'query' => $value,
                                        'boost' => 10,
                                    ],
                                ],
                            ],
                            [
                                'match' => [
                                    'title' => [
                                        'query' => $value,
                                        'operator' => 'and',
                                        'auto_generate_synonyms_phrase_query' => false,
                                        'fuzzy_transpositions' => false,
                                        'fuzziness' => '0',
                                        'boost' => 4,
                                    ],
                                ],
                            ],
                            [
                                'match' => [
                                    'title' => [
                                        'query' => $value,
                                        'operator' => 'or',
                                        'auto_generate_synonyms_phrase_query' => false,
                                        'fuzzy_transpositions' => false,
                                        'fuzziness' => '1',
                                        'boost' => 2,
                                    ],
                                ],
                            ],
                            [
                                'match_phrase' => [
                                    'songs_performers' => [
                                        'query' => $value,
                                        'boost' => 3,
                                    ],
                                ],
                            ],
                            [
                                'match' => [
                                    'songs_performers' => [
                                        'query' => $value,
                                        'operator' => 'and',
                                        'auto_generate_synonyms_phrase_query' => false,
                                        'fuzzy_transpositions' => false,
                                        'fuzziness' => '0',
                                        'boost' => 2,
                                    ],
                                ],
                            ],
                            [
                                'match' => [
                                    'songs_titles' => [
                                        'query' => $value,
                                        'operator' => 'and',
                                        'auto_generate_synonyms_phrase_query' => false,
                                        'fuzzy_transpositions' => false,
                                        'fuzziness' => '0',
                                        'boost' => 1,
                                    ],
                                ],
                            ],
                        ],
                    ],
                ],
            ];
        }

        $shouldCondition = [
            ['range' => ['published_at' => ['boost' => 30, 'lte' => 'now', 'gt' => 'now-7d']]],
            ['range' => ['published_at' => ['boost' => 20, 'lte' => 'now-7d', 'gt' => 'now-60d']]],
            ['range' => ['published_at' => ['boost' => 10, 'lte' => 'now-60d', 'gt' => 'now-365d']]],
            ['range' => ['published_at' => ['boost' => 0, 'lte' => 'now-365d']]],
        ];

        return $this->elasticsearchService->searchAdvancedCustom(
            config('services.elasticsearch.indexes.playlists'),
            $size,
            $mustCondition,
            $filterCondition,
            $shouldCondition,
            $from
        );
    }

    public function searchNews(
        string $value,
        int $size = 7,
        int $from = 0,
        array $filterCondition = [],
    ): array {
        $index = config('services.elasticsearch.indexes.news_articles');

        if (Str::startsWith($value, self::FILTER_AUTHOR)) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_phrase' => ['authors.username' => Str::replaceFirst(self::FILTER_AUTHOR, '', $value)],
                    ],
                    'filter' => \array_merge($filterCondition, [
                        ['range' => ['published_at' => ['lt' => 'now']]],
                    ]),
                ],
            ];
        } elseif (Str::startsWith($value, self::FILTER_TAGS)) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_phrase' => ['tags' => Str::replaceFirst(self::FILTER_TAGS, '', $value)],
                    ],
                    'filter' => \array_merge($filterCondition, [
                        ['range' => ['published_at' => ['lt' => 'now']]],
                    ]),
                ],
            ];
        } elseif (Str::startsWith($value, self::FILTER_DATE) && $this->validateDate(Str::replaceFirst(self::FILTER_DATE, '', $value))) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_all' => (object) [],
                    ],
                    'filter' => \array_merge($filterCondition, [
                        [
                            'range' => [
                                'published_at' => [
                                    'gte' => Str::replaceFirst(self::FILTER_DATE, '', $value),
                                    'lte' => Str::replaceFirst(self::FILTER_DATE, '', $value),
                                ],
                            ],
                        ],
                        [
                            'range' => [
                                'published_at' => [
                                    'lt' => 'now',
                                ],
                            ],
                        ],
                    ]),
                ],
            ];
        }

        if (isset($query)) {
            return $this->elasticsearchService->searchFilterCustom(
                $index,
                $size,
                $query,
                [
                    'published_at' => [
                        'order' => 'desc',
                        'format' => 'strict_date_optional_time_nanos',
                    ],
                ],
                $from
            );
        }

        $mustCondition = [
            [
                'bool' => [
                    'should' => [
                        [
                            'match' => [
                                'title' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '1',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'tags' => [
                                    'query' => $value,
                                    'operator' => 'or',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '1',
                                    'boost' => 1,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'tags' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'description' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 0.5,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'description' => [
                                    'query' => $value,
                                    'operator' => 'or',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 0.1,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        $filterCondition = \array_merge($filterCondition, [
            ['range' => ['published_at' => ['lte' => 'now']]],
        ]);

        $shouldCondition = [
            ['range' => ['published_at' => ['boost' => 30, 'lte' => 'now', 'gt' => 'now-3d']]],
            ['range' => ['published_at' => ['boost' => 20, 'lte' => 'now-3d', 'gt' => 'now-7d']]],
            ['range' => ['published_at' => ['boost' => 15, 'lte' => 'now-7d', 'gt' => 'now-30d']]],
            ['range' => ['published_at' => ['boost' => 10, 'lte' => 'now-30d', 'gt' => 'now-120d']]],
            ['range' => ['published_at' => ['boost' => 5, 'lte' => 'now-120d', 'gt' => 'now-365d']]],
            ['range' => ['published_at' => ['boost' => 0, 'lte' => 'now-365d']]],
        ];

        return $this->elasticsearchService->searchAdvancedCustom(
            $index,
            $size,
            $mustCondition,
            $filterCondition,
            $shouldCondition,
            $from
        );
    }

    public function searchEvents(
        string $value,
        int $size = 7,
        int $from = 0,
        array $filterCondition = [],
    ): array {
        $index = config('services.elasticsearch.indexes.events');

        $shouldCondition = [
            ['range' => ['started_at' => ['boost' => 5, 'gt' => 'now+60d']]],
            ['range' => ['started_at' => ['boost' => 10, 'lte' => 'now+60d', 'gt' => 'now+30d']]],
            ['range' => ['started_at' => ['boost' => 20, 'lte' => 'now+30d', 'gt' => 'now+14d']]],
            ['range' => ['started_at' => ['boost' => 30, 'lte' => 'now+14d', 'gt' => 'now+3d']]],
            ['range' => ['started_at' => ['boost' => 40, 'lte' => 'now+3d', 'gt' => 'now+1d']]],
            ['range' => ['started_at' => ['boost' => 30, 'lte' => 'now+1d', 'gt' => 'now']]],
            ['range' => ['started_at' => ['boost' => 20, 'lte' => 'now', 'gt' => 'now-3d']]],
            ['range' => ['started_at' => ['boost' => 15, 'lte' => 'now-3d', 'gt' => 'now-7d']]],
            ['range' => ['started_at' => ['boost' => 10, 'lte' => 'now-7d', 'gt' => 'now-30d']]],
            ['range' => ['started_at' => ['boost' => 5, 'lte' => 'now-30d', 'gt' => 'now-365d']]],
            ['range' => ['started_at' => ['boost' => 0, 'lte' => 'now-365d']]],
        ];

        if (Str::startsWith($value, self::FILTER_AUTHOR)) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_phrase' => ['authors.username' => Str::replaceFirst(self::FILTER_AUTHOR, '', $value)],
                    ],
                    'should' => $shouldCondition,
                ],
            ];

            if ($filterCondition) {
                $query['bool']['filter'] = $filterCondition;
            }
        } elseif (Str::startsWith($value, self::FILTER_TAGS)) {
            $query = [
                'bool' => [
                    'must' => [
                        'match_phrase' => ['tags' => Str::replaceFirst(self::FILTER_TAGS, '', $value)],
                    ],
                    'should' => $shouldCondition,
                ],
            ];

            if ($filterCondition) {
                $query['bool']['filter'] = $filterCondition;
            }
        } elseif (Str::startsWith($value, self::FILTER_DATE) && $this->validateDate(Str::replaceFirst(self::FILTER_DATE, '', $value))) {
            $datesStringUTC = $this->formatEventDates(Str::replaceFirst(self::FILTER_DATE, '', $value));

            $query = [
                'bool' => [
                    'must' => [
                        'match_all' => (object) [],
                    ],
                    'should' => $shouldCondition,
                    'filter' => \array_merge($filterCondition, [
                        [
                            'range' => [
                                'started_at' => [
                                    'lte' => Str::replaceFirst(self::FILTER_DATE, '', $datesStringUTC['start']),
                                ],
                            ],
                        ],
                        [
                            'range' => [
                                'ended_at' => [
                                    'gte' => Str::replaceFirst(self::FILTER_DATE, '', $datesStringUTC['end']),
                                ],
                            ],
                        ],
                    ]),
                ],
            ];
        }

        if (isset($query)) {
            return $this->elasticsearchService->searchFilterCustom($index, $size, $query, [], $from);
        }

        $mustCondition = [
            [
                'bool' => [
                    'should' => [
                        [
                            'match' => [
                                'title' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '1',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'tags' => [
                                    'query' => $value,
                                    'operator' => 'or',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '1',
                                    'boost' => 1,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'tags' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 2,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'description' => [
                                    'query' => $value,
                                    'operator' => 'and',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 0.5,
                                ],
                            ],
                        ],
                        [
                            'match' => [
                                'description' => [
                                    'query' => $value,
                                    'operator' => 'or',
                                    'auto_generate_synonyms_phrase_query' => false,
                                    'fuzzy_transpositions' => false,
                                    'fuzziness' => '0',
                                    'boost' => 0.1,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ];

        return $this->elasticsearchService->searchAdvancedCustom(
            $index,
            $size,
            $mustCondition,
            $filterCondition,
            $shouldCondition,
            $from
        );
    }

    public function validateDate($date)
    {
        try {
            $parsedDate = Date::createFromFormat('Y-m-d', $date);

            return $parsedDate && $parsedDate->format('Y-m-d') === $date;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function formatEventDates($date)
    {
        $parsedDateStart = Date::createFromFormat('Y-m-d', $date)->timezone('Europe/Paris');
        $parsedDateStart = $parsedDateStart->setHour(23);
        $parsedDateStart = $parsedDateStart->setMinute(59);
        $parsedDateStart = $parsedDateStart->setSecond(59);

        $parsedDateEnd = Date::createFromFormat('Y-m-d', $date)->timezone('Europe/Paris');
        $parsedDateEnd = $parsedDateEnd->setHour(0);
        $parsedDateEnd = $parsedDateEnd->setMinute(0);
        $parsedDateEnd = $parsedDateEnd->setSecond(0);

        return [
            'start' => $parsedDateStart->setTimezone('UTC')->format('Y-m-d H:i:s'),
            'end' => $parsedDateEnd->setTimezone('UTC')->format('Y-m-d H:i:s'),
        ];
    }
}

<?php

namespace App\Services\Elasticsearch;

use App\Models\News\NewsArticle;
use App\Models\Radio\RadioStation;
use App\Models\Users\User;

class NewsArticlesIndexService
{
    protected ElasticsearchService $elasticsearchService;

    protected string $elasticsearchIndex;

    public function __construct()
    {
        $this->elasticsearchService = app(ElasticsearchService::class);
        $this->elasticsearchIndex = config('services.elasticsearch.indexes.news_articles');
    }

    public function updateOrCreate(NewsArticle $newsArticle): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $newsArticle->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->updateDocument(
                $this->elasticsearchIndex,
                $esEntry['hits']['hits'][0]['_id'],
                $this->getNewsData($newsArticle)
            );

            return;
        }
        $this->elasticsearchService->indexDocument($this->elasticsearchIndex, $this->getNewsData($newsArticle));
    }

    public function delete(NewsArticle $newsArticle): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $newsArticle->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->deleteDocument($this->elasticsearchIndex, $esEntry['hits']['hits'][0]['_id']);
        }
    }

    protected function getNewsData(NewsArticle $newsArticle): array
    {
        $location = $newsArticle->location();

        return [
            'id' => $newsArticle->id,
            'audio_source_class' => $newsArticle::class,
            'title' => $newsArticle->title,
            'slug' => $newsArticle->slug,
            'tags' => $newsArticle->tags,
            'description' => $newsArticle->description,
            'published_at' => $newsArticle->published_at,
            'cover_card' => $newsArticle->getFirstMediaUrl('illustrations', 'small_card'),
            'thematic_id' => $newsArticle->thematic_id,
            'thematic_title' => $newsArticle->thematic?->title,
            'radio_stations' => $newsArticle->radioStations
                ->map(static fn (RadioStation $radio) => ['id' => $radio->id, 'name' => $radio->name]),
            'authors' => $newsArticle->author()->get()
                ->map(static fn (User $user) => ['id' => $user->id, 'username' => $user->username]),
            'location_id' => $location?->id,
            'location_type' => $location ? $location::class : null,
            'has_audio' => $newsArticle->has_audio,
            'active' => $newsArticle->active,
        ];
    }
}

<?php

namespace App\Services\Elasticsearch;

use App\Models\Audio\Song;

class SongsIndexService
{
    protected ElasticsearchService $elasticsearchService;

    protected string $elasticsearchIndex;

    public function __construct()
    {
        $this->elasticsearchService = app(ElasticsearchService::class);
        $this->elasticsearchIndex = config('services.elasticsearch.indexes.songs');
    }

    public function updateOrCreate(Song $song): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $song->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->updateDocument(
                $this->elasticsearchIndex,
                $esEntry['hits']['hits'][0]['_id'],
                $this->getSongData($song)
            );

            return;
        }
        $this->elasticsearchService->indexDocument($this->elasticsearchIndex, $this->getSongData($song));
    }

    public function delete(Song $song): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $song->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->deleteDocument($this->elasticsearchIndex, $esEntry['hits']['hits'][0]['_id']);
        }
    }

    protected function getSongData(Song $song): array
    {
        return [
            'id' => $song->id,
            'imedia' => $song->imedia,
            'audio_source_class' => $song::class,
            'cover_thumb' => $song->cover_thumb,
            'title' => $song->title,
            'performer_id' => $song->performerRelationship?->id,
            'performer_name' => $song->performerRelationship?->name ?: $song->performer,
            'version' => $song->version,
            'album_id' => $song->albumRelationship?->id,
            'album_name' => $song->album !== '0' ? $song->album : '',
            'year' => $song->yearFourDigit,
            'genre' => $song->genre !== '0' ? $song->genre : '',
            'label_id' => $song->labelRelationship?->id,
            'label_name' => $song->labelRelationship?->name ?: $song->publisher,
            'comment' => $song->comment,
            'human_readable_duration' => $song->human_readable_duration,
            'created_at' => $song->created_at,
            'hidden' => $song->imedia !== null && $song->category_plateforme !== Song::CATEGORY_PLATEFORME_MUSICS,
        ];
    }
}

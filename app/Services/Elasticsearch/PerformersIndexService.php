<?php

namespace App\Services\Elasticsearch;

use App\Models\Performers\Performer;

class PerformersIndexService
{
    protected ElasticsearchService $elasticsearchService;

    protected string $elasticsearchIndex;

    public function __construct()
    {
        $this->elasticsearchService = app(ElasticsearchService::class);
        $this->elasticsearchIndex = config('services.elasticsearch.indexes.performers');
    }

    public function updateOrCreate(Performer $performer): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $performer->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->updateDocument(
                $this->elasticsearchIndex,
                $esEntry['hits']['hits'][0]['_id'],
                $this->getPerformerData($performer)
            );

            return;
        }
        $this->elasticsearchService->indexDocument($this->elasticsearchIndex, $this->getPerformerData($performer));
    }

    public function delete(Performer $performer): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $performer->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->deleteDocument($this->elasticsearchIndex, $esEntry['hits']['hits'][0]['_id']);
        }
    }

    protected function getPerformerData(Performer $performer): array
    {
        $albums = [];
        foreach ($performer->albums()->with('contentLocations.location')->get() as $album) {
            $location = $album->location();

            $albums[] = [
                'id' => $album->id,
                'name' => $album->name,
                'location_type' => $location ? $location::class : null,
                'location_id' => $location?->id,
                'updated_at' => $album->updated_at?->toDateTimeString(),
                'created_at' => $album->created_at?->toDateTimeString(),
                'published_at' => $album->published_at?->toDateTimeString(),
            ];
        }

        $labels = [];
        foreach ($performer->labels()->with('contentLocations.location')->get() as $label) {
            $location = $label->location();

            $labels[] = [
                'id' => $label->id,
                'name' => $label->name,
                'location_type' => $location ? $location::class : null,
                'location_id' => $location?->id,
                'updated_at' => $label->updated_at?->toDateTimeString(),
                'created_at' => $label->created_at?->toDateTimeString(),
            ];
        }

        return [
            'id' => $performer->id,
            'thumb' => $performer->cover_thumb_or_album,
            'name' => $performer->name,
            'music_styles' => $performer->music_styles,
            'albums' => $albums,
            'labels' => $labels,
            'updated_at' => $performer->updated_at?->toDateTimeString(),
            'created_at' => $performer->created_at?->toDateTimeString(),
        ];
    }
}

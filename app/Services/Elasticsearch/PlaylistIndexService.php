<?php

namespace App\Services\Elasticsearch;

use App\Models\Audio\Playlist;
use App\Models\Audio\Song;
use App\Models\Radio\RadioStation;

class PlaylistIndexService
{
    protected ElasticsearchService $elasticsearchService;

    protected string $elasticsearchIndex;

    public function __construct()
    {
        $this->elasticsearchService = app(ElasticsearchService::class);
        $this->elasticsearchIndex = config('services.elasticsearch.indexes.playlists');
    }

    public function updateOrCreate(Playlist $playlist): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $playlist->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->updateDocument(
                $this->elasticsearchIndex,
                $esEntry['hits']['hits'][0]['_id'],
                $this->getPlaylistData($playlist)
            );

            return;
        }
        $this->elasticsearchService->indexDocument($this->elasticsearchIndex, $this->getPlaylistData($playlist));
    }

    public function delete(Playlist $playlist): void
    {
        $esEntry = $this->elasticsearchService->findByField($this->elasticsearchIndex, 'id', $playlist->id);
        if (data_get($esEntry, 'hits.hits.0')) {
            $this->elasticsearchService->deleteDocument($this->elasticsearchIndex, $esEntry['hits']['hits'][0]['_id']);
        }
    }

    protected function getPlaylistData(Playlist $playlist): array
    {
        $location = $playlist->location();

        return [
            'id' => $playlist->id,
            'audio_source_class' => $playlist::class,
            'cover_thumb' => $playlist->getFirstMediaUrl('cover', 'medium'),
            'title' => $playlist->title,
            'tags' => $playlist->tags,
            'user_id' => $playlist->user_id,
            'thematic_id' => $playlist->thematic_id,
            'thematic_title' => $playlist->thematic?->title,
            'radio_stations' => $playlist->radioStations
                ->map(static fn (RadioStation $radio) => ['id' => $radio->id, 'name' => $radio->name]),
            'songs_performers' => $playlist->songs->pluck('performer')->implode(', '),
            'songs_titles' => $playlist->songs->pluck('title')->implode(', '),
            'songs_cover_urls' => $playlist->songs->take(4)->map(static fn (Song $song) => $song->cover_thumb),
            'songs_count' => $playlist->songs->count(),
            'location_id' => $location?->id,
            'location_type' => $location ? $location::class : null,
            'published_at' => $playlist->published_at?->toDateTimeString(),
            'unpublished_at' => $playlist->unpublished_at?->toDateTimeString(),
            'active' => $playlist->active,
        ];
    }
}

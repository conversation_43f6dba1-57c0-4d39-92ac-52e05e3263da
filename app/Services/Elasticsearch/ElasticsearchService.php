<?php

namespace App\Services\Elasticsearch;

use Elastic\Elasticsearch\Client;
use Elastic\Elasticsearch\ClientBuilder;
use Illuminate\Support\Collection;

class ElasticsearchService
{
    protected Client $client;

    public function __construct()
    {
        $this->client = ClientBuilder::create()
            ->setHosts([config('services.elasticsearch.host') . ':' . config('services.elasticsearch.port')])
            ->setBasicAuthentication(
                config('services.elasticsearch.username'),
                config('services.elasticsearch.password')
            )
            ->build();
    }

    public function getClient(): Client
    {
        return $this->client;
    }

    public function createIndex(string $index, array $properties): void
    {
        $this->client->indices()->create([
            'index' => $index,
            'body' => [
                'settings' => [
                    'analysis' => [
                        'analyzer' => [
                            'default' => [
                                'tokenizer' => 'standard',
                                'filter' => [
                                    'lowercase',
                                    'asciifolding',
                                ],
                            ],
                            'tags_analyzer' => [
                                'tokenizer' => 'tags_tokenizer',
                                'filter' => [
                                    'lowercase',
                                    'asciifolding',
                                    'trim',
                                ],
                            ],
                        ],
                        'tokenizer' => [
                            'tags_tokenizer' => [
                                'type' => 'pattern',
                                'pattern' => ',',
                            ],
                        ],
                    ],
                ],
                'mappings' => [
                    'properties' => $properties,
                ],
            ],
        ]);
    }

    public function deleteIndex(string $index): void
    {
        if ($this->indexExists($index)) {
            $this->client->indices()->delete(['index' => $index]);
        }
    }

    public function indexExists(string $index): bool
    {
        return $this->client->indices()->exists(['index' => $index])->asBool();
    }

    public function bulkIndex(string $index, array $data): void
    {
        $body = [];
        foreach ($data as $item) {
            $body[] = ['index' => ['_index' => $index]];
            $body[] = $item;
        }
        if (count($body) > 0) {
            $this->client->bulk(compact('body'));
        }
    }

    public function clearIndex(string $index): void
    {
        $this->client->deleteByQuery([
            'index' => $index,
            'conflicts' => 'proceed',
            'body' => [
                'query' => [
                    'match_all' => (object) [],
                ],
            ],
        ]);
    }

    public function countIndex(string $index): int
    {
        return $this->client->count(['index' => $index])['count'];
    }

    public function all(string $index, int $size = 1000, array $fields = []): Collection
    {
        $items = collect();
        $scrollResponse = $this->client->search([
            'index' => $index,
            'scroll' => '10s', // Time before scroll timeout
            'size' => $size,
            'body' => [
                'query' => ['match_all' => ['boost' => 1.0]],
                'fields' => $fields,
                '_source' => false,
            ],
        ])->asArray();

        return $this->scrollAppend($scrollResponse, $items);
    }

    public function searchScroll(string $scrollId, string $scrollTimeout): array
    {
        return $this->client->scroll([
            'body' => [
                'scroll_id' => $scrollId,
                'scroll' => $scrollTimeout,
            ],
        ])->asArray();
    }

    protected function scrollAppend(array $scrollResponse, Collection $items): Collection
    {
        if (! isset($scrollResponse['hits']['hits']) || count($scrollResponse['hits']['hits']) === 0) {
            return $items;
        }
        $newScrollResponse = $this->searchScroll($scrollResponse['_scroll_id'], '10s');

        return $this->scrollAppend($newScrollResponse, $items->merge(collect($scrollResponse['hits']['hits'])));
    }

    public function searchFuzzyByFields(
        string $index,
        string $value,
        array $fields,
        int $size,
        ?string $scrollTimeout = null,
        ?array $pluckFields = null,
        int $from = 0
    ): array {
        return $this->client->search(array_filter([
            'index' => $index,
            'scroll' => $scrollTimeout,
            'body' => array_filter([
                'size' => $size,
                'from' => $from,
                'query' => [
                    'multi_match' => [
                        'query' => $value,
                        'fields' => $fields,
                        'fuzziness' => '1',
                    ],
                ],
                'fields' => $pluckFields,
            ]),
            '_source' => $pluckFields ? false : null,
        ]))->asArray();
    }

    public function searchFuzzyByFieldsWithCondition(
        string $index,
        string $value,
        array $fields,
        int $size,
        array $filterCondition,
        array $shouldCondition = [],
        ?array $pluckFields = null,
        int $from = 0
    ): array {
        return $this->client->search(array_filter([
            'index' => $index,
            'body' => array_filter([
                'size' => $size,
                'from' => $from,
                'query' => [
                    'bool' => [
                        'must' => [
                            'multi_match' => [
                                'query' => $value,
                                'fields' => $fields,
                                'fuzziness' => '1',
                            ],
                        ],
                        'filter' => $filterCondition,
                        'should' => $shouldCondition,
                    ],
                ],
                'fields' => $pluckFields,
            ]),
            '_source' => $pluckFields ? false : null,
        ]))->asArray();
    }

    public function findByField(string $index, string $field, mixed $value, int $size = 5): array
    {
        return $this->client->search([
            'index' => $index,
            'body' => [
                'size' => $size,
                'query' => [
                    'term' => [
                        $field => [
                            'value' => $value,
                            'boost' => 1.0,
                        ],
                    ],
                ],
            ],
        ])->asArray();
    }

    public function searchAdvancedCustom(
        string $index,
        int $size,
        array $mustCondition = [],
        array $filterCondition = [],
        array $shouldCondition = [],
        int $from = 0,
        ?array $pluckFields = null,
        int|string|null $minimumShouldMatch = null,
    ): array {
        $params = [
            'index' => $index,
            'body' => array_filter([
                'size' => $size,
                'from' => $from,
                'query' => [
                    'bool' => [
                        'must' => $mustCondition,
                        'filter' => $filterCondition,
                        'should' => $shouldCondition,
                    ],
                ],
                'fields' => $pluckFields,
            ]),
            '_source' => $pluckFields ? false : null,
        ];

        if ($minimumShouldMatch !== null) {
            $params['body']['query']['bool']['minimum_should_match'] = $minimumShouldMatch;
        }

        return $this->client->search(array_filter($params))->asArray();
    }

    public function searchFilterCustom(
        string $index,
        int $size,
        array $query,
        array $sort,
        int $from = 0,
    ): array {
        return $this->client->search(array_filter([
            'index' => $index,
            'body' => array_filter([
                'size' => $size,
                'from' => $from,
                'query' => $query,
                'sort' => $sort,
            ]),
        ]))->asArray();
    }

    public function searchSimpleById(
        string $index,
        int $id
    ): array {
        return $this->client->search(array_filter([
            'index' => $index,
            'body' => array_filter([
                'query' => [
                    'term' => [
                        'id' => [
                            'value' => $id,
                            'boost' => '1.0',
                        ],
                    ],
                ],
                'size' => 1,
            ]),
        ]))->asArray();
    }

    public function searchMoreLikeThis(
        string $esSourceContentIndex,
        string $esSourceContentId,
        array $fields,
        ?string $searchIndex = null,
        int $minTermFreq = 1,
        int $maxQueryTerms = 12,
    ): array {
        return $this->client->search(array_filter([
            'index' => $searchIndex,
            'body' => array_filter([
                'query' => [
                    'more_like_this' => [
                        'fields' => $fields,
                        'like' => [
                            '_index' => $esSourceContentIndex,
                            '_id' => $esSourceContentId,
                        ],
                        'min_term_freq' => $minTermFreq,
                        'max_query_terms' => $maxQueryTerms,
                    ],
                ],
            ]),
        ]))->asArray();
    }

    public function searchMoreLikeThisAdvanced(
        string $esSourceContentIndex,
        string $esSourceContentId,
        array $fields,
        ?string $searchIndex = null,
        array $shouldCondition = [],
        array $filterCondition = [],
        int $size = 10,
        int $minTermFreq = 1,
        int $maxQueryTerms = 12,
    ): array {
        return $this->client->search(array_filter([
            'index' => $searchIndex,
            'body' => array_filter([
                'query' => [
                    'bool' => [
                        'must' => [
                            'more_like_this' => [
                                'fields' => $fields,
                                'like' => [
                                    '_index' => $esSourceContentIndex,
                                    '_id' => $esSourceContentId,
                                ],
                                'min_term_freq' => $minTermFreq,
                                'max_query_terms' => $maxQueryTerms,
                            ],
                        ],
                        'should' => $shouldCondition,
                        'filter' => $filterCondition,
                    ],
                ],
                'size' => $size,
            ]),
        ]))->asArray();
    }

    public function indexDocument(string $index, array $data): array
    {
        return $this->client->index(['index' => $index, 'body' => $data])->asArray();
    }

    public function updateDocument(string $index, string $docId, array $data): array
    {
        return $this->client->update(['index' => $index, 'id' => $docId, 'body' => ['doc' => $data]])->asArray();
    }

    public function deleteDocument(string $index, string $docId): void
    {
        $this->client->delete(['index' => $index, 'id' => $docId]);
    }
}

<?php

use App\Models\Audio\Thematic;
use App\Models\Cookies\CookieCategory;
use App\Models\Pages\Page;
use App\Models\Radio\Program;
use App\Models\Radio\RadioStation;
use App\Models\Settings\Settings;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection as EloquentCollection;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

if (! function_exists('settings')) {
    /** @throws \Exception */
    function settings(bool $clearCache = false): Settings
    {
        if ($clearCache) {
            Cache::forget('settings');
        }

        return Cache::rememberForever('settings', static fn () => Settings::with(['media'])->sole());
    }
}

if (! function_exists('pages')) {
    /** @throws \Exception */
    function pages(bool $clearCache = false): Collection
    {
        if ($clearCache) {
            Cache::forget('pages');
        }

        return Cache::rememberForever('pages', static fn () => Page::where('active', true)->get());
    }
}

if (! function_exists('cookieCategories')) {
    /** @throws \Exception */
    function cookieCategories(bool $clearCache = false): Collection
    {
        if ($clearCache) {
            Cache::forget('cookie_categories');
        }

        return Cache::rememberForever(
            'cookie_categories',
            static fn () => CookieCategory::with([
                'services' => fn (BelongsToMany $services) => $services->where('active', true)->with(['categories']),
            ])->whereHas('services')->ordered()->get()
        );
    }
}

if (! function_exists('radioStations')) {
    /** @return EloquentCollection<int, RadioStation> */
    function radioStations(bool $clearCache = false): EloquentCollection
    {
        if ($clearCache) {
            Cache::forget('radio_stations');
        }

        return Cache::rememberForever(
            'radio_stations',
            static fn () => RadioStation::with(['contentLocations', 'media'])
                ->where('active', true)
                ->ordered()
                ->get()
        );
    }
}

if (! function_exists('lastRadioStationsLiveBroadcasts')) {
    /** @throws \Exception */
    function lastRadioStationsLiveBroadcasts(bool $clearCache = false): Collection
    {
        if ($clearCache) {
            Cache::forget('last_radio_stations_live_broadcastings');
        }

        return Cache::rememberForever('last_radio_stations_live_broadcastings', static function () {
            $lastRadioStationsLiveBroadcastings = collect();
            $radioStations = RadioStation::with([
                'latestBroadcast',
                'latestBroadcast.dedicationUser.media',
                'latestBroadcast.song.performerRelationship',
                'latestBroadcast.song.albumRelationship',
                'latestBroadcast.song.labelRelationship',
                'latestBroadcast.song.favoriteUsers',
                'latestBroadcast.podcast.program',
            ])
                ->where('active', true)
                ->get();
            foreach ($radioStations as $radioStation) {
                $broadcast = $radioStation->latestBroadcast;
                if (! $broadcast) {
                    continue;
                }
                $lastRadioStationsLiveBroadcastings->add($broadcast);
            }

            return $lastRadioStationsLiveBroadcastings;
        });
    }
}

if (! function_exists('programs')) {
    /** @throws \Exception */
    function programs(bool $clearCache = false): Collection
    {
        if ($clearCache) {
            Cache::forget('programs');
        }

        return Cache::rememberForever(
            'programs',
            static fn () => Program::with(['media', 'recurrences'])->get()
        );
    }
}

if (! function_exists('thematics')) {
    /** @throws \Exception */
    function thematics(bool $clearCache = false): Collection
    {
        if ($clearCache) {
            Cache::forget('thematics');
        }

        return Cache::rememberForever(
            'thematics',
            static fn () => Thematic::with(['media'])->get()
        );
    }
}

if (! function_exists('browseNewsArticlesByStation')) {
    /** @throws \Exception */
    function browseNewsArticlesByStation(int $radioStationId, bool $clearCache = false): Collection
    {
        $cacheKey = "browse_news_articles_station_{$radioStationId}";
        $cacheExpiration = now()->addHours(1);

        if ($clearCache) {
            Cache::forget($cacheKey);
        }

        // Récupérer tous les articles potentiellement affichables (sans filtre de date)
        $allArticles = Cache::remember(
            $cacheKey,
            $cacheExpiration,
            static function () use ($radioStationId, $cacheExpiration) {
                return \App\Models\News\NewsArticle::with(['media'])
                    ->where('active', true)
                    // Pré-filtrage avec une marge par rapport au cache
                    ->where('published_at', '<=', $cacheExpiration->copy()->addHour())
                    ->where(function (\Illuminate\Database\Eloquent\Builder $subWhereQuery) use ($radioStationId) {
                        $subWhereQuery->whereRelation(
                            'radioStations',
                            fn (\Illuminate\Database\Eloquent\Builder $radioStationQuery) => $radioStationQuery->where('id', $radioStationId)
                        )->orWhereDoesntHave('radioStations');
                    })
                    ->orderBy('published_at', 'desc')
                    // Prendre plus d'articles pour avoir une marge
                    ->take(10)
                    ->get();
            }
        );

        // Appliquer le filtre de date après récupération du cache
        return $allArticles
            ->filter(function ($article) {
                return $article->published_at <= now();
            })
            ->take(7);
    }
}

if (! function_exists('browseEventsByStation')) {
    /** @throws \Exception */
    function browseEventsByStation(int $radioStationId, bool $clearCache = false): Collection
    {
        $cacheKey = "browse_events_station_{$radioStationId}";
        $cacheExpiration = now()->addHours(1);

        if ($clearCache) {
            Cache::forget($cacheKey);
        }

        // Récupérer tous les évènements potentiellement affichables (sans filtre de date)
        $allEvents = Cache::remember(
            $cacheKey,
            $cacheExpiration,
            static function () use ($radioStationId) {
                return \App\Models\Events\Event::with(['media'])
                    ->where(function (Builder $subWhereQuery) use ($radioStationId) {
                        $subWhereQuery->whereRelation(
                            'radioStations',
                            fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $radioStationId)
                        )->orWhereDoesntHave('radioStations');
                    })
                    ->where('active', true)
                    // Pré-filtrage avec une marge d'un jour supplémentaire
                    ->where('ended_at', '>=', now()->subDays(2)->startOfDay())
                    ->orderByRaw(config('database.default') === 'pgsql'
                        ? 'ABS((started_at AT TIME ZONE \'UTC\' AT TIME ZONE \'Europe/Paris\')::date - (now() AT TIME ZONE \'Europe/Paris\')::date)'
                        : 'ABS(DATEDIFF(started_at, NOW()))')
                    ->orderBy('started_at')
                    // Prendre plus d'évènements pour avoir une marge
                    ->limit(8)
                    ->get();
            }
        );

        // Appliquer le filtre de date après récupération du cache
        return $allEvents
            ->filter(function ($event) {
                return $event->ended_at >= now()->subDay()->startOfDay();
            })
            ->take(3);
    }
}

if (! function_exists('browsePodcastsByStation')) {
    /** @throws \Exception */
    function browsePodcastsByStation(int $radioStationId, bool $clearCache = false): Collection
    {
        $cacheKey = "browse_podcasts_station_{$radioStationId}";
        $cacheExpiration = now()->addHours(1);

        if ($clearCache) {
            Cache::forget($cacheKey);
        }

        // Récupérer tous les podcasts potentiellement affichables (sans filtre de date)
        $allPodcasts = Cache::remember(
            $cacheKey,
            $cacheExpiration,
            static function () use ($radioStationId, $cacheExpiration) {
                return \App\Models\Audio\Podcast::hasAudio()
                    ->with(['media', 'program'])
                    ->where('active', true)
                    // Pré-filtrage avec une marge par rapport au cache
                    ->where('published_at', '<=', $cacheExpiration->copy()->addHour())
                    ->where(function (Builder $subWhereQuery) use ($radioStationId) {
                        $subWhereQuery->whereRelation(
                            'radioStations',
                            fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $radioStationId)
                        )->orWhereDoesntHave('radioStations');
                    })
                    // Prendre plus de podcasts pour avoir une marge
                    ->limit(10)
                    ->get();
            }
        );

        // Appliquer le filtre de date après récupération du cache
        return $allPodcasts
            ->filter(function ($podcast) {
                return $podcast->published_at <= now();
            })
            ->take(7);
    }

    if (! function_exists('browsePlaylistsByStation')) {
        /** @throws \Exception */
        function browsePlaylistsByStation(int $radioStationId, bool $clearCache = false): Collection
        {
            $cacheKey = "browse_playlists_station_{$radioStationId}";
            $cacheExpiration = now()->addHours(1);

            if ($clearCache) {
                Cache::forget($cacheKey);
            }

            // Récupérer toutes les playlists potentiellement affichables
            $allPlaylists = Cache::remember(
                $cacheKey,
                $cacheExpiration,
                static function () use ($radioStationId, $cacheExpiration) {
                    return \App\Models\Audio\Playlist::with(['media', 'thematic', 'songs'])
                        ->where('user_id', null)
                        ->where(function (Builder $subWhereQuery) use ($radioStationId) {
                            $subWhereQuery->whereRelation(
                                'radioStations',
                                fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $radioStationId)
                            )->orWhereDoesntHave('radioStations');
                        })
                        ->where('active', true)
                        // Pré-filtrage avec une marge par rapport au cache
                        ->where('published_at', '<=', $cacheExpiration->copy()->addHour())
                        ->where(function (Builder $subWhereQuery) use ($cacheExpiration) {
                            $subWhereQuery->whereNull('unpublished_at')
                                ->orWhere('unpublished_at', '>', $cacheExpiration);
                        })
                        ->latest('published_at')
                        ->limit(10)
                        ->get();
                }
            );

            // Appliquer le filtre de date après récupération du cache
            return $allPlaylists
                ->filter(function ($playlist) {
                    return $playlist->published_at <= now()
                        && ($playlist->unpublished_at === null || $playlist->unpublished_at > now());
                })
                ->take(7);
        }
    }

    if (! function_exists('browseAnnouncementsByStation')) {
        /** @throws \Exception */
        function browseAnnouncementsByStation(int $radioStationId, bool $clearCache = false): Collection
        {
            $cacheKey = "browse_announcements_station_{$radioStationId}";
            $cacheExpiration = now()->addHours(1);

            if ($clearCache) {
                Cache::forget($cacheKey);
            }

            // Récupérer toutes les annonces potentiellement affichables
            $allAnnouncements = Cache::remember(
                $cacheKey,
                $cacheExpiration,
                static function () use ($radioStationId, $cacheExpiration) {
                    return \App\Models\Announcements\Announcement::with('announceable')
                        ->where(function (Builder $subWhereQuery) use ($radioStationId) {
                            $subWhereQuery->whereRelation(
                                'radioStations',
                                fn (Builder $radioStationQuery) => $radioStationQuery->where('id', $radioStationId)
                            )->orWhereDoesntHave('radioStations');
                        })
                        ->where('active', true)
                        ->where('published_at', '<=', $cacheExpiration->copy()->addHour())
                        ->where(function (Builder $subWhereQuery) use ($cacheExpiration) {
                            $subWhereQuery->whereNull('unpublished_at')
                                ->orWhere('unpublished_at', '>', $cacheExpiration);
                        })
                        ->latest('published_at')
                        ->limit(12)
                        ->get();
                }
            );

            // Appliquer le filtre de date après récupération du cache
            return $allAnnouncements
                ->filter(function ($announcement) {
                    return $announcement->published_at <= now()
                        && ($announcement->unpublished_at === null || $announcement->unpublished_at > now());
                })
                ->take(8);
        }
    }

    if (! function_exists('browseSongsByStation')) {
        /** @throws \Exception */
        function browseSongsByStation(int $radioStationWinmediaId, bool $clearCache = false): Collection
        {
            $cacheKey = "browse_songs_station_{$radioStationWinmediaId}";
            $cacheExpiration = now()->addHours(1);

            if ($clearCache) {
                Cache::forget($cacheKey);
            }

            return Cache::remember(
                $cacheKey,
                $cacheExpiration,
                static function () use ($radioStationWinmediaId) {
                    return \App\Models\Audio\Song::musicWinmediaType()
                        ->with(['performerRelationship', 'albumRelationship'])
                        ->where(
                            '_station',
                            config('database.default') === 'pgsql' ? 'ILIKE' : 'LIKE',
                            '%/' . $radioStationWinmediaId . '/%'
                        )
                        ->news()
                        ->orderByDesc('imedia')
                        ->limit(12)
                        ->get();
                }
            );
        }
    }
}

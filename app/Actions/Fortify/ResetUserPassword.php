<?php

namespace App\Actions\Fortify;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;
use Laravel\Fortify\Contracts\ResetsUserPasswords;

class ResetUserPassword implements ResetsUserPasswords
{
    /** @throws \Illuminate\Validation\ValidationException */
    public function reset($user, array $input): void
    {
        Validator::make($input, ['password' => ['required', Password::defaults()]])->validate();
        $user->forceFill(['password' => Hash::make($input['password'])])->save();
    }
}

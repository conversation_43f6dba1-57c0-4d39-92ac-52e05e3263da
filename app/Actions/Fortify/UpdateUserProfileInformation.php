<?php

namespace App\Actions\Fortify;

use App\Models\Users\User;
use App\Services\Users\UsersService;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Lara<PERSON>\Fortify\Contracts\UpdatesUserProfileInformation;

class UpdateUserProfileInformation implements UpdatesUserProfileInformation
{
    /**
     * @throws \Illuminate\Validation\ValidationException
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     * @throws \Okipa\MediaLibraryExt\Exceptions\CollectionNotFound
     */
    public function update(mixed $user, array $input): void
    {
        $input = array_merge($input, ['remove_profile_picture' => (bool) data_get($input, 'remove_profile_picture')]);
        Validator::make($input, $this->rules($user))->validateWithBag('update_profile_information');
        if ($input['email'] !== $user->email && $user instanceof MustVerifyEmail) {
            $this->updateVerifiedUser($user, $input);
        } else {
            $user->update([
                'username' => $input['username'],
                'email' => $input['email'],
                'birth_date' => data_get($input, 'birth_date'),
                'address' => data_get($input, 'address'),
                'city' => data_get($input, 'city'),
            ]);
            toast()->success('Vos informations de profil ont été mises à jour.');
        }
        $profilePicture = data_get($input, 'profile_picture');
        $removeProfilePicture = (bool) $input['remove_profile_picture'];
        if ($profilePicture || $removeProfilePicture) {
            $uploadedFiled = $removeProfilePicture ? null : $profilePicture;
            app(UsersService::class)->saveAvatarFromUploadedFile($uploadedFiled, $user);
        }
    }

    /** @throws \Okipa\MediaLibraryExt\Exceptions\CollectionNotFound */
    public function rules(User $user, bool $withProfilePictureRules = true): array
    {
        return array_merge(
            $withProfilePictureRules ? [
                'profile_picture' => array_merge(
                    ['nullable'],
                    app(User::class)->getMediaValidationRules('profile_picture')
                ),
                'remove_profile_picture' => ['required', 'boolean'],
            ] : [],
            [
                'username' => ['required', 'string', 'max:255'],
                'email' => [
                    'required',
                    'string',
                    'max:255',
                    'email:rfc,dns,spoof',
                    Rule::unique('users')->ignore($user),
                ],
                'birth_date' => ['nullable', 'date', 'before:today'],
                'address' => ['nullable', 'string', 'max:255'],
                'city' => ['nullable', 'string', 'max:255'],
            ]
        );
    }

    protected function updateVerifiedUser(mixed $user, array $input): void
    {
        $user->forceFill([
            'username' => $input['username'],
            'email' => $input['email'],
            'birth_date' => data_get($input, 'birth_date'),
            'address' => data_get($input, 'address'),
            'city' => data_get($input, 'city'),
            'email_verified_at' => null,
        ])->save();
        $user->sendEmailVerificationNotification();
    }
}

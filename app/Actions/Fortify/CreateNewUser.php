<?php

namespace App\Actions\Fortify;

use App\Models\Teams\Team;
use App\Models\Users\User;
use App\Services\Users\UsersService;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use Laravel\Fortify\Contracts\CreatesNewUsers;

class CreateNewUser implements CreatesNewUsers
{
    /**
     * @throws \Illuminate\Validation\ValidationException
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileDoesNotExist
     * @throws \Spatie\MediaLibrary\MediaCollections\Exceptions\FileIsTooBig
     */
    public function create(array $input): User
    {
        Validator::make($input, $this->rules())->validate();
        $user = User::create([
            'team_id' => Team::where('unique_key', 'listener')->sole()->id,
            'username' => $input['username'],
            'email' => $input['email'],
            'password' => Hash::make($input['password']),
        ]);
        app(UsersService::class)->saveAvatarFromUploadedFile(null, $user);
        $user->sendEmailVerificationNotification();

        return $user;
    }

    public function rules(): array
    {
        return [
            'username' => ['required', 'string', 'max:255', Rule::unique(User::class)],
            'email' => ['required', 'string', 'max:255', 'email:rfc,dns,spoof', Rule::unique(User::class)],
            'password' => ['required', Password::default()],
        ];
    }
}

<?php

namespace App\Actions\Fortify;

use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rules\Password;
use <PERSON>vel\Fortify\Contracts\UpdatesUserPasswords;

class UpdateUserPassword implements UpdatesUserPasswords
{
    /** @throws \Illuminate\Validation\ValidationException */
    public function update($user, array $input): void
    {
        Validator::make($input, [
            'current_password' => ['required', 'string'],
            'new_password' => ['required', Password::defaults(), 'confirmed'],
        ])->after(function ($validator) use ($user, $input) {
            if (! isset($input['current_password']) || ! Hash::check($input['current_password'], $user->password)) {
                $validator->errors()
                    ->add('current_password', 'Le mot de passe fourni ne correspond pas à votre mot de passe actuel.');
            }
        })->validateWithBag('update_password');
        $user->forceFill(['password' => Hash::make($input['new_password'])])->save();
        toast()->success(__('Your new password has been saved.'));
    }
}

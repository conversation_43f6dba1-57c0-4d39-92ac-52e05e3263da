<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Facades\Storage;

class ValidFileUpload implements Rule
{
    public function __construct(private readonly array $validMimeTypes)
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (! Storage::exists($value)) {
            return false;
        }

        if (! in_array(Storage::mimeType($value), $this->validMimeTypes, true)) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The file is not valid.';
    }
}

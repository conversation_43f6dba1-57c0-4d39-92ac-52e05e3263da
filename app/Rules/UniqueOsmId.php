<?php

namespace App\Rules;

use App\Models\Map\Place;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueOsmId implements ValidationRule
{
    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        $existingPlace = Place::whereRaw('LOWER(source_id) = LOWER(?)', [$value])
            ->where('source_name', 'openstreetmap')
            ->first();

        if ($existingPlace) {
            $fail(__('A place :name already exists with this OpenStreetMap ID.', [
                'name' => '"' . $existingPlace->name . '"',
            ]));
        }
    }
}

<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class InJson implements Rule
{
    protected string $rule = 'in_json';

    protected array $values;

    public function __construct(array $values)
    {
        $this->values = $values;
    }

    /** @throws \JsonException */
    public function passes($attribute, $value): bool
    {
        $json = json_decode($value, true, 512, JSON_THROW_ON_ERROR);

        return count(array_intersect($json, $this->values)) === count($json);
    }

    public function message(): string
    {
        return 'Le champs :attribute is invalid';
    }
}

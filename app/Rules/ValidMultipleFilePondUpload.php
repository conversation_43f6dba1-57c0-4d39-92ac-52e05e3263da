<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ValidMultipleFilePondUpload implements Rule
{
    private $validFileUploadRule;

    public function __construct(
        private readonly array $validMimeTypes,
        private readonly ?int $maxFileSize = 0)
    {
        $this->validFileUploadRule = new ValidFilePondUpload($this->validMimeTypes, $this->maxFileSize);
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        foreach ($value as $file) {
            if (! $file) {
                return false;
            }
            if (! $this->validFileUploadRule->passes($attribute, $file)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'One or more files are not valid.';
    }
}

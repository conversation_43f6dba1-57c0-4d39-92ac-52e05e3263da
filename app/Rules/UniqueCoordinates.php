<?php

namespace App\Rules;

use App\Models\Map\Place;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueCoordinates implements DataAwareRule, ValidationRule
{
    protected array $data = [];

    public function setData($data): self
    {
        $this->data = $data;

        return $this;
    }

    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        if (! isset($this->data['latitude']) || ! isset($this->data['longitude'])) {
            $fail('Both latitude and longitude are required');

            return;
        }

        $existingPlace = Place::whereHas('points', function ($query) {
            $query->whereRaw('ST_DWithin(
                coord::geography,
                ST_SetSRID(ST_MakePoint(?, ?), 4326)::geography,
                1
            )', [$this->data['longitude'], $this->data['latitude']]);
        })->first();

        if ($existingPlace) {
            $fail(__('A place :name already exists at these coordinates.', [
                'name' => '"' . $existingPlace->name . '"',
            ]));
        }
    }
}

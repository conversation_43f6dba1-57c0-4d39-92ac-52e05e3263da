<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class TagsLength implements Rule
{
    public function __construct(protected int $maxNumberOfCharacters)
    {
        //
    }

    public function passes($attribute, $value): bool
    {
        return strlen(implode(', ', $value)) <= $this->maxNumberOfCharacters;
    }

    public function message(): string
    {
        return __('validation.max.string', ['max' => $this->maxNumberOfCharacters]);
    }
}

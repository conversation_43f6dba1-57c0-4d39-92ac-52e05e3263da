<?php

namespace App\Rules;

use App\Models\Audio\Song;
use Illuminate\Contracts\Validation\Rule;

class ValidUniquePerformerTitleVersion implements Rule
{
    public function __construct(
        private readonly ?string $performer,
        private readonly ?string $title,
        private readonly ?string $version)
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        $exist = Song::where('performer', $this->performer)
            ->where('title', $this->title)
            ->where('version', $this->version)
            ->where('id', '!=', $value)
            ->exists();

        return $exist ? false : true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'Il existe déjà un titre avec les mêmes champs Interprète / Titre / Version';
    }
}

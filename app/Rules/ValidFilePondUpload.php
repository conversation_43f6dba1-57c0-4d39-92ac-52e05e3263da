<?php

namespace App\Rules;

use Illuminate\Contracts\Validation\Rule;

class ValidFilePondUpload implements Rule
{
    public function __construct(
        private readonly array $validMimeTypes,
        private readonly ?int $maxFileSize = 0)
    {
        //
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (! $value->exists()) {
            return false;
        }

        if (! in_array($value->getMimeType(), $this->validMimeTypes, true)) {
            return false;
        }

        if ($this->maxFileSize > 0 && ($value->getSize() > $this->maxFileSize)) {
            return false;
        }

        return true;
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return 'The file is not valid.';
    }
}

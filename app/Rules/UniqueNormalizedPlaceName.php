<?php

namespace App\Rules;

use App\Models\Map\Place;
use Illuminate\Contracts\Validation\ValidationRule;

class UniqueNormalizedPlaceName implements ValidationRule
{
    public function validate(string $attribute, mixed $value, \Closure $fail): void
    {
        $exists = config('database.default') === 'pgsql'
            ? Place::whereRaw('LOWER(UNACCENT(name)) = LOWER(UNACCENT(?))', [$value])->exists()
            : Place::whereRaw('LOWER(name) = LOWER(?)', [$value])->exists();

        if ($exists) {
            $fail(__('A place with this name already exists.'));
        }
    }
}

<?php

/*** @see https://github.com/artesaos/seotools */

$defaultTitle = env('APP_NAME', 'Laravel');
$defaultDescription = 'SUN est une station de radio diffusée sur le 93 FM à Nantes, le 87.7 FM à Cholet et en DAB+ à Nantes, Saint-Nazaire, Angers et la Roche-sur-Yon.';
$defaultImages = [env('APP_URL', 'http://localhost') . '/images/pwa/icon-512x512.png'];

return [

    'meta' => [

        /*
         * The default configurations to be used by the meta generator.
         */
        'defaults' => [
            'title' => $defaultTitle, // set false to total remove
            'titleBefore' => false, // Put defaults.title before page title, like 'It's Over 9000! - Dashboard'
            'description' => $defaultDescription, // set false to total remove
            'separator' => ' - ',
            'keywords' => [],
            'canonical' => 'full', // Set to null or 'full' to use Url::full(), set to 'current' to use Url::current(), set false to total remove
            'robots' => env('APP_ENV') !== 'production'
                ? 'none'
                : 'index, follow, max-snippet:-1, max-image-preview:standard', // Set to 'all', 'none' or any combination of index/noindex and follow/nofollow
        ],

        /*
         * Webmaster tags are always added.
         */
        'webmaster_tags' => [
            'google' => null, // https://support.google.com/webmasters/answer/79812
            'bing' => null, // https://www.bing.com/webmaster/help/how-to-verify-ownership-of-your-site-afcfefc6
            'alexa' => null,
            'pinterest' => null, // https://help.pinterest.com/fr/articles/claim-your-website
            'yandex' => null, // https://yandex.com/support/webmaster/adding-site/how-to-add-site.html
            'norton' => null, // https://support.norton.com/sp/en/in/home/<USER>/solutions/kb20090410134005EN
        ],

        'add_notranslate_class' => false,

    ],

    'opengraph' => [

        /*
         * The default configurations to be used by the opengraph generator.
         */
        'defaults' => [
            'title' => $defaultTitle, // set false to total remove
            'description' => $defaultDescription, // set false to total remove
            'url' => null, // Set null for using Url::current(), set false to total remove
            'type' => 'article', // https://ogp.me/#types
            'site_name' => false,
            'images' => $defaultImages,
        ],
    ],

    'twitter' => [

        /*
         * The default values to be used by the twitter cards generator.
         */
        'defaults' => [
            'card' => 'summary_large_image', // Possible values : summary, summary_large_image, app, player
            'site' => '@LeSonUnique',
        ],
    ],

    'json-ld' => [

        /*
         * The default configurations to be used by the json-ld generator.
         */
        'defaults' => [
            'title' => $defaultTitle, // set false to total remove
            'description' => $defaultDescription, // set false to total remove
            'url' => null, // Set null for using Url::current(), set false to total remove
            'type' => 'WebPage',
            'images' => $defaultImages,
        ],
    ],

];

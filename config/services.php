<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | default location for this type of information, allowing packages
    | to have a conventional place to find your various credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'google' => [
        'key' => env('GOOGLE_API_KEY'),
    ],

    'elasticsearch' => [
        'host' => env('ELASTICSEARCH_HOST', 'localhost'),
        'port' => env('ELASTICSEARCH_PORT', '9200'),
        'username' => env('ELASTICSEARCH_USERNAME'),
        'password' => env('ELASTICSEARCH_PASSWORD'),
        'indexes' => [
            'songs' => env('ELASTICSEARCH_INDEX_SONGS'),
            'podcasts' => env('ELASTICSEARCH_INDEX_PODCASTS'),
            'events' => env('ELASTICSEARCH_INDEX_EVENTS'),
            'news_articles' => env('ELASTICSEARCH_INDEX_NEWS_ARTICLES'),
            'playlists' => env('ELASTICSEARCH_INDEX_PLAYLISTS'),
            'performers' => env('ELASTICSEARCH_INDEX_PERFORMERS'),
        ],
    ],

    'dedication' => [
        'endpoint' => env('DEDICATION_ENDPOINT'),
        'verification_user_eligibility_path' => env(
            'DEDICATION_VERIFICATION_USER_ELIGIBILITY_PATH',
            'verifier_utilisateur.php'
        ),
        'verify_songs_eligibility_path' => env(
            'DEDICATION_VERIFY_SONGS_ELIGIBILITY_PATH',
            'recherche_creation_tableau.php'
        ),
        'timestamp_slots_path' => env('DEDICATION_TIMESTAMP_SLOTS_PATH', 'recuperation_creneaux_morceau.php'),
        'validation_path' => env('DEDICATION_VALIDATION_PATH', 'validation.php'),
    ],

    'streaming' => [
        'url' => 'https://aod.lesonunique.com',
    ],

];
